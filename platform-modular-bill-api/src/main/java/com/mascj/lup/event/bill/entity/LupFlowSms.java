package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 网格数据表
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_flow_sms")
public class LupFlowSms extends BaseEntity {

    /**
     * 是否已删除
     */
    private Integer deleted;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 短信接收用户ID
     */
    private Long userId;

    /**
     * 类型：1 事件处理；2 事件驳回；
     */
    private Integer type;

    /**
     * 是否实时发送：0否 1是
     */
    private Integer timer;

    /**
     * 是否提交给短信发送 0否 1是
     */
    private Integer state;

    /**
     * 短信类型编码
     */
    private String smsCode;

    /**
     * 短信内容唯一标识 用于定时发数据统一调度分组的
     */
    private String contentUuid;

    /**
     * 短信内容
     */
    private String smsContent;

}
