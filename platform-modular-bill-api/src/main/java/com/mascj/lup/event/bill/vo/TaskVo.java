package com.mascj.lup.event.bill.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@ApiModel("任务详情")
public class TaskVo {

    @ApiModelProperty(value = "任务Id")
    private String Id;
    /**
     *
     */
    @ApiModelProperty(value = "任务名称")
    private String name;

    /**
     *
     */
    @ApiModelProperty(value = "办理人")
    private String assignee;

    /**
     *
     */
    @ApiModelProperty(value = "taskKey")
    private String taskDefinitionKey;

    /**
     *
     */
    @ApiModelProperty(value = "流程实例Id")
    private String processInstanceId;

    /**
     *
     */
    @ApiModelProperty(value = "任务开始时间")
    private Date createTime;

    /**
     *
     */
    @ApiModelProperty(value = "任务结束时间")
    private Date endTime;

    /**
     *
     */
    @ApiModelProperty(value = "任务运转时长，单位：毫秒")
    private Long durationInMillis;

    /**
     *
     */
    @ApiModelProperty(value = "详细内容")
    private List<String> content;

    /**
     * 表单数据
     */
    @ApiModelProperty(value = "表单数据")
    private Map<String, Object> formData;

    @ApiModelProperty(value = "comment")
    private String comment;
}
