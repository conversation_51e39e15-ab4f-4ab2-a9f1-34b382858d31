package com.mascj.lup.event.bill.dto;

import com.mascj.kernel.database.entity.Search;
import com.mascj.lup.event.bill.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(value = "工单参数")
@Data
public class BillSearchDTO extends Search {

    private String bizCode;

    @ApiModelProperty(value = "工单编号")
    private String billNumber;

    @ApiModelProperty(value = "事件编号 精准查询")
    private String eventBillNumber;

    @ApiModelProperty(value = "网格区域")
    private List<Long> gridIdList;


    @ApiModelProperty(value = "事件标签")
    private String eventLabel;

    @ApiModelProperty(value = "事件标签列表")
    private List<Long> eventLabelList;
    @ApiModelProperty(value = "机场ids")
    private List<Long> deviceIdList;

    @ApiModelProperty(value = "事件来源列表  标签id数组")
    private List<Integer> eventOriginList;

    @ApiModelProperty(value = "事件来源")
    private String eventOrigin;

    @ApiModelProperty(value = "日期-开始")
    private String dateStart;

    @ApiModelProperty(value = "日期-结束")
    private String dateEnd;

    @ApiModelProperty(value = "流程状态列表")
    private List<Integer> flowStateList;

    @ApiModelProperty(value = "流程状态")
    private Integer flowState;

    @ApiModelProperty(value = "处置类型列表 处置方式 0未处置 1 无需处置 2 已处置 3后期处置")
    private List<Integer> handleTypeList;


    @ApiModelProperty(value = "四平台处理结果 1处理成功  0 退回")
    private List<Integer> fourSuccessList;

    /**
     * 经纬度坐标，用于距离排序。标准格式：[31.426894,118.070765]
     */
    @ApiModelProperty(value = "经纬度坐标", notes = "用于距离排序。标准格式：[31.426894,118.070765]")
    private String lngLat;
    /**
     * 经纬度空间格式，用于数据库计算。前端不能用
     * <p>
     * 标准格式：POINT(31.426894 118.070765)
     */
    private String lngLatGeometry;

    private List<Long> gridUnitIdList;

    @ApiModelProperty(value = "占地面积 大于等于")
    private BigDecimal locateArea;

    @ApiModelProperty(value = "最小占地面积 大于等于")
    private BigDecimal locateAreaMin;
    @ApiModelProperty(value = " 最大占地面积 小于等于")
    private BigDecimal locateAreaMax;


    @ApiModelProperty(value = "网格id 如果是聚合使用  直接不传")
    private Long unitId;

    /**
     * 为空时  查询顶级数据 否则查询list数据
     */
    private String unitCode;

    @ApiModelProperty(value = "事件标签id授权")
    private List<Long> labelIdListPerm;

    @ApiModelProperty(value = "后期处理（挂起）状态：默认0   1已申请后期处理 待审批 2 已审批")
    private Integer pendingState;
}
