package com.mascj.lup.event.bill.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/9 15:25
 * @describe
 */
@Data
@ApiModel(value = "数据推送分页item")
public class LupDataPushLogPagedItemVO {

    private Long id;

    @ApiModelProperty(value = "事件编号")
    private Long dataId;

    @ApiModelProperty(value = "工单编号")
    private String billNumber;

    @ApiModelProperty(value = "事件来源")
    private Integer dataOrigin;
    @ApiModelProperty(value = "事件来源")
    private String eventOrigin;

    @ApiModelProperty(value = "甄别状态 是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）")
    private Integer generateBill;
    @ApiModelProperty(value = "甄别状态 是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）")
    private String generateBillName;

    @ApiModelProperty(value = "推送状态 0未推送   1已推送   -1推送失败")
    private Integer pushState;
    @ApiModelProperty(value = "推送状态 0未推送   1已推送   -1推送失败")
    private String pushStateName;

    @ApiModelProperty(value = "推送结果信息")
    private String pushMsg;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "推送时间")
    private String pushTime;

    @ApiModelProperty(value = "是否可以重新推送 0否 1是")
    private Integer pushable;

}
