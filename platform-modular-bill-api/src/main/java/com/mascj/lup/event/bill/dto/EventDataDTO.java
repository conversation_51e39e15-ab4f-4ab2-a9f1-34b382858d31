package com.mascj.lup.event.bill.dto;

import com.mascj.lup.event.bill.geo.DataPicPoint;
import com.mascj.lup.event.bill.geo.GeoTile;
import com.mascj.lup.event.bill.geo.GeoVary;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(value = "事件数据参数")
@Data
public class EventDataDTO extends FlySystemEventDataSourceDTO {

    //    数据类型：1图片 2瓦片 3视频 4其他
    @ApiModelProperty(value = "数据类型  1图片 2瓦片 3视频 4其他  5 人工建立的任务对应的瓦片（需要变更为 自定义瓦片或者 建图采集瓦片）")
    private int dataType;

    @ApiModelProperty(value = "瓦片-经纬度图形")
    private String shape;

    @ApiModelProperty(value = "图片-像素点图形")
    private List<DataPicPoint> pixelPointList;

    @ApiModelProperty(value = "当前数据 无人机飞行的任务id  来自无人机的业务字段")
    private List<AirlineDataDTO> currentAirlineTaskId;

    @ApiModelProperty(value = "比对数据 无人机飞行的任务id  来自无人机的业务字段")
    private List<AirlineDataDTO> compareAirlineTaskId;

    @ApiModelProperty(value = "当前数据 自定义瓦片数据")
    private List<GeoTile> currentTile;

    @ApiModelProperty(value = "比对数据 自定义瓦片数据")
    private List<GeoTile> compareTile;

    @ApiModelProperty(value = "占地面积 瓦片数据")
    private BigDecimal locateArea;


}
