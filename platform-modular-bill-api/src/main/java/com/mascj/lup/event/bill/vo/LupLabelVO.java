package com.mascj.lup.event.bill.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 *
 *
 * <AUTHOR> @since 2024-01-05 14:36:06
 */
@Data
@ApiModel("事件类型（事件标签）")
public class LupLabelVO {


    /**
     * 标签名
     */
    @ApiModelProperty("标签名")
    private String labelName;
    @ApiModelProperty("序号")
    private Integer number;

    private String algoName;
}