package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.lup.event.bill.entity.LupLabel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:29
 * @describe
 */
@Data
@ApiModel(value = "事件数据")
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class EventDataTaskVO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "数据来源类型：1 飞控AI提取   2 飞控人工提报 3 比对系统")
    private Integer dataOrigin;

    @ApiModelProperty(value = "事件编号  工单编号")
    private String billNumber;

    @ApiModelProperty(value = "扩展类数据：额外的数据")
    private String extraData;

    @ApiModelProperty(value = "扩展类数据：额外的数据")
    public ExtraDataVO extraDataVO;

    @ApiModelProperty(value = "事件图片url")
    private String eventPictureUrl;

    @ApiModelProperty(value = "事件图片url")
    private List<String> eventPictureUrlList;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "标签名称")
    private String labelName;

}
