package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/17 13:39
 * @describe
 */
@Data
@ApiModel(value = "推送数据默认参数")
public class PushDataDefaultDTO {

    @ApiModelProperty(value = "事件唯一标识ID")
    private Long dataId;

    @ApiModelProperty(value = "事件工单编号")
    private String billNumber;

    @ApiModelProperty(value = "事件类型名称")
    private String eventTypeName;

    @ApiModelProperty(value = "事件地理位置")
    private String eventLocation;

    /**
     * 事件经度
     */
    @ApiModelProperty(value = "事件经度")
    private BigDecimal locationLng;

    /**
     * 事件纬度
     */
    @ApiModelProperty(value = "事件纬度")
    private BigDecimal locationLat;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty("事件发现时 填写的事件备注描述")
    private String eventDesc;

    @ApiModelProperty("事件基础图片 建议客户自己读取重新保存到自己的服务器后保存")
    private List<String> picList;

    @ApiModelProperty("网格名称")
    private String gridName;

    @ApiModelProperty("网格编码")
    private String gridCode;

    @ApiModelProperty("坐标系")
    private String locationGeoType = "wgs84";

    @ApiModelProperty("授权应用app-ke")
    private String appKey;

    @ApiModelProperty("时间戳")
    private long timestamp;

    /**
     * 设备序列号
     */
    private String deviceSn;
    /**
     * 飞行任务名称
     */
    private String flyTaskName;

    /**
     * 飞行任务编号
     */
    private String flyTaskNumber;

    /**
     * 航线名称
     */
    private String airLineName;


}
