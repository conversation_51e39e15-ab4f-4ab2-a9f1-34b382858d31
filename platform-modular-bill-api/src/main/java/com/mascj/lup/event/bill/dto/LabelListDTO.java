package com.mascj.lup.event.bill.dto;

import com.mascj.lup.event.bill.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "事件标签参数")
@Data
public class LabelListDTO extends BaseDTO {

    @ApiModelProperty(value = "事件标签")
    private String eventLabel;

    @ApiModelProperty(value = "日期-开始")
    private String dateStart;

    @ApiModelProperty(value = "日期-结束")
    private String dateEnd;

    @ApiModelProperty(value = "事件标签id授权")
    private List<Long> labelIdListPerm;

}
