package com.mascj.lup.event.bill.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

/**
 * 报告生成记录
 */
@Data
public class DataReportVO {
	/** 应用类型id */
	private Long appId;
	private Long id;
	/** 应用类型名称 */
	private String appName;
	/** 当前处理数量，从1开始 */
	private Integer currentLine;
	/** 逻辑删除 */
	@TableLogic
	private boolean deleted;
	/** 文件下载地址 */
	private String downloadUrl;
	/** 执行备注：执行结果，成功 填写文件已经生成 执行失败，记录失败异常信息 */
	private String executeRemark;
	/** zip文件名 */
	private String fileName;
	/** 搜索条件，json字符串 */
	private String searchCondition;
	/** 搜索条件，按照json的key的阿斯克码升序排序，再按顺序拼接key和value，最后md5加密形成唯一hash
下次搜索时，先生成hash，判断是否存在，已完成任务直接下载资料，已有、正在生成时，提示文件正在生成中，请耐心等待
； */
	private String searchHash;
	/** 报告生成状态：0 报告生成中 1 报告已完成，可随时下载 2 报告生成失败 */
	private Integer state;
	/** 异步任务id */
	private String syncId;
	private String batchCode;
	/** 租户Id */
	private Long tenantId;
	/** 预计处理工单的总数量 */
	private Integer totalLine;

	@TableField(exist = false)
	private String templateId;

	@TableField(exist = false)
	private String reportTypeTag;

	@TableField(exist = false)
	private String fileTemplateUrl;

}