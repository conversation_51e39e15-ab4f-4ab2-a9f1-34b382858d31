package com.mascj.lup.event.bill.util.clip;

import java.util.List;

/**
 * @program: 靓马云
 * @description: 图形标识
 * @author: 徐旻晨
 * @E-mail:<EMAIL>
 * @Phone：18114482486
 * @create: 2021-04-19 10:47
 **/
public class WShpGeo {

    /// <summary>
    /// 矢量图形，外层 List 代表部件；里层 List 代表点集，一个点是点，不闭合点集代表线，闭合代表面
    /// </summary>
    public List<List<WPoint>> geo;

    // 图形包络线范围
    public double dMinX;
    public double dMinY;
    public double dMaxX;
    public double dMaxY;

    public List<List<WPoint>> getGeo() {
        return geo;
    }

    public void setGeo(List<List<WPoint>> geo) {
        this.geo = geo;
    }

    public double getdMinX() {
        return dMinX;
    }

    public void setdMinX(double dMinX) {
        this.dMinX = dMinX;
    }

    public double getdMinY() {
        return dMinY;
    }

    public void setdMinY(double dMinY) {
        this.dMinY = dMinY;
    }

    public double getdMaxX() {
        return dMaxX;
    }

    public void setdMaxX(double dMaxX) {
        this.dMaxX = dMaxX;
    }

    public double getdMaxY() {
        return dMaxY;
    }

    public void setdMaxY(double dMaxY) {
        this.dMaxY = dMaxY;
    }
}
