package com.mascj.lup.event.bill.enums;

import lombok.Data;
import lombok.Getter;

/**
 * 数据来源类型：1 飞控AI提取   2 飞控人工提报 3 比对系统
 */
@Getter
public enum DataOrigin {

    AIFetch("AI识别",1),HumanFetch("人工提报",2)
    ,CompareFetch("比对识别",3)
    ,FlyFile("飞行素材",4);

    DataOrigin(String name, int value){
        this.name=name;
        this.value = value;
    }
    private int value;
    private String name;

    public String getName() {
        return name;
    }

    public static DataOrigin parse(int value) {
        for (DataOrigin item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    public static String parseToName(int value) {
        for (DataOrigin item : values()) {
            if (item.getValue() == value) {
                return item.getName();
            }
        }
        return null;
    }
}
