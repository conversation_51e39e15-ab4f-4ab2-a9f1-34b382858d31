package com.mascj.lup.event.bill.enums;

import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import lombok.Getter;

/**
 * 是否生成工单 0否 1是
 * <AUTHOR>
 * @date 2025/1/3 10:16
 * @describe
 */
@Getter
public enum GenerateBillEnum {

    GenerateBillNothing(0,"不生成工单", EventTipKey.GenerateBillNothing),GenerateBill(1,"生成工单",EventTipKey.GenerateBill);

    int value;

    String desc;
    String name;

    GenerateBillEnum(int value,String desc,String name){

        this.value = value;
        this.desc = desc;
        this.name = name;

    }

    public String getName() {
        return LocaleMessageUtil.getMessage(name);
    }

    public static GenerateBillEnum parse(Integer value) {
        for (GenerateBillEnum item : values()) {

            //为空时 默认已甄别 老服务和数据处理用
            if(value == null) return GenerateBill;

            if (item.getValue() == value) {
                return item;
            }
        }
        return GenerateBillNothing;
    }
}
