package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

/**
 * <p>
 * 工单数据表
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_grid_user")
public class LupGridUser extends BaseEntity{

    private static final long serialVersionUID=1L;

    /**
     * 删除状态：0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 网格记录id
     */
    private Long gridUnitId;

    /**
     * 网格用户id
     */
    private Long userId;

    /**
     * 网格用户姓名
     */
    private String userName;

}
