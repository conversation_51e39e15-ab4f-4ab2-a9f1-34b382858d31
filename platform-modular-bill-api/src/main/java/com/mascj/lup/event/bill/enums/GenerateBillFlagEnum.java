package com.mascj.lup.event.bill.enums;

import io.swagger.annotations.ApiModel;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/8 18:27
 * @describe
 */
@Getter
@ApiModel(value = "推送甄别的数据 1，推送全量数据（包含已甄别和未甄别）2，默认推送已甄别的数据")
public enum GenerateBillFlagEnum {

    AllGenerateBillFlagEnum(2,"推送全量数据"),GenerateBillFlagEnum(1,"默认推送已甄别的数据");
    int code;
    String value;
    GenerateBillFlagEnum(int code,String value){
        this.code = code;
        this.value = value;
    }

    public static GenerateBillFlagEnum parse(int code) {
        for (GenerateBillFlagEnum item : values()) {
            if (item.getCode()==code) {
                return item;
            }
        }
        return GenerateBillFlagEnum;
    }
}
