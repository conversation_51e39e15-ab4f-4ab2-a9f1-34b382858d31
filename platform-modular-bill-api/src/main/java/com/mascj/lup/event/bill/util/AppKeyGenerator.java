package com.mascj.lup.event.bill.util;

/**
 * <AUTHOR>
 * @date 2025/1/13 16:43
 * @describe
 */
import java.util.UUID;

public class AppKeyGenerator {
    public static String generateAppKey() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static String generateAppSecret() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static String generateAppAuthCode() {
        return UUID.randomUUID().toString().toUpperCase();
    }
}
