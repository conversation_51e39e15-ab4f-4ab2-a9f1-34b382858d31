package com.mascj.lup.event.bill.feign;

import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.EventDataDTO;
import com.mascj.lup.event.bill.dto.FlySystemDelDTO;
import com.mascj.lup.event.bill.dto.FlySystemEventDataSourceDTO;
import com.mascj.lup.event.bill.vo.SendFourDoVo;
import com.mascj.lup.event.bill.vo.SendFourResutVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api(value = "工单服务",tags = "工单服务")
@FeignClient(value = "lup-event-bill-server")
public interface SendFourPlantFeign {

    @ApiOperation(value = "流程发送四平台业务，定制接口 不能随便掉")
    @PostMapping("/provider/support/doSendFour")
    Result<SendFourResutVo> doSendFour(@RequestBody SendFourDoVo sendFourDoVo);


}
