package com.mascj.lup.event.bill.feign.open;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.EventDataQueryDTO;
import com.mascj.lup.event.bill.open.vo.OpenEventDataDetailVO;
import com.mascj.lup.event.bill.open.vo.OpenEventDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2025/7/16 11:21
 * @describe
 */
@Api(value = "事件数据-开放接口",tags = "事件数据-开放接口")
@FeignClient(value = "lup-event-bill-server")
public interface IEventDataOpenProvider {

    @ApiOperation(value = "分页查询事件数据")
    @PostMapping("/eventData/pagedEventData")
    Result<IPage<OpenEventDataVO>> pagedEventData(@RequestBody EventDataQueryDTO eventDataQueryDTO);

    @ApiOperation(value = "根据id查询事件数据详情")
    @GetMapping("/eventData/detail/{id}")
    Result<OpenEventDataDetailVO> detailEventData(@PathVariable("id") Long billId);
}
