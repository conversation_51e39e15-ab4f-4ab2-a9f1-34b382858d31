package com.mascj.lup.event.bill.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 工单网格变更参数
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@ApiModel(value = "工单网格变更参数")
public class BillSwitchGridDTO {

    @ApiModelProperty(value = "工单id")
    private Long billId;

    @ApiModelProperty(value = "目标网格id")
    private Long targetGridUnitId;

}
