package com.mascj.lup.event.bill.vo;

import com.mascj.kernel.common.util.StringUtil;
import com.mascj.lup.event.bill.constant.VariableConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FlyEventTjResVO implements Serializable {


    @ApiModelProperty("待巡查事件")
    private Integer eventNum;
    @ApiModelProperty("核准")
    private Integer eventEndNum;




}
