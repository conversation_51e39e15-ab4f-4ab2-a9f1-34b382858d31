package com.mascj.lup.event.bill.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/23 10:08
 * @describe
 */
@Data
@ApiModel(value = "工单查询统计响应参数")
public class BillQueryCountVO {

    @ApiModelProperty(value = "所属网格")
    private String gridName;

    @ApiModelProperty(value = "类型： 事件标签（事件类型）")
    private String eventTypeName;

    @ApiModelProperty(value = "数量（个）")
    private BigDecimal billCount;
}
