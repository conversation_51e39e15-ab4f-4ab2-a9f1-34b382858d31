package com.mascj.lup.event.bill.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/16 17:23
 * @describe
 */
@Data
public class DataPicDTO {

    public DataPicDTO(){
        this.eventPictureUrlList = new ArrayList<>();
    }

    /**
     * 事件图片
     */
    private String eventPictureUrl;

    private List<String> eventPictureUrlList;

    private List<String> carNumberList;
    private String parentOriginalDataLogo;
    private List<String> plateTag;

    private boolean ocr;
}
