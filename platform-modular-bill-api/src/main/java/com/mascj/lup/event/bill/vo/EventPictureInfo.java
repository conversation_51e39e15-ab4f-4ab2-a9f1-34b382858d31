package com.mascj.lup.event.bill.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/11 11:16
 * @describe
 */
//{"lat":30.213123,"lng":1203434343,"url":"https://fk.com/a.png","type":"1 代表图片 2 代表视频","urlList":[万一多个 就启用这个字段]}
@Data
public class EventPictureInfo {

    private BigDecimal lat;

    private BigDecimal lng;

    private String url;

    private Integer type;

    private List<String> urlList;
}
