package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;


/**
 * 永安事件上报实体类
 * 用于接收和处理事件上报相关数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class YonganEventReportEntity {

        /**
         * 事件问题来源ID
         */
        private Integer sourceId;

        /**
         * 一级分类
         */
        private String categoryLevel1;

        /**
         * 二级分类
         */
        private String categoryLevel2;

        /**
         * 三级分类
         */
        private String categoryLevel13;

        /**
         * 四级分类
         */
        private String categoryLevel4;

        /**
         * 事件描述
         */
        private String description;

        /**
         * 事件紧急程度
         */
        private String urgencyDegree="0";

        /**
         * 发生地址
         */
        private String address;

        /**
         * 是否进街镇核验
         * 1表示是，0表示否
         */
        private String isStreetAccept="0";

        /**
         * 是否需要城运平台流转处置
         * 1表示是，0表示否
         */
        private String isDisposeInCitySystem="1";

        /**
         * 坐标信息
         */
        private CoordinateInfo coordinate;

        /**
         * 多媒体集合
         */
        private List<AttachmentInfo> attachments;

        /**
         * 坐标信息内部类
         */
        @Data
        public static class CoordinateInfo {

            /**
             * 经度
             */
            private String longitude;

            /**
             * 纬度
             */
            private String latitude;
        }

        /**
         * 附件信息内部类
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class AttachmentInfo {

            /**
             * 文件分组
             * report上报，disposition处置，other其他，有多媒体时必填
             */
            private String fileGroup;

            /**
             * 文件名(带后缀)
             * 有多媒体时必填
             */
            private String fileName;

            /**
             * 多媒体上传接口中返回的id
             * 有多媒体时必填
             */
            private String field;
        }
}
