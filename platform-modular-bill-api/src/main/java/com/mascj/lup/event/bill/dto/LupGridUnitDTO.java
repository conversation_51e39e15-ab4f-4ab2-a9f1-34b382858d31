package com.mascj.lup.event.bill.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 网格数据表
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@ApiModel("网格初始化参数")
public class LupGridUnitDTO {

    /**
     * 名称
     */
    @ApiModelProperty("网格名称")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty("网格编码，直接用租户编码即可")
    private String code;


    @ApiModelProperty("项目范围")
    private String shape;

    /**
     * 租户ID
     */
    @ApiModelProperty("租户ID")
    private Long tenantId;


}
