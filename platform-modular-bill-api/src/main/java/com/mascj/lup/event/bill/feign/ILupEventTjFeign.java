package com.mascj.lup.event.bill.feign;


import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.vo.FlyEventTjQueryVO;
import com.mascj.lup.event.bill.vo.FlyEventTjResVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "lup-event-bill-server")
public interface ILupEventTjFeign {

    @PostMapping(value = "/provider/v3/lupEventFeign/eventTj")
    Result<FlyEventTjResVO> eventTj(@RequestBody FlyEventTjQueryVO flyEventTjQueryVO);

}
