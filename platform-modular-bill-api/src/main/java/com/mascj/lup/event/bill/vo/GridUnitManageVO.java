package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.mascj.kernel.web.tree.INode;
import com.mascj.lup.event.bill.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@ApiModel(value = "网格展示出参")
@Data
public class GridUnitManageVO implements INode {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "网格编码")
    private String gridCode;

    @ApiModelProperty(value = "网格名称")
    private String gridName;

    @ApiModelProperty(value = "顺序")
    private int sort;

    @ApiModelProperty(value = "占地面积")
    private BigDecimal locateArea;

    /**
     * 子孙节点
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<INode> children;

    private Long parentId;

    @Override
    public List<INode> getChildren() {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        return this.children;
    }
}
