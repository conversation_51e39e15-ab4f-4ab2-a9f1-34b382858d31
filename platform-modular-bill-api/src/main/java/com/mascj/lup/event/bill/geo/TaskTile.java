package com.mascj.lup.event.bill.geo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "任务瓦片")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TaskTile {

    @ApiModelProperty(value = "当前瓦片")
    private List<GeoTile> currentTile;

    @ApiModelProperty(value = "对比瓦片")
    private List<GeoTile> compareTile;

    @ApiModelProperty("瓦片模式：任务范围 ")
    private GeoVary shape;
}
