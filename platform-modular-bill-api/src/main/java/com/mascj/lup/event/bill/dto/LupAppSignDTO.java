package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/1/13 16:27
 * @describe
 */
@Data
@ApiModel(value = "应用参数")
public class LupAppSignDTO {

    @ApiModelProperty(value = "客户应用名称",required = true)
    @NotNull
    @NotBlank
    private String appKey;

    @ApiModelProperty(value = "签名 规则  根据 key 加上查询的secret 链接 后 MD5加密 生成签名 全部大写",required = true)
    @NotNull
    @NotBlank
    private String sign;
}
