package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:29
 * @describe
 */
@Data
@ApiModel(value = "统计事件数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CountEventDataVO {

    @ApiModelProperty(value = "统计的分组名称")
    private String name;

    @ApiModelProperty(value = "统计数量")
    private int count;

    @ApiModelProperty(value = "周")
    private String weekDay;

    @ApiModelProperty(value = "工单日期")
    private String billDate;

    @ApiModelProperty(value = "工单月份")
    private String billMonth;
    @ApiModelProperty(value = "工单年份")
    private String billYear;


    @ApiModelProperty(value = "周统计")
    private CountEventDataByTimeVO weekCount;

    @ApiModelProperty(value = "月统计")
    private CountEventDataByTimeVO monthCount;

    @ApiModelProperty(value = "年统计")
    private CountEventDataByTimeVO yearCount;

}
