package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "查询网格列表")
public class ListUnitDTO extends BillDTO {


    @ApiModelProperty(value = "网格id 如果是聚合使用  直接不传")
    private Long unitId;

    /**
     * 为空时  查询顶级数据 否则查询list数据
     */
    private String unitCode;

    private int limitCount = 1000;

    @ApiModelProperty(value = "事件编号 精准查询")
    private String eventBillNumber;

    @ApiModelProperty(value = "占地面积 大于等于")
    private BigDecimal locateArea;

    @ApiModelProperty(value = "最小占地面积 大于等于")
    private BigDecimal locateAreaMin;
    @ApiModelProperty(value = " 最大占地面积 小于等于")
    private BigDecimal locateAreaMax;

}
