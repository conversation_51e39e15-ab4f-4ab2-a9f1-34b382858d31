package com.mascj.lup.event.bill.enums;

import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import lombok.Getter;

/**
 * 后期处理（挂起）状态：默认0   1已申请后期处理 待审批 2 已审批 3 已驳回
 * pendJudging pendingToHandle
 * <AUTHOR>
 * @date 2024/12/27 17:05
 * @describe
 */
@Getter
public enum PendingStateEnum {

    PendingStateEnumNone("NonePendingStateEnum",0,"默认0",""),
    PendingStateEnumWaitViewing("pendJudging",1,"1已申请后期处理",EventTipKey.EventPendingApproving),
    PendingStateEnumViewingAccess("pendingToHandle",2,"2已审批",EventTipKey.EventPendingApprovalAccess),
    PendingStateEnumViewingReject("pendingToReject",3,"3已驳回",EventTipKey.EventPendingApprovalReject)
    ;
    PendingStateEnum(String code ,Integer pendingState,String desc,String name){
        this.code = code;
        this.pendingState = pendingState;
        this.desc = desc;
        this.name = name;
    }
    String code;
    Integer pendingState;
    String desc;
    String name;

    public String getName() {
        return LocaleMessageUtil.getMessage(name);
    }

    public static PendingStateEnum parse(String code) {
        for (PendingStateEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return PendingStateEnumNone;
    }

    public static PendingStateEnum parseFromState(Integer pendingState) {
        for (PendingStateEnum item : values()) {
            if (item.getPendingState() == pendingState) {
                return item;
            }
        }
        return PendingStateEnumNone;
    }
}
