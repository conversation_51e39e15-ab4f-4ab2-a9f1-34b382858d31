package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.lup.event.bill.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "工单返回数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BillDataVO extends BaseVO{

    @ApiModelProperty(value = "事件工单编号")
    private String billNumber;

    @ApiModelProperty(value = "事件经度")
    private BigDecimal locationLng;

    @ApiModelProperty(value = "事件纬度")
    private BigDecimal locationLat;

    @ApiModelProperty(value = "事件占地面积")
    private BigDecimal locateArea;


    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @ApiModelProperty(value = "事件来源")
    private Integer dataOrigin;

}
