package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.lup.event.bill.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "综合展示 网格列表")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class GriUnitCountVO extends BaseVO {

    @ApiModelProperty(value = "网格名称")
    private String gridName;

    @ApiModelProperty(value = "网格图形")
    private String shape;

    @ApiModelProperty(value = "事件数量")
    private int eventAmount;
}
