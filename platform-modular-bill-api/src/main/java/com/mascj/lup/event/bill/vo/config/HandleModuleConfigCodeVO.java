package com.mascj.lup.event.bill.vo.config;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/18 10:00
 * @describe
 */
@Data
public class HandleModuleConfigCodeVO {

    private Boolean eventSmsSwitchKey;

    private String eventSmsTimeListKey;

    private String hmcFlowActivityKey;
    /**
     * 流程时间期限开关
     */
    private Boolean flowEntityTimeOutSwitchKey;

    private String flowEntityTimeOutKey;

    private String eventNotificationApprovalRejectKey;
    private String exportBillDataTemplateStrategyKey;
}
