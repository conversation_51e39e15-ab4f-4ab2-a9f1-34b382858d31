package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.lup.event.bill.base.BaseBillEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/13 15:46
 * @describe
 */
@Data
@TableName("lup_app")
@ApiModel(value = "应用记录 平台级数据")
public class LupApp extends BaseBillEntity {

    @ApiModelProperty(value = "客户应用名称")
    private String name;

    @ApiModelProperty(value = "应用key 可以公网传输")
    private String appKey;

    @ApiModelProperty(value = "授权码 用于激活")
    private String authCode;

    @ApiModelProperty(value = "是否激活 0否 1是")
    private Integer active;
    @ApiModelProperty(value = "激活次数")
    private Integer activeCount;

    @ApiModelProperty(value = "应用密钥")
    private String appSecret;

    @ApiModelProperty(value = "有效日期 为null的情况下 不受期限")
    private String validDateTime;

    @ApiModelProperty(value = "备注描述")
    private String appDesc;

}
