package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/5 14:10
 * @describe
 */
@Data
@ApiModel("切换工单启用入参")
public class SwitchBillEnableDTO {

    @ApiModelProperty(value = "工单id",required = true)
    @NotNull
    private Long billId;

    @ApiModelProperty(value = "可用 0否 1是",required = true)
    @NotNull
    private Integer usable;

    @ApiModelProperty("原因")
    private String reason;

    @ApiModelProperty("附件url列表")
    private List<LupBillFileDTO> fileUrlList;

}
