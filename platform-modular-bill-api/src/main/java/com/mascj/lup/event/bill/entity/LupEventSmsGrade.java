package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 处置分级短信
 * </p>
 *
 * <AUTHOR> @since 2024-01-01
 */
@Data
@TableName("lup_event_sms_grade")
public class LupEventSmsGrade extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    @ApiModelProperty(value = "标签ID")
    private Long labelId;

    /**
     * 类型 1：一般(待办，要求事件计划执行，会发送事件处置的汇总通知短信；) 2紧急(速办，要求事件尽快处理，会实时发送巡查和处置短信。)
     */
    @ApiModelProperty(value = "类型 1：一般(待办) 2：紧急(速办)")
    private Integer type;

    /**
     * 巡查短信是否开启 1开 0不开
     */
    @ApiModelProperty(value = "巡查短信是否开启 1开 0不开")
    private Integer patrolSms;

    /**
     * 处置短信是否开启 1开 0不开
     */
    @ApiModelProperty(value = "处置短信是否开启 1开 0不开")
    private Integer handleSms;
    /**
     * 类型  1 ai识别  2人工提报 3比对识别  4飞行素材 5设备 11三方数据
     */
    @ApiModelProperty(value = "类型  1 ai识别  2人工提报 3比对识别  4飞行素材 5设备 11三方数据")
    private Integer codeValue;

    /**
     * 租户Id
     */
    @ApiModelProperty(value = "租户Id")
    private Long tenantId;

    /**
     * 逻辑删除 0未删除 1已删除
     */
    @TableLogic
    @ApiModelProperty(value = "逻辑删除 0未删除 1已删除")
    private Integer deleted;

}