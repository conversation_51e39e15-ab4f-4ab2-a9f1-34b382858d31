package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 网格数据表
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_grid_unit")
public class LupGridUnit extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 父id
     */
    private Long pid;

    /**
     * 完整路径
     */
    private String hierarchy;

    /**
     * 网格面坐标组
     */
    private String polygon;

    /**
     * 网格重心点坐标
     */
    private String centriod;

    /**
     * geometry字符串
     */
    private String geometryStr;

    /**
     * 中心点字符串
     */
    private String centriodStr;

    /**
     * 网格面积
     */
    private BigDecimal gridArea;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态
     */
    private Boolean status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否已删除
     */
    private Integer isDeleted;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 项目ID
     */
    private Long projectId;

}
