package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/9 10:20
 * @describe
 */
@ApiModel(value = "网格用户参数类")
@Data
public class LupGridUserDTO {

    @ApiModelProperty(value = "网格id")
    private Long gridUnitId;

    @ApiModelProperty(value = "用户id列表")
    private List<LupUserDTO> userList;
}
