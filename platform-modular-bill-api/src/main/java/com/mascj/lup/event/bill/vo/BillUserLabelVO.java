package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

@Data
@ApiModel(value = "用户标签数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BillUserLabelVO {

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "标签id")
    private Long labelId;

    @ApiModelProperty(value = "工单id")
    private Long billId;

    @ApiModelProperty(value = "处理任务id")
    private Long processTaskId;

    private String eventNo;
    private String eventLabel;
    private String gridName;

    private Long dataId;
    private Long tenantId;

    private Integer dataOrigin;

}
