package com.mascj.lup.event.bill.dto;

import com.mascj.kernel.database.entity.Search;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/9 15:58
 * @describe
 */
@Data
@ApiModel(value = "三方平台推送记录")
public class LupDataPushLogPagedDTO extends Search {

    @ApiModelProperty(value = "事件编号、工单编号")
    private String number;

    @ApiModelProperty(value = "创建时间范围：开始时间")
    private String startTime;

    @ApiModelProperty(value = "创建时间范围：结束时间")
    private String endTime;

    @ApiModelProperty(value = "甄别状态 是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）")
    private Integer generateBill;

    @ApiModelProperty(value = "甄别状态 是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）")
    private List<Integer> generateBillList;

    @ApiModelProperty(value = "推送状态 0未推送   1已推送   -1推送失败")
    private Integer pushState;

    @ApiModelProperty(value = "推送状态 0未推送   1已推送   -1推送失败")
    private List<Integer> pushStateList;

    @ApiModelProperty(value = "事件来源")
    private Integer dataOrigin;

    @ApiModelProperty(value = "事件来源")
    private List<Integer> dataOriginList;


}
