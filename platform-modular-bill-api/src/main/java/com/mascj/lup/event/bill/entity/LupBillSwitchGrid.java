package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

/**
 * <p>
 * 工单数据表
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_bill_switch_grid")
public class LupBillSwitchGrid extends BaseEntity{

    private static final long serialVersionUID=1L;

    /**
     * 删除状态：0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 租户id 
     */
    private Long tenantId;


    /**
     * 工单id
     */
    private Long billId;

    /**
     * 流程信息id
     */
    private Long flowId;

    private String originalAreaCode;
    private String originalAreaName;
    private String originalGridCode;
    private String originalGridName;


    private String targetAreaCode;
    private String targetAreaName;
    private String targetGridCode;
    private String targetGridName;

    private Integer submitState;

    /**
     * 操作人姓名
     */
    private String operateUserName;

    /**
     * 操作类型 1 切换网格 2 切换事件类型
     */
    private Integer operateType;

    /**
     * 任务ID
     */
    private String processTaskId;

    private String operateOriginalName;
    private String operateTargetName;

}
