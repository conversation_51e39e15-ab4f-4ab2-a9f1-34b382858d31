package com.mascj.lup.event.bill.vo;

import cn.hutool.core.img.FontUtil;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.HyperlinkData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.context.MessageSource;
import org.springframework.util.PropertyPlaceholderHelper;


import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2024/3/25 15:52
 * @describe
 */
public class CustomerTitleHandler implements CellWriteHandler {
    PropertyPlaceholderHelper placeholderHelper = new PropertyPlaceholderHelper("${", "}");

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer integer, Integer integer1, Boolean aBoolean) {
        if (head != null) {
            List<String> headNameList = head.getHeadNameList();
            if (CollectionUtils.isNotEmpty(headNameList) ) {

                for (int i = 0; i < headNameList.size(); i++) {
                    try {
                        if(headNameList.get(i).startsWith("${") && headNameList.get(i).endsWith("}")) {
                            String key = headNameList.get(i).replace("${","").replace("}","");
                            String val = LocaleMessageUtil.getMessage(key);
                            headNameList.set(i, val);
                            System.out.println("val=" + val);
                        }
                    }catch (Exception exp){
                        exp.printStackTrace();
                    }
                }

            }
        }
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer integer, Boolean aBoolean) {

        System.out.println("afterCellCreate");
    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, CellData cellData, Cell cell, Head head, Integer integer, Boolean aBoolean) {
        System.out.println("afterCellDataConverted");
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<CellData> cellValues, Cell cell, Head head, Integer integer, Boolean isHead) {
        System.out.println("afterCellDataConverted");

        List<String> nameList = new ArrayList<>();
        nameList.add("eventUrlAddress");

        if (!isHead &&
                nameList.contains(head.getFieldName())
                && cellValues!=null && cellValues.size()>0 && cellValues.get(0) != null) {

            String url = cell.getStringCellValue(); //cellValues.get(0).getStringValue();
            cell.setHyperlink(createHyperlink(url, writeSheetHolder.getSheet().getWorkbook()));
            cell.getHyperlink().setAddress(url);


            Workbook workbook = writeSheetHolder.getParentWriteWorkbookHolder().getWorkbook();

            url = url.replaceAll("[{}]", "");
            // xlsx格式，如果是老版本格式的话就用 HSSFRichTextString
            XSSFRichTextString richString = new XSSFRichTextString(url);

            CellStyle cellStyle = workbook.createCellStyle();
            Font cellFont = workbook.createFont();
            cellFont.setFontName("宋体");
            cellStyle.setFont(cellFont);
            cellFont.setColor(IndexedColors.BLUE.getIndex());

            richString.applyFont(0, url.length()-1, cellFont);

            cellStyle.setFillPattern(FillPatternType.FINE_DOTS);
            cellStyle.setAlignment(HorizontalAlignment.CENTER);// 水平居中
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
            cell.setCellStyle(cellStyle);

            cell.setCellValue(richString);

        }
    }
    private Hyperlink createHyperlink(String url, Workbook workbook) {
        CreationHelper createHelper = workbook.getCreationHelper();
        return createHelper.createHyperlink(HyperlinkData.HyperlinkType.URL.getValue());// 设置超链接地址
    }
}