package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/30 11:39
 * @describe
 */
@Data
@ApiModel("申请挂起工单参数")
public class LupBillPendingApplyDTO {

    @ApiModelProperty(value = "工单ID",required = true)
    @NotNull
    private Long billId;

    @ApiModelProperty(value = "流程当前任务ID",required = true)
    @NotNull
    private String processTaskId;

    @ApiModelProperty(value = "申请原因",required = true)
    @NotNull
    @NotBlank
    private String applyReason;

    @ApiModelProperty(value = "挂起附件的文件ID列表")
    private List<Long> fileIdList;
}
