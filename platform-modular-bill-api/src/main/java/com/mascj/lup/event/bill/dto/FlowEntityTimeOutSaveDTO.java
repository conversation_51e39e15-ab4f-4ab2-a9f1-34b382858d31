package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/15 14:58
 * @describe
 */
@Data
@ApiModel("流程期限数据保存参数")
public class FlowEntityTimeOutSaveDTO {

    /**
     * 流程当前任务
     */
    @ApiModelProperty("流程当前任务")
    private String processTaskId;

    /**
     * 流程ID
     */
    @ApiModelProperty("流程ID")
    private Long flowEntityId;


    @ApiModelProperty("流程阶段")
    private Integer flowState;

}
