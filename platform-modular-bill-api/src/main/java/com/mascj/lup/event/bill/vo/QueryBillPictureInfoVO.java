package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/26 15:30
 * @describe
 */
@Data
@ApiModel(value = "事件查询图片回参")
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class QueryBillPictureInfoVO {

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "事件编号")
    private String billNumber;

    @ApiModelProperty(value = "图片链接")
    private String picUrl;

    @ApiModelProperty(value = "事件类型")
    private String eventType;
}
