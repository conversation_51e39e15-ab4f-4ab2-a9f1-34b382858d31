package com.mascj.lup.event.bill.util;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Line2D;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class DrawPolygonForImageUtil {

    public static void main(String[] args) {
        String fromFile = "/Users/<USER>/Downloads/1.jpeg";
        String toPath = "/Users/<USER>/Downloads/a/";
        int[] xPoint = {474, 471, 963, 855};
        int[] yPoint = {711, 996, 1005, 612};

        List<List<String>> pointList = new ArrayList<>();

        List<String> p = new ArrayList<>();
        p.add("10%");
        p.add("10%");
        List<String> p1 = new ArrayList<>();

        p1.add("10%");
        p1.add("20%");
        List<String> p2 = new ArrayList<>();


        p2.add("30%");
        p2.add("30%");


        List<String> p3 = new ArrayList<>();
        p3.add("30%");
        p3.add("10%");

        pointList.add(p);
        pointList.add(p1);
        pointList.add(p2);
        pointList.add(p3);



        DrawPolygonForImageUtil.fillPolygon(fromFile, pointList, toPath);
        DrawPolygonForImageUtil.fillPolygon(fromFile, xPoint, yPoint, toPath);
        DrawPolygonForImageUtil.reverseFillPolygon(fromFile, xPoint, yPoint, toPath);
        DrawPolygonForImageUtil.fillPolygonWithLine(fromFile, xPoint, yPoint, toPath);
    }
    /**
     * 填充矩形（单色）
     * @param fromFile
     * @param percentPointList 百分比坐标
     * @param toPath
     */
    public static void fillPolygon(String fromFile, List<List<String>> percentPointList, String toPath) {
        try {
            int[] x = new int[percentPointList.size()];
            int[] y = new int[percentPointList.size()];

            BufferedImage from = ImageIO.read(new File(fromFile));

            for (int i =0 ;i< percentPointList.size();i++) {
                x[i] = from.getWidth()* Integer.parseInt(percentPointList.get(i).get(0).replace("%",""))/100;
                y[i] = from.getHeight()* Integer.parseInt(percentPointList.get(i).get(1).replace("%",""))/100;
            }

            Graphics2D g2d = from.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setColor(new Color(255, 0, 0, 255)); //设置颜色
            g2d.setStroke(new BasicStroke(5f)); //设置划线粗细
            Polygon polygon = new Polygon(x, y, 4);
            g2d.drawPolygon(polygon);

            String toFile = toPath + System.currentTimeMillis() + ".jpg";
            FileOutputStream out = new FileOutputStream(toFile);
            ImageIO.write(from, "jpg", out);
            out.close();
            System.out.println("==fillPolygon==================" + toFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * 填充矩形（单色）
     * @param fromFile
     * @param x
     * @param y
     * @param toPath
     */
    public static void fillPolygon(String fromFile, int[] x, int[] y, String toPath) {
        try {
            BufferedImage from = ImageIO.read(new File(fromFile));

            Graphics2D g2d = from.createGraphics();

            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setColor(new Color(255, 0, 0, 255)); //设置颜色
            g2d.setStroke(new BasicStroke(5f)); //设置划线粗细

            Polygon polygon = new Polygon(x, y, 4);
            g2d.drawPolygon(polygon);
//            g2d.fillPolygon(polygon);

            int[] xPoint = {0, 100, 100, 0};
            int[] yPoint = {0, 0, 100, 100};

            Polygon polygon2 = new Polygon(xPoint, yPoint, 4);
            g2d.drawPolygon(polygon2);


            String toFile = toPath + System.currentTimeMillis() + ".jpg";
            FileOutputStream out = new FileOutputStream(toFile);
            ImageIO.write(from, "jpg", out);
            out.close();
            System.out.println("==fillPolygon==================" + toFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 反向填充矩形（单色）
     * @param fromFile
     * @param x
     * @param y
     * @param toPath
     */
    public static void reverseFillPolygon(String fromFile, int[] x, int[] y, String toPath) {
        try {
            BufferedImage img_from = ImageIO.read(new File(fromFile));

            //1、绘制裁剪矩形
            Polygon polygon = new Polygon(x, y, 4);

            //设置裁剪后的背景色
            Rectangle2D rectangle = new Rectangle(0,0,img_from.getWidth(),img_from.getHeight());
            TexturePaint tPaint = new TexturePaint(img_from,rectangle);
            BufferedImage img_clip = new BufferedImage(img_from.getWidth(), img_from.getHeight(), BufferedImage.TYPE_4BYTE_ABGR);

            Graphics2D g2d_clip = img_clip.createGraphics();
            g2d_clip.setPaint(tPaint);
            g2d_clip.fillPolygon(polygon);
            g2d_clip.dispose();

            //2、将裁剪后的原图整个填充背景色
            Graphics2D g2d = img_from.createGraphics();
            g2d.setColor(new Color(255, 255, 255, 150)); //设置颜色
            g2d.fillRect(0, 0, img_from.getWidth(), img_from.getHeight()); //填充指定的矩形

            //3、将裁剪的矩形绘制到原图
            g2d.drawImage(img_clip, 0, 0, null);

            g2d.dispose();

            String toFile2 = toPath + System.currentTimeMillis() + ".jpg";
            FileOutputStream out2 = new FileOutputStream(toFile2);
            ImageIO.write(img_from, "jpg", out2);
            out2.close();
            System.out.println("==reverseFillPolygon==================" + toFile2);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * 填充矩形（斜线）
     * @param fromFile
     * @param x
     * @param y
     * @param toPath
     */
    public static void fillPolygonWithLine(String fromFile, int[] x, int[] y, String toPath) {
        try {
            BufferedImage from = ImageIO.read(new File(fromFile));

            Graphics2D g2d = from.createGraphics();

            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setColor(new Color(255, 255, 255, 200)); //设置颜色
            g2d.setStroke(new BasicStroke(1.5f)); //设置划线粗细

            Polygon polygon = new Polygon(x, y, 4);

            //裁切
            g2d.setClip(polygon);

            //取得多边形外接矩形
            Rectangle rect = polygon.getBounds();

            //绘制填充线i
            for (int i = rect.y; i-rect.width < rect.y + rect.height; i = i + 6) {
                Line2D line = new Line2D.Float(rect.x, i, (rect.x + rect.width), i-rect.width);
                g2d.draw(line);
            }
            //绘制多边形
            g2d.drawPolygon(polygon);

            //输出绘制后的图片
            String toFile = toPath + System.currentTimeMillis() + ".jpg";
            FileOutputStream out = new FileOutputStream(toFile);
            ImageIO.write(from, "jpg", out);
            out.close();
            System.out.println("==fillPolygonWithLine==================" + toFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
