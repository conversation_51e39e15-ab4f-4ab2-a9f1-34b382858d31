package com.mascj.lup.event.bill.dto;

import com.mascj.kernel.database.entity.Search;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@ApiModel(value = "工单参数")
@Data
public class QueryBillSearchDTO extends Search {
    
    @ApiModelProperty(value = "工单编号")
    private String billNumber;

    @ApiModelProperty(value = "事件标签列表")
    private List<String> eventLabelNameList;

    @ApiModelProperty(value = "事件来源列表  标签id数组  数据来源类型：1 飞控AI提取   2 飞控人工提报 3 比对系统 ")
    private List<Integer> eventOriginList;

    @ApiModelProperty(value = "日期-开始")
    @NotEmpty
    private String dateStart;

    @ApiModelProperty(value = "日期-结束")
    @NotEmpty
    private String dateEnd;

    @ApiModelProperty(value = "最小占地面积 大于等于")
    private BigDecimal locateAreaMin;
    @ApiModelProperty(value = " 最大占地面积 小于等于")
    private BigDecimal locateAreaMax;

}
