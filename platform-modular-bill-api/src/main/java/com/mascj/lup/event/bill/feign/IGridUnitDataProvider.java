package com.mascj.lup.event.bill.feign;

import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.EventDataDTO;
import com.mascj.lup.event.bill.dto.FlySystemDelDTO;
import com.mascj.lup.event.bill.dto.FlySystemEventDataSourceDTO;
import com.mascj.lup.event.bill.dto.LupGridUnitDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api(value = "网格数据服务",tags = "网格数据服务")
@FeignClient(value = "lup-event-bill-server")
public interface IGridUnitDataProvider {

    @ApiOperation(value = "【网格数据服务】初始化网格数据")
    @PostMapping("/support/initProjectGridUnitData")
    Result initProjectGridUnitData(@RequestBody LupGridUnitDTO gridUnitDTO);
}
