package com.mascj.lup.event.bill.vo;


import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.support.config.vo.meta.MetaTag;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;

/**
 * 标签页数量统计
 */
@Data
public class TagStateCountVo {

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    private String tagName;

    /**
     * 标签编码
     */
    @ApiModelProperty(value = "标签编码")
    private String tagCode;
    
    private Integer tagType;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private List<Integer> flowStates;

    @ApiModelProperty(value = "数量")
    private long count;

    public TagStateCountVo(MetaTag metadata) {
        Assert.notNull(metadata, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneMetaData, Arrays.asList(EventTipKey.RequiredTip)));
        if(metadata.getTagType()!=null && metadata.getTagType() == 1)
            Assert.notEmpty(metadata.getFlowStates(), LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneTagName, Arrays.asList(EventTipKey.NoneTip)) + metadata.getTagName());
        tagName = metadata.getTagName();
        tagCode = metadata.getTagCode();
        flowStates = metadata.getFlowStates();
        tagType = metadata.getTagType();
    }
}
