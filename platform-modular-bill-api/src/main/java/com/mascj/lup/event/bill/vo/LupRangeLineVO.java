package com.mascj.lup.event.bill.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/29 09:30
 * @describe
 */
@Data
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class LupRangeLineVO extends BaseEntity {

    /**
     * 面积
     */
    private String area;

    /**
     * 备注
     */
    private String remark;

    /**
     * 范围中心点  纬度
     */
    private BigDecimal lat;

    /**
     * 范围中心点  经度
     */
    private BigDecimal lng;

    /**
     * 属性
     */
    private String properties;

    /**
     * 范围数据
     */
    private String geometry;
}
