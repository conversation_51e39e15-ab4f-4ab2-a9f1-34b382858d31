package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
报告模板记录：查询数据时，org_id为0是 默认通用可用模板；不为0时 为个性化模板；
 */
@Data
@ApiModel("报告模板表")
@TableName("data_report_template")
@EqualsAndHashCode(callSuper = true)
public class DataReportTemplate extends BaseEntity {
	/** 逻辑删除 */
	@TableLogic
	private boolean deleted;
	/** 模板文件的名称 */
	private String fileTemplateName;
	/** 模板地址 */
	private String fileTemplateUrl;

	/** 行政主体id */
	private Long orgId;
	/** 报告类型标签：
normalReportType 只有数字的报告
imageReportType 带图片的报告
lishuiReportType 溧水的台账报告 */
	private String reportTypeTag;
	/** 租户Id */
	private Long tenantId;


}