package com.mascj.lup.event.bill.entity;

import com.mascj.lup.event.bill.base.BaseBillEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/8 19:51
 * @describe
 */
@Data
@ApiModel(value = "数据推送日志")
public class LupDataPushLog extends BaseBillEntity {

    @ApiModelProperty(value = "数据ID")
    private Long dataId;

    @ApiModelProperty(value = "推送状态 0未推送   1已推送   -1推送失败 2无需推送")
    private Integer pushState;

    @ApiModelProperty(value = "推送结果信息")
    private String pushMsg;

    @ApiModelProperty(value = "推送策略")
    private String pushStage;

    @ApiModelProperty(value = "推送模式1 已甄别;2全量 ")
    private Integer pushMode;

    @ApiModelProperty(value = "是否自动推 0否 1是")
    private Integer pushAuto;

    @ApiModelProperty(value = "推送事件的流程 默认通用流程 DefaultFlow、推送关联流程（杨汛桥项目的事件流程）PushEventFlow")
    private String pushEventFlow;

    @ApiModelProperty(value = "数据来源类型：1 飞控AI提取   2 飞控人工提报 3 比对系统")
    private Integer dataOrigin;

    @ApiModelProperty(value = "工单编号")
    private String billNumber;

    @ApiModelProperty(value = "工单ID")
    private Long billId;

    @ApiModelProperty(value = "是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）")
    private Integer generateBill;


    @ApiModelProperty(value = "推送时间")
    private String pushTime;

    @ApiModelProperty(value = "是否可以重新推送 0否 1是")
    private Integer pushable;

}
