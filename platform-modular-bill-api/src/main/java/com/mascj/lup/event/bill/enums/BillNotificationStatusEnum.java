package com.mascj.lup.event.bill.enums;

import lombok.Getter;

/**
 *
 * 状态：0 未推给中心 1已经推给中心
 * <AUTHOR>
 * @date 2024/10/12 09:40
 * @describe
 */
@Getter
public enum BillNotificationStatusEnum {

    UNPushToLupDataCenter(0,"未推给中心"),PushedToLupDataCenter(1,"已经推给中心");

    private int code;
    private String value;
    BillNotificationStatusEnum(int code,String value){
        this.code = code;
        this.value = value;
    }

    public static BillNotificationStatusEnum parse(int value) {
        for (BillNotificationStatusEnum item : values()) {
            if (item.getCode() == value) {
                return item;
            }
        }
        return null;
    }
}
