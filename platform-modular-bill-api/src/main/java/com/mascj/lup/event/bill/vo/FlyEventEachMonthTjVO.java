package com.mascj.lup.event.bill.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class FlyEventEachMonthTjVO implements Serializable {

    public FlyEventEachMonthTjVO(){

    }
    public FlyEventEachMonthTjVO(String month){
            this.month = month;
    }
    @ApiModelProperty("月")
    private String month;

   private List<FlyEventMonthTjVO> flyEventMonthTjVOList = new ArrayList<>();

}
