package com.mascj.lup.event.bill.dto;

import cn.hutool.core.util.ObjectUtil;
import com.mascj.kernel.database.entity.Search;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/26 15:19
 * @describe
 */
@Data
@ApiModel(value = "事件图片查询参数")
public class QueryBillPictureInfoDTO extends Search {

    /**
     * 1 处置系统
     * 2 中台
     * 3 飞控
     */
    @ApiModelProperty(value = "来源类型：1 处置系统 2 中台 3 飞控",required = true)
    @NotNull
    @NotEmpty
    private Integer originType;

    @ApiModelProperty(value = "流程阶段")
    private List<Integer> flowStateList;

    /**
     * 事件ID
     */
    @ApiModelProperty(value = "事件ID")
    private Long eventId;

    /**
     * 飞行任务ID
     */
    @ApiModelProperty(value = "飞行任务ID")
    private Long flyTaskId;


    /**
     * {"dateType":"month","monthStart":"2024-11","monthEnd":"2024-11"}
     * {"dateType":"date","monthStart":"2024-11-26","monthEnd":"2024-11-26"}
     * {"dateType":"date","monthStart":"","monthEnd":""}
     */
    @ApiModelProperty(value = "数据类型 month 按月查询 date 按日期查询  空 所有 ")
    private String dateType;

    public String getDateType() {
        //全量数据
        if(ObjectUtil.isEmpty(this.monthStart)){
            dateType = null;
        }
        return dateType;
    }

    @ApiModelProperty(value = "开始时间")
    private String monthStart;

    @ApiModelProperty(value = "结束时间")
    private String monthEnd;
}
