package com.mascj.lup.event.bill.enums;

import lombok.Getter;

/**
 * 事件比对类型：1 单期比对 2 两期比对
 */
@Getter
public enum EventCompareType {

    SingleCompareType("单期比对",1),DoubleCompareType("两期比对",2);

    EventCompareType(String name, int value){
        this.name=name;
        this.value = value;
    }
    private int value;
    private String name;


    public static EventCompareType parse(int value) {
        for (EventCompareType item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }
}
