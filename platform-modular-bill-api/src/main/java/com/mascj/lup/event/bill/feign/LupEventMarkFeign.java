package com.mascj.lup.event.bill.feign;


import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.vo.EventDataVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "lup-event-bill-server",path = "/provider/v1/lupEventMarkFeign")
public interface LupEventMarkFeign {

    @DeleteMapping(value = "/deleteByMarkId")
    Result deleteByMarkId(@RequestParam("markId") Long markId);


    @DeleteMapping(value = "/deleteByMarkIds")
    Result deleteByMarkIds(@RequestParam("markIds") List<Long> markIds ,@RequestParam("tenantId") Long tenantId);


}
