package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.lup.event.bill.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "工单统计数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BillCountVO {

    @ApiModelProperty(value = "阶段状态名称")
    private String flowStateName;

    @ApiModelProperty(value = "阶段状态")
    private Integer flowState;

    @ApiModelProperty(value = "工单数量")
    private Long billCount;

}
