package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.kernel.database.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工单同步日志
 */
@Data
@TableName("data_sync_log")
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(value = "工单同步日志")
public class DataSyncLog extends BaseEntity {

    /**
     * 唯一标识符
     */
    @ApiModelProperty("唯一标识符")
    private String uuid;

    /**
     * 任务操作者
     */
    @ApiModelProperty("任务操作者")
    private Long userId;

    /**
     * 应用ID
     */
    @ApiModelProperty("应用ID")
    private long appId;
    /**
     * 批次代码
     */
    @ApiModelProperty("批次代码")
    private String batchCode;

    /**
     * 数据总量
     */
    @ApiModelProperty("数据总量")
    private int total;

    /**
     * 出错数量
     */
    @ApiModelProperty("出错数量")
    private int errCount;

    /**
     * 详情
     */
    @ApiModelProperty("详情")
    private String details;

    /**
     * 是否完成
     */
    @ApiModelProperty("是否完成")
    private boolean success;

    /**
     *  失败详情
     */
    @ApiModelProperty("失败详情")
    private String failedMessage;

    /**
     * 租户Id
     */
    @ApiModelProperty("租户Id")
    private Long tenantId;

}
