package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/23 10:05
 * @describe
 */
@Data()
@ApiModel(value = "工单查询统计参数")
public class BillQueryCountDTO {

    @ApiModelProperty(value = "租户ID",required = true)
    @NotNull
    private Long tenantId;

    @ApiModelProperty(value = "原始数据的唯一标识",required = true)
    @NotNull
    @NotEmpty
    private List<Long> originalDataLogoList;


}
