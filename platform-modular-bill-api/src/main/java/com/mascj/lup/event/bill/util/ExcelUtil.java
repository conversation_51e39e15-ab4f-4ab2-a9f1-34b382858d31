package com.mascj.lup.event.bill.util;

import com.alibaba.excel.EasyExcel;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

public class ExcelUtil {

	public static void writeExcel(HttpServletResponse response, List<?> list, String fileName, String sheetName, Class<?> clazz) {
		try {
			EasyExcel.write(getOutputStream(fileName, response), clazz)
				.sheet(sheetName)
				.doWrite(list);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 导出文件时为Writer生成OutputStream
	 *
	 * @param fileName
	 * @param response
	 * @return
	 */
	private static OutputStream getOutputStream(String fileName, HttpServletResponse response) throws Exception {
		try {
			fileName = URLEncoder.encode(fileName, "UTF-8");
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("utf8");
			response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xlsx");
			response.setHeader("Pragma", "public");
			response.setHeader("Cache-Control", "no-store");
			response.addHeader("Cache-Control", "max-age=0");
			return response.getOutputStream();
		} catch (IOException e) {
			throw new Exception(LocaleMessageUtil.getMessage(EventTipKey.FailExportExcel), e);
		}
	}
}
