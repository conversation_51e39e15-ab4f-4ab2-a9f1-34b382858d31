package com.mascj.lup.event.bill.base;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.lup.event.bill.util.EventDateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "展现对象基础类")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BaseDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;



}
