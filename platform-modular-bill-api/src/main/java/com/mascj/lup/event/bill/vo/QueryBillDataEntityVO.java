package com.mascj.lup.event.bill.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 任务目标：支持SaaS环境下金坛客户的事件对接需求
 *
 * 1、新加一个：接口服务约定权限编码 PreAuth("workbill.event.list")
 *
 * 2、功能启用前，需要为客户指定一个对接账号，最小授权上面权限编码
 *
 * 3、三方系统调用接口前，通过auth服务使用指定账号密码登录，获取token
 *
 * 4、接口关键参数包括但不限于：
 *
 *     query:  pagesize/page/startime/endtime
 *
 *     result:  eventId/localtion(lan,lat)/photos/typeCode/typeName/createTime
 *
 *
 * 选项
 * <AUTHOR>
 * @date 2024/1/3 16:11
 * @describe
 */
@Data
@ApiModel("查询")
public class QueryBillDataEntityVO {

    @ApiModelProperty("工单ID")
    private Long billId;

    @ApiModelProperty("工单编号")
    private String billNumber;
    @ApiModelProperty("机场名称")
    private String deviceName;

    @ApiModelProperty("标签名称")
    private String labelName;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("图片url列表")
    private String extraData;

    @ApiModelProperty("创建时间 格式：yyyy-MM-dd HH:mm:ss")
    private String createTime;

    @ApiModelProperty("事件描述")
    private String eventDesc;

    @ApiModelProperty("事件地址反解析位置")
    private String address;

}
