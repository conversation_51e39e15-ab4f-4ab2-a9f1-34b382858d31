package com.mascj.lup.event.bill.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/11 13:56
 * @describe
 */
@Data
public class FixEventDataSourceDTO {

    @ExcelProperty(value = "比对系统资源id")
    private Long dataSourceId;

    @ExcelProperty(value = "中台飞行任务id")
    private Long dataCenterFlyTaskPlanId;

    @ExcelProperty(value = "机场成果id")
    private Long flyTaskAchievementId;

    @ExcelProperty(value = "任务编号")
    private String taskNumber;

    @ExcelProperty(value = "瓦片地址")
    private String url;

    private String bounds;
}
