package com.mascj.lup.event.bill.geo;

import cn.hutool.json.JSONArray;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "图斑对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Geometry {

    @ApiModelProperty(value = "图斑类型",example = "Polygon")
    private String type;

    @ApiModelProperty(value = "图斑坐标")
    private JSONArray coordinates;
}
