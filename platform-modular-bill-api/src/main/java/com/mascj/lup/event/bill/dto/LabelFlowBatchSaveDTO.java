package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/12 17:06
 * @describe
 */
@Data
@ApiModel("流程标签保存参数")
public class LabelFlowBatchSaveDTO {

    @ApiModelProperty("标签ID List")
    private List<Long> labelIdList;

    @ApiModelProperty("流程状态List")
    private List<Integer> flowStateList;

    /**
     * 时间期限
     */
    @ApiModelProperty("时间期限")
    private BigDecimal timeLimit;

    /**
     *
     */
    @ApiModelProperty("时间期限开关")
    private Integer timeSwitch;


}
