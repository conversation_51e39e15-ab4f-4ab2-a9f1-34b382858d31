package com.mascj.lup.event.bill.entity;

import com.mascj.lup.event.bill.base.BaseBillEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/30 11:07
 * @describe
 */
@Data
@ApiModel(value = "工单挂起记录关联文件表")
public class LupBillPendingFile extends BaseBillEntity {

    @ApiModelProperty(value = "工单ID")
    private Long pendingId;

    @ApiModelProperty(value = "挂起文件ID")
    private Long fileId;

}
