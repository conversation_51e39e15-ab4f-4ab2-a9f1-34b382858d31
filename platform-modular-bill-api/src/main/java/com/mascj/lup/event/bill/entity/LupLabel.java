package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 事件标签 按名字去重
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_label")
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class LupLabel extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 删除状态：0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 租户id 
     */
    private Long tenantId;

    /**
     * 项目id  
     */
    private Long projectId;

    /**
     * 标签名称
     */
    private String name;

}
