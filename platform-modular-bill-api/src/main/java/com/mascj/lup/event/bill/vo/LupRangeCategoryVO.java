package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.kernel.web.tree.INode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 15:02
 * @describe
 */
@Data
public class LupRangeCategoryVO implements INode {
    private Long id;

    private Long parentId;

    private Long pid;

    /**
     * 范围类目对应的线的颜色
     */
    private String lineColor;

    /**
     * 范围线填充类型
     */
    private Integer fillType;

    public void setPid(Long pid){
        this.parentId = pid;
        this.pid = pid;
    }
    private String name;

    /**
     * 子孙节点
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<INode> children;

    /**
     * 是否有子孙节点
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean hasChildren;

    @Override
    public List<INode> getChildren() {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        return this.children;
    }
}
