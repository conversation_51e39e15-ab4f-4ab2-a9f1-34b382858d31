package com.mascj.lup.event.bill.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 *
 *
 * <AUTHOR> @since 2024-01-05 14:36:06
 */
@Data
public class LupLabelFlowVO {

    /**
     * 标签ID
     */
    @ApiModelProperty("标签ID")
    private Long labelId;

    /**
     * 标签名
     */
    @ApiModelProperty("标签名")
    private String labelName;

    /**
     * 时间期限数据
     */
    @ApiModelProperty("时间期限数据")
    private Map<Integer, LupLabelFlowTimeLimitVO> timeLimitData;
}