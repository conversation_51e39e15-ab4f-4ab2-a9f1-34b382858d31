package com.mascj.lup.event.bill.vo;

import com.mascj.lup.event.bill.enums.PendingStateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 16:24
 * @describe
 */
@Data
@ApiModel(value = "事件挂起返回数据项")
public class LupBillPendingDataItemVO {

    private String applyUserName;
    private String applyReason;
    private LocalDateTime createTime;
    private String approvalTime;
    private String approvalResult;
    private PendingStateEnum approvalResultState;
    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;
    private String approvalUserName;
    private List<String> fileList;
}
