package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/12 17:06
 * @describe
 */
@Data
@ApiModel("流程标签保存参数")
public class LabelFlowSaveDTO {

    @ApiModelProperty(" 数据主键 labelFlowId")
    private Long labelFlowId;

    @ApiModelProperty("标签ID")
    private Long labelId;

    @ApiModelProperty("流程状态")
    private Integer flowState;

    /**
     * 时间期限
     */
    @ApiModelProperty("时间期限")
    private BigDecimal timeLimit;

    /**
     * 时间期限开关
     */
    @ApiModelProperty("时间期限开关")
    private Integer timeSwitch;


}
