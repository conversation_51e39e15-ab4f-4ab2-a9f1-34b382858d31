package com.mascj.lup.event.bill.util.clip;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mascj.lup.event.bill.dto.MergePictureTileDTO;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: 靓马云
 * @description: 切割瓦片工具类
 * @author: 徐旻晨
 * @E-mail:<EMAIL>
 * @Phone：18114482486
 * @create: 2021-04-19 09:28
 **/
public class ClipTailsUtil {

    private static int iBuffer = 500;

//    public static void main(String[] args) {
//        try {
//            URL url = null;
//            int state = -1;
//            HttpURLConnection con;
//            url = new URL("https://nlmtiles.mascj.com/AHS/MASS/DTX/GuShuZhen/LingXuCun/Tile_20210114/21/1739527/854528.png");
//            con = (HttpURLConnection) url.openConnection();
//            state = con.getResponseCode();
//            if (state == 200) {
//                System.out.println("URL可用！");
//            }else {
//                System.out.println("URL不可用！");
//            }
//            System.out.println("111！");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
    /**
     * 获取默认缩放等级下切割好的图片的byte数组
     * @param json 图形数据
     * @param sTilePath 瓦片路径
     * @return 图片byte数组
     */
    public static byte[] getImgByte(String json, List sTilePath){
        return getImgByte(json,sTilePath,19,ClipTailsUtil.iBuffer);
    }
    /**
     * 获取切割好的图片的byte数组
     * @param json 图形数据
     * @param sTilePath 瓦片路径
     * @param iZoom 缩放等级
     * @return 图片byte数组
     */
    public static byte[] getImgByte(String json, List sTilePath, int iZoom,int iBuffer){
        ClipTailsUtil.iBuffer = iBuffer;
        ByteArrayOutputStream baot = new ByteArrayOutputStream();
        try {
            WShpGeo geo = ReadGeoJson(json);
            BufferedImage bufImage = ClipAnalysis(geo,sTilePath,iZoom);
            if(bufImage==null){
                return null;
            }
            ImageIO.write(bufImage, "JPEG", baot);
            byte[] bytes = baot.toByteArray();
            return bytes;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }finally {
            try {
                baot.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    public static BufferedImage getBufferedImage(String json, List sTilePath, int iZoom,int iBuffer,boolean isDashed){
        ClipTailsUtil.iBuffer = iBuffer;
        return getBufferedImage(json,sTilePath,iZoom,iBuffer,isDashed,null,true);
    }

    public static BufferedImage getBufferedImage(String json, List sTilePath, int iZoom,int iBuffer,boolean isDashed,String lineColor,boolean drawLine){
        ClipTailsUtil.iBuffer = iBuffer;

        try {
            WShpGeo geo = ReadGeoJson(json);
            BufferedImage bufImage = ClipAnalysis(geo,sTilePath,iZoom,isDashed,lineColor,drawLine);
            if(bufImage==null){
                return null;
            }
            return bufImage;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 得到最高缩放等级的base64位字符串的瓦片截取图
     * @param json 图形数据
     * @param sTilePath 瓦片地址数据
     * @return
     */
    public static String getEncodeString(String json, List<String> sTilePath){
     return getEncodeString(json,sTilePath,21);
    }

    /**
     * 得到base64位字符串的瓦片截取图
     * @param json 图形数据
     * @param sTilePath 瓦片地址数据
     * @param iZoom 缩放等级
     * @return
     */
    public static String getEncodeString(String json, List sTilePath, int iZoom){
        byte[] bytes = getImgByte(json,sTilePath,iZoom,ClipTailsUtil.iBuffer);

        return  Base64Encoder.encode(bytes);

    }

    public static boolean isValidURL(String url) {
        try {
            url = url.replace("/{z}/{x}/{y}.png","");
            URLConnection conn = new URL(url).openConnection();
            conn.connect();
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    public static boolean isValidURLCommon(String url) {
        try {

            URLConnection conn = new URL(url).openConnection();
            conn.connect();
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    public static List<MergePictureTileDTO> rebuildPathList(List<MergePictureTileDTO> sTilePath){

        List<MergePictureTileDTO> tileList = new ArrayList<>();

        sTilePath.forEach(tileUrl -> {

            if(isValidURL(tileUrl.getUrl())){

                String tail = "/{z}/{x}/{y}.png";
                if(!tileUrl.getUrl().contains(tail)){
                    tileUrl.setUrl(tileUrl.getUrl() + tail);
                }

                tileList.add(tileUrl);
            }

        });

        return tileList;
    }

    public static BufferedImage ClipAnalysis(WShpGeo Geo, List<MergePictureTileDTO> sTilePath, int iZoom, boolean isDashed)
    {
        return ClipAnalysis(Geo,sTilePath,iZoom,isDashed,null,true);
    }
    public static BufferedImage ClipAnalysis(WShpGeo Geo, List<MergePictureTileDTO> sTilePath, int iZoom, boolean isDashed,String lineColor,boolean drawLine)
    {
        try {
            sTilePath = rebuildPathList(sTilePath);

            WPoint LeftTopPoint = new WPoint(Geo.dMinX, Geo.dMaxY), RightDownPoint = new WPoint(Geo.dMaxX, Geo.dMinY);  // 经纬度 左上角点 和 右下角点
            LeftTopPoint = Wgs84ToWct(LeftTopPoint);
            RightDownPoint = Wgs84ToWct(RightDownPoint);
            // 经纬度 转换成 墨卡托平面坐标
            LeftTopPoint.X = (LeftTopPoint.X < (iBuffer - 20037508.34)) ? -20037508.34 : (LeftTopPoint.X - iBuffer);// 左上角点缓冲
            LeftTopPoint.Y = (LeftTopPoint.Y > (20037508.34 - iBuffer)) ? 20037508.34 : (LeftTopPoint.Y + iBuffer);
            RightDownPoint.X = (RightDownPoint.X > (20037508.34 - iBuffer)) ? 20037508.34 : (RightDownPoint.X + iBuffer);// 右下角点缓冲
            RightDownPoint.Y = (RightDownPoint.Y < (iBuffer - 20037508.34)) ? -20037508.34 : (RightDownPoint.Y - iBuffer);
            WRowCol LeftTopRC = WatCalcRowCol(LeftTopPoint, iZoom);
            WRowCol RightDownRC = WatCalcRowCol(RightDownPoint, iZoom); // 坐标 转换成 行列号
            int iColCount = RightDownRC.Col - LeftTopRC.Col==0? RightDownRC.Col - LeftTopRC.Col + 2: RightDownRC.Col - LeftTopRC.Col + 1;
            int iRowCount = RightDownRC.Row - LeftTopRC.Row==0?RightDownRC.Row - LeftTopRC.Row+2:RightDownRC.Row - LeftTopRC.Row + 1;// 获取瓦片小块的区间范围
            BufferedImage imageNew = new BufferedImage(iColCount * 256+1, iRowCount * 256+1 ,
                    BufferedImage.TYPE_INT_RGB);
            //拼接多张图片
            int AnchorX = 0;
            int AnchorY = 0;
            // 拼接多幅瓦片成一个
            for (int i = 0; i < iColCount; ++i)
            {
                AnchorY = 0;
                for (int j = 0; j < iRowCount; ++j)
                {
                    String sTileCol = String.valueOf(i + LeftTopRC.Col), sTileRow = String.valueOf(j + LeftTopRC.Row);
                    for (int k = sTilePath.size() - 1; k >= 0; --k) // 输入瓦片为多个时，倒序进行判断和裁切
                    {

                        MergePictureTileDTO tileDTO = sTilePath.get(k);

                        if(tileDTO.getFlipFlag() ==1)
                        {
                            // 计算 2 的 20 次方（直接使用位移运算）
                            Integer powerOfTwo = 1 << 20;

                            // 逐步计算结果
                            Integer result = powerOfTwo - 1;  // 2^20 - 1

                            sTileRow = (result-Integer.parseInt(sTileRow))+"";
                        }

//                        String sFilePath = sTilePath[k] + "/" + String.valueOf(iZoom) + "/" + sTileCol + "/" + sTileRow + ".png";
                        String sFilePath = tileDTO.getUrl().replaceAll("\\{z}", String.valueOf(iZoom)).replaceAll("\\{x}",sTileCol).replaceAll("\\{y}",
//                                (tileDTO.getFlipFlag() == 1 ?"-":"")+
                                sTileRow);
                        System.out.println("地址：" + sFilePath);
                        URL url = new URL(sFilePath);
                        HttpURLConnection con = (HttpURLConnection) url.openConnection();
                        if(con.getResponseCode()!=200){
                            continue;
//                            return null;
                        }
                        if(!isValidURLCommon(sFilePath))continue;

                        try {
                            BufferedImage bufImage = ImageIO.read(url);
                            final int width = bufImage.getWidth();
                            final int height = bufImage.getHeight();
                            //读取数据
                            int[] rgbs = bufImage.getRGB(0, 0, width, height, null, 0, width);
                            //装配上新的数据
                            imageNew.setRGB(AnchorX, AnchorY, width, height, rgbs, 0, width);
                        }catch (Exception exp){
                            System.out.println("图片文件损坏，地址：" + url);
                            exp.printStackTrace();
                            continue;
                        }
                    }
                    AnchorY += 256;
                }
                AnchorX += 256;
            }
            if(drawLine) {
                // 绘制矢量图形
                WPoint Anchor = TileCalcWat(LeftTopRC, iZoom); //左上角锚点
                double units = 3.6448014725465327501296997070313e-5 * Math.pow(2, 32 - iZoom);     // 一像素代表的距离
                Graphics2D graph = imageNew.createGraphics();

                if (ObjectUtil.isNotEmpty(lineColor)) {
                    graph.setColor(Color.decode(lineColor));
                } else {
                    graph.setColor(Color.RED);
                }

                graph.setStroke(new BasicStroke(3.0f));
                if (isDashed) {
                    float[] dash = {10.0f}; // 虚线的长度和间隔
                    BasicStroke dashedStroke = new BasicStroke(3.0f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER, 10.0f, dash, 0.0f);
                    graph.setStroke(dashedStroke);
                }


            WRowCol LT = new WRowCol(0, 0); WRowCol RB = new WRowCol(0, 0); //矢量范围在 SpliceBitMap 中的像素范围
            for (int i = 0; i < Geo.geo.size(); ++i)
            {
                List<WPoint> Points = Geo.geo.get(i);
                int[] xPoints = new int[Points.size()];
                int[] yPoints = new int[Points.size()];
                for (int j = 0; j < Points.size(); ++j)
                {
                    WPoint TempPoint = Wgs84ToWct(Points.get(j));
                    int x = (int)(Math.abs(TempPoint.X - Anchor.X) / units);
                    int y = (int)(Math.abs(TempPoint.Y - Anchor.Y) / units);
                    xPoints[j] = x;
                    yPoints[j] = y;
                    // 框出矢量图斑在合并的瓦片图片中的像素区间
                    LT.Row = LT.Row > y ? y : LT.Row; LT.Col = LT.Col > x ? x : LT.Col;
                    RB.Row = RB.Row < y ? y : RB.Row; RB.Col = RB.Col < x ? x : RB.Col;
                }
                graph.drawPolyline(xPoints,yPoints,Points.size());
            }
            //释放画布资源
            graph.dispose();

            int BufferCell = (int) Math.ceil(iBuffer / units); //缓冲像素宽度
            LT.Col = LT.Col < BufferCell ? 0 : LT.Col - BufferCell; LT.Row = LT.Row < BufferCell ? 0 : LT.Row - BufferCell;
            RB.Col = RB.Col > imageNew.getWidth() - BufferCell ? imageNew.getWidth() : RB.Col + BufferCell;
            RB.Row = RB.Row > imageNew.getHeight() - BufferCell ? imageNew.getHeight() : RB.Row + BufferCell;

            imageNew.getSubimage(LT.Col,LT.Row,RB.Col - LT.Col + 1,RB.Row - LT.Row + 1 );
            }
            return imageNew;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static BufferedImage ClipAnalysis(WShpGeo Geo, List<MergePictureTileDTO> sTilePath, int iZoom)
    {
        try {
            sTilePath = rebuildPathList(sTilePath);

            WPoint LeftTopPoint = new WPoint(Geo.dMinX, Geo.dMaxY), RightDownPoint = new WPoint(Geo.dMaxX, Geo.dMinY);  // 经纬度 左上角点 和 右下角点
            LeftTopPoint = Wgs84ToWct(LeftTopPoint);
            RightDownPoint = Wgs84ToWct(RightDownPoint);
            // 经纬度 转换成 墨卡托平面坐标
            LeftTopPoint.X = (LeftTopPoint.X < (iBuffer - 20037508.34)) ? -20037508.34 : (LeftTopPoint.X - iBuffer);// 左上角点缓冲
            LeftTopPoint.Y = (LeftTopPoint.Y > (20037508.34 - iBuffer)) ? 20037508.34 : (LeftTopPoint.Y + iBuffer);
            RightDownPoint.X = (RightDownPoint.X > (20037508.34 - iBuffer)) ? 20037508.34 : (RightDownPoint.X + iBuffer);// 右下角点缓冲
            RightDownPoint.Y = (RightDownPoint.Y < (iBuffer - 20037508.34)) ? -20037508.34 : (RightDownPoint.Y - iBuffer);
            WRowCol LeftTopRC = WatCalcRowCol(LeftTopPoint, iZoom);
            WRowCol RightDownRC = WatCalcRowCol(RightDownPoint, iZoom); // 坐标 转换成 行列号
            int iColCount = RightDownRC.Col - LeftTopRC.Col==0? RightDownRC.Col - LeftTopRC.Col + 2: RightDownRC.Col - LeftTopRC.Col + 1;
            int iRowCount = RightDownRC.Row - LeftTopRC.Row==0?RightDownRC.Row - LeftTopRC.Row+2:RightDownRC.Row - LeftTopRC.Row + 1;// 获取瓦片小块的区间范围
            BufferedImage imageNew = new BufferedImage(iColCount * 256+1, iRowCount * 256+1 ,
                    BufferedImage.TYPE_INT_RGB);
            //拼接多张图片
            int AnchorX = 0;
            int AnchorY = 0;
            // 拼接多幅瓦片成一个
            for (int i = 0; i < iColCount; ++i)
            {
                AnchorY = 0;
                for (int j = 0; j < iRowCount; ++j)
                {
                    String sTileCol = String.valueOf(i + LeftTopRC.Col), sTileRow = String.valueOf(j + LeftTopRC.Row);
                    for (int k = sTilePath.size() - 1; k >= 0; --k) // 输入瓦片为多个时，倒序进行判断和裁切
                    {
                        MergePictureTileDTO tileDTO =sTilePath.get(k);
//                        String sFilePath = sTilePath[k] + "/" + String.valueOf(iZoom) + "/" + sTileCol + "/" + sTileRow + ".png";
                        String sFilePath = tileDTO.getUrl().replaceAll("\\{z}", String.valueOf(iZoom)).replaceAll("\\{x}",sTileCol).replaceAll("\\{y}",
                                (tileDTO.getFlipFlag() == 1 ?"-":"")+
                                sTileRow);
                        URL url = new URL(sFilePath);
                        HttpURLConnection con = (HttpURLConnection) url.openConnection();
                        if(con.getResponseCode()!=200){
                            continue;
//                            return null;
                        }
                        if(!isValidURLCommon(sFilePath))continue;

                        BufferedImage bufImage = ImageIO.read(url);
                        final int width = bufImage.getWidth();
                        final int height = bufImage.getHeight();
                        //读取数据
                        int[] rgbs = bufImage.getRGB(0, 0, width, height, null, 0, width);
                        //装配上新的数据
                        imageNew.setRGB(AnchorX,AnchorY,width, height,rgbs,0,width);
                    }
                    AnchorY += 256;
                }
                AnchorX += 256;
            }
            // 绘制矢量图形
            WPoint Anchor = TileCalcWat(LeftTopRC, iZoom); //左上角锚点
            double units = 3.6448014725465327501296997070313e-5 * Math.pow(2, 32 - iZoom);     // 一像素代表的距离
            Graphics2D graph = imageNew.createGraphics();
            graph.setColor(Color.YELLOW);
            graph.setStroke(new BasicStroke(3.0f));
            WRowCol LT = new WRowCol(0, 0); WRowCol RB = new WRowCol(0, 0); //矢量范围在 SpliceBitMap 中的像素范围
            for (int i = 0; i < Geo.geo.size(); ++i)
            {
                List<WPoint> Points = Geo.geo.get(i);
                int[] xPoints = new int[Points.size()];
                int[] yPoints = new int[Points.size()];
                for (int j = 0; j < Points.size(); ++j)
                {
                    WPoint TempPoint = Wgs84ToWct(Points.get(j));
                    int x = (int)(Math.abs(TempPoint.X - Anchor.X) / units);
                    int y = (int)(Math.abs(TempPoint.Y - Anchor.Y) / units);
                    xPoints[j] = x;
                    yPoints[j] = y;
                    // 框出矢量图斑在合并的瓦片图片中的像素区间
                    LT.Row = LT.Row > y ? y : LT.Row; LT.Col = LT.Col > x ? x : LT.Col;
                    RB.Row = RB.Row < y ? y : RB.Row; RB.Col = RB.Col < x ? x : RB.Col;
                }
                graph.drawPolyline(xPoints,yPoints,Points.size());
            }
            //释放画布资源
            graph.dispose();
            int BufferCell = (int) Math.ceil(iBuffer / units); //缓冲像素宽度
            LT.Col = LT.Col < BufferCell ? 0 : LT.Col - BufferCell; LT.Row = LT.Row < BufferCell ? 0 : LT.Row - BufferCell;
            RB.Col = RB.Col > imageNew.getWidth() - BufferCell ? imageNew.getWidth() : RB.Col + BufferCell;
            RB.Row = RB.Row > imageNew.getHeight() - BufferCell ? imageNew.getHeight() : RB.Row + BufferCell;

            imageNew.getSubimage(LT.Col,LT.Row,RB.Col - LT.Col + 1,RB.Row - LT.Row + 1 );
            return imageNew;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /// <summary>
    /// 读取 GeoJson 数据
    /// </summary>
    private static WShpGeo ReadGeoJson(String json)
    {
        try
        {
            WShpGeo shpGeo = new WShpGeo();
            shpGeo.dMaxX = -180;
            shpGeo.dMaxY = -180;
            shpGeo.dMinX = 180;
            shpGeo.dMinY = 180;
            JSONObject obj = JSON.parseObject(json);
            switch (obj.get("type").toString())
            {
                case "Point": break;//{"type":"Point","coordinates":[100.0,0.0]}
                case "MultiPoint": break;//{"type":"MultiPoint","coordinates":[[100.0,0.0],[101.0,1.0]]}
                case "LineString": break;//{"type":"LineString","coordinates":[[100.0,0.0],[101.0,1.0]]}
                case "MultiLineString": break;//{"type":"MultiLineString","coordinates":[[[100.0,0.0],[101.0,1.0]],[[102.0,2.0],[103.0,3.0]]]}
                case "MultiPolygon": break;//{"type":"MultiPolygon","coordinates":[[[[102.0,2.0],[103.0,2.0],[103.0,3.0],[102.0,3.0],[102.0,2.0]]],[[[100.0,0.0],[101.0,0.0],[101.0,1.0],[100.0,1.0],[100.0,0.0]],[[100.2,0.2],[100.8,0.2],[100.8,0.8],[100.2,0.8],[100.2,0.2]]]]}
                case "Polygon"://{"type":"Polygon","coordinates":[[[100.0,0.0],[101.0,0.0],[101.0,1.0],[100.0,1.0],[100.0,0.0]]]}
                    //{"type":"Polygon","coordinates":[[[100.0,0.0],[101.0,0.0],[101.0,1.0],[100.0,1.0],[100.0,0.0]],[[100.2,0.2],[100.8,0.2],[100.8,0.8],[100.2,0.8],[100.2,0.2]]]}
                {
                    List<List<WPoint>> LGeoList = new ArrayList();
                    JSONArray xyArray = obj.getJSONArray("coordinates"); //[[[100.0,0.0],[101.0,0.0],[101.0,1.0],[100.0,1.0],[100.0,0.0]],[[100.2,0.2],[100.8,0.2],[100.8,0.8],[100.2,0.8],[100.2,0.2]]]
                    for (int i = 0; i < xyArray.size(); ++i)
                    {
                        List<WPoint> LPartList = new ArrayList();
                        JSONArray xyArray1 = (JSONArray)xyArray.get(i); //[[100.0,0.0],[101.0,0.0],[101.0,1.0],[100.0,1.0],[100.0,0.0]]
                        for (int j = 0; j < xyArray1.size(); ++j)
                        {
                            JSONArray xyArray2 = (JSONArray)xyArray1.get(j); //[100.0,0.0]
                            double X = Double.valueOf(xyArray2.get(0).toString()); double Y = Double.valueOf(xyArray2.get(1).toString());
                            LPartList.add(new WPoint(X, Y));
                            if (X > shpGeo.dMaxX) shpGeo.dMaxX = X; if (Y > shpGeo.dMaxY) shpGeo.dMaxY = Y;
                            if (X < shpGeo.dMinX) shpGeo.dMinX = X; if (Y < shpGeo.dMinY) shpGeo.dMinY = Y;
                        }
                        LGeoList.add(LPartList);
                    }
                    shpGeo.geo = LGeoList;
                    break;
                }
                default: break;
            }
            return shpGeo;
        }
        catch (Exception e){
            e.printStackTrace();
            return null;
        }

    }
    // 根据墨卡托坐标 求算 瓦片行列号,点位于瓦片内，算出以左上角为锚点的瓦片行列号
    private static WRowCol WatCalcRowCol(WPoint sou, int zoom)
    {
        //坐标换算到以左上角为原点，没有正负之分
        sou.X = sou.X+20037508.34;
        sou.Y = 20037508.34 - sou.Y;
        // 计算当前缩放级别下一像素代表多少米,和一片瓦片代表多大
        double a = Math.pow(2, 32 - zoom);
        double units = 3.6448014725465327501296997070313e-5 * a;
        double span = units * 256;
        // 当前坐标所在的行列号
        Integer col = (int)(sou.X / span);
        Integer row = (int)(sou.Y / span);
        WRowCol Tag = new WRowCol(row, col);
        return Tag;
    }
    // 根据瓦片行列号 求算 墨卡托坐标，算出瓦片左上角坐标
    private static WPoint TileCalcWat(WRowCol sou, int zoom)
    {
        // 计算当前缩放级别下一像素代表多少米,和一片瓦片代表多大
       double a = Math.pow(2, (32 - zoom));
       double units = 3.6448014725465327501296997070313e-5 * a;
       double span = units * 256;

       Double x = sou.getCol() * span;
       Double y = sou.getRow() * span;

       x -= 20037508.34;
       y = 20037508.34 - y;
       WPoint Tag = new WPoint(x, y);
       return Tag;
    }
    // Wgs1984坐标（EPSG4326）转 墨卡托坐标（EPSG3857）
    private static WPoint Wgs84ToWct(WPoint sou)
    {
        Double x = sou.X * 20037508.34 / 180;
        Double y = Math.log(Math.tan((90 + sou.Y) * Math.PI / 360)) * 180 / Math.PI;
        y = y * 20037508.34 / 180;
        WPoint Tag = new WPoint(x, y);
        return Tag;
    }

    // 墨卡托坐标（EPSG3857）转 Wgs1984坐标（EPSG4326）
    private static WPoint WctToWgs84(WPoint sou)
    {
        Double x = sou.getX() / 20037508.34 * 180;
        Double y = sou.getY() / 20037508.34 * 180;
        y = 180 / Math.PI * (2 * Math.atan(Math.exp(y * Math.PI / 180)) - Math.PI / 2);
        WPoint Tag = new WPoint(x, y);
        return Tag;
    }

}
