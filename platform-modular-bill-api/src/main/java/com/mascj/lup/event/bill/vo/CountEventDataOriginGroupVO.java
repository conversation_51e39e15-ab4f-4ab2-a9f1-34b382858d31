package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:29
 * @describe
 */
@Data
@ApiModel(value = "统计事件数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CountEventDataOriginGroupVO {

    @ApiModelProperty("本周提取方式数据统计列表")
    private List<CountEventDataOriginVO> originDataFetchWayListForWeek;

    @ApiModelProperty("本月提取方式数据统计列表")
    private List<CountEventDataOriginVO> originDataFetchWayListForMonth;

    @ApiModelProperty("本年提取方式数据统计列表")
    private List<CountEventDataOriginVO> originDataFetchWayListForYear;

}
