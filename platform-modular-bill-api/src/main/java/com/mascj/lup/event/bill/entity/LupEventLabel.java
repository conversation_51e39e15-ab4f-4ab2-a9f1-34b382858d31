package com.mascj.lup.event.bill.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 *
 *
 * <AUTHOR> @since 2024-01-05 14:36:06
 */
@Data
@TableName("lup_event_label")
@ApiModel(value = "LupEventLabel", description = " ")
public class LupEventLabel implements Serializable{

    private static final long serialVersionUID=1L;

    /**
     *
     */
    private Long id;

    /**
     *
     */
    @ApiModelProperty(" ")
    private Long labelId;
    @ApiModelProperty("类型 1：组织机构 2：用户角色 3：用户信息")
    private Integer type;

    @ApiModelProperty("组织id")
    private Long orgId;

    /**
     *
     */
    @ApiModelProperty("角色id")
    private Long roleId;

    /**
     *
     */
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     *  租户Id
     */
    @ApiModelProperty("租户Id")
    private Long tenantId;

    /**
     *  创建人
     */
    @ApiModelProperty("创建人")
    private Long createBy;

    /**
     *  创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     *  更新人
     */
    @ApiModelProperty("更新人")
    private Long updateBy;

    /**
     *  更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     *  逻辑删除
     */
    @ApiModelProperty("逻辑删除")
    @TableLogic
    private Integer deleted;

}