package com.mascj.lup.event.bill.feign;

import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.EventDataDTO;
import com.mascj.lup.event.bill.dto.ExportDataBaseDTO;
import com.mascj.lup.event.bill.dto.FlySystemDelDTO;
import com.mascj.lup.event.bill.dto.FlySystemEventDataSourceDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@Api(value = "导出数据",tags = "导出数据")
@FeignClient(value = "lup-event-bill-server")
public interface IDataExportProvider {
    @ApiOperation(value = "导出数据")
    @PostMapping("/provider/support/exportData")
    Result exportData(@RequestBody Map<String,Object> exportDataBaseDTO);

}
