package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/6 17:35
 * @describe
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(value = "工单扩展数据")
public class BillExtendData {

    @ApiModelProperty(value = "当前图片地址")
    private String currentPictureUrl;

    @ApiModelProperty(value = "当前图片文件Id")
    private Long currentPictureFileId;

    @ApiModelProperty(value = "对比图片地址")
    private String comparePictureUrl;

    @ApiModelProperty(value = "合并的图片文件id")
    private Long comparePictureFileId;

    @ApiModelProperty(value = "合并的图片地址")
    private String mergePictureUrl;

    @ApiModelProperty(value = "合并的图片文件id")
    private Long mergePictureFileId;
}
