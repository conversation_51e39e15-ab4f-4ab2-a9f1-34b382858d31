package com.mascj.lup.event.bill.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.lup.event.bill.entity.LupEventSmsGrade;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> @since 2024-01-05 14:36:06
 */
@Data
public class LupEventLabelVo {


    /**
     *
     */
    @ApiModelProperty(" ")
    private Long labelId;


    /**
     *
     */
    @ApiModelProperty("标签名")
    private String labelName;

    /**
     *
     */
    @ApiModelProperty("类型 1：组织机构 2：用户角色 3：用户信息")
    private Integer type;

    /**
     *
     */
    @ApiModelProperty("组织id")
    private List<Long> orgIds;

    private List<String> orgNames = new ArrayList<>();

    /**
     *
     */
    @ApiModelProperty("角色id")
    private List<Long> roleIds;
    private List<String> roleNames = new ArrayList<>();


    /**
     *
     */
    @ApiModelProperty("用户id")
    private List<Long> userIds = new ArrayList<>();

    private List<String> userNames = new ArrayList<>();

    private LupEventSmsGrade lupEventSmsGrade;
}