package com.mascj.lup.event.bill.util;

import org.apache.commons.fileupload.FileItem;
import org.springframework.lang.Nullable;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;

public class CommonsMultipartFile implements MultipartFile, Serializable {

    private final FileItem fileItem;
    private final long size;
    private boolean preserveFilename = false;

    public CommonsMultipartFile(FileItem fileItem) {
        this.fileItem = fileItem;
        this.size = this.fileItem.getSize();
    }

    public String getName() {
        return this.fileItem.getFieldName();
    }

    public String getOriginalFilename() {
        String filename = this.fileItem.getName();
        if (filename == null) {
            return "";
        } else if (this.preserveFilename) {
            return filename;
        } else {
            int unixSep = filename.lastIndexOf(47);
            int winSep = filename.lastIndexOf(92);
            int pos = winSep > unixSep ? winSep : unixSep;
            return pos != -1 ? filename.substring(pos + 1) : filename;
        }
    }

    @Nullable
    @Override
    public String getContentType() {
        return fileItem.getContentType();
    }

    @Override
    public boolean isEmpty() {
        return false;
    }

    @Override
    public long getSize() {
        return size;
    }

    @Override
    public byte[] getBytes() throws IOException {
        return fileItem.get();
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return fileItem.getInputStream();
    }

    @Override
    public void transferTo(File file) throws IOException, IllegalStateException {

    }

}

