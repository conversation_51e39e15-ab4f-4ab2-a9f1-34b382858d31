package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.lup.event.bill.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "标签项")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LabelItemVO extends BaseVO {

    @ApiModelProperty(value = "标签名称")
    private String labelName;

}
