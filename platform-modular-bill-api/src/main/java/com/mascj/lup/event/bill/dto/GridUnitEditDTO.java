package com.mascj.lup.event.bill.dto;

import com.mascj.lup.event.bill.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "网格参数")
@Data
public class GridUnitEditDTO extends BaseDTO {

    @ApiModelProperty(value = "网格名称" ,required = true)
    private String gridName;

    @ApiModelProperty(value = "网格排序" ,required = true)
    private int sort;

}
