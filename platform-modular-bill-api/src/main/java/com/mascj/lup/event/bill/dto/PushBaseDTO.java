package com.mascj.lup.event.bill.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/3 18:05
 * @describe
 */
@Data
public class PushBaseDTO {

    //{"stageName":"KQTranslateService","appKey":"923d094315154f35a507e9b2c1179e03","appSecret":"1ef15cc00b364a27a533707391d7b28f","pushPath":"/video-fusion/upnotify/commonReport","pushPathCode":488,"host":"http://**************:7002",open:true}
    /**
     * 策略
     * 杨汛桥：YXQTranslateService
     * 广汉：GHTranslateService
     * 默认：DefaultTranslateService
     */
    private String stageName;

    /**
     * 推送开关对应push_auto 默认false
     */
    private Boolean open;

    /**
     * 自动推送 默认false 关闭 true代表自动推送
     */
    private Boolean autoPush;

    /**
     * 对应流程
     * 推送事件的流程 默认通用流程 DefaultFlow、推送关联流程（杨汛桥项目的事件流程）PushEventFlow
     */
    private String pushEventFlow;

    /**
     * 事件甄别标识
     * 推送甄别的数据 1，推送全量数据（包含已甄别和未甄别）2，默认推送已甄别的数据
     */
    private Integer generateBillFlag;

    /**
     * 地理位置反解析密钥
     */
    private String geoLocationKey;

    /**
     * 重复推送  默认 false 可以设置为 允许重复推  重复推的情况下 需要 客户接收位置做好数据保存和更新的操作
     */
    private Boolean pushRepeat = Boolean.FALSE;
}
