package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.lup.event.bill.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "网格展示出参")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class GriUnitVO extends BaseVO {

    @ApiModelProperty(value = "网格编码")
    private String gridCode;

    @ApiModelProperty(value = "网格名称")
    private String gridName;

    @ApiModelProperty(value = "网格图形")
    private String shape;

    @ApiModelProperty(value = "网格中心点")
    private String center;

    @ApiModelProperty(value = "事件数量")
    private int eventAmount;

    @ApiModelProperty(value = "顺序")
    private int sort;

    @ApiModelProperty(value = "子集数据")
    private List<GriUnitVO> children;

    @ApiModelProperty(value = "是否为叶子节点")
    private boolean lastNode;
}
