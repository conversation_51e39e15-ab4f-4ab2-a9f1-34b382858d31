package com.mascj.lup.event.bill.feign;


import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.vo.EventDataVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "lup-datacenter-server",path = "/provider/v1/mqtt-notification/")
public interface LupDataNotificationFeign {

    @PostMapping(value = "/eventDataOnChanged")
    Object eventDataOnChanged(@RequestBody Result<EventDataVO> data);

    @PostMapping(value = "/eventDataCountOnChanged")
    Object eventDataCountOnChanged();

}
