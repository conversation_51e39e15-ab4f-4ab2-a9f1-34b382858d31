package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/29 09:30
 * @describe
 */
@Data
@TableName("lup_range_line")
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class LupRangeLine extends BaseEntity {
    /**
     * 删除状态：0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * range_category 表主键 id
     */
    private Long rangeCategoryId;

    /**
     * 面积
     */
    private String area;

    /**
     * 备注
     */
    private String remark;

    /**
     * 范围中心点  纬度
     */
    private BigDecimal lat;

    /**
     * 范围中心点  经度
     */
    private BigDecimal lng;

    /**
     * 属性
     */
    private String properties;

    /**
     * 范围数据
     */
    private String geometry;
}
