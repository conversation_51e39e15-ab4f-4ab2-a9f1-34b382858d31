package com.mascj.lup.event.bill.base;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.mascj.kernel.database.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/30 11:07
 * @describe
 */
@Data
@ApiModel(value = "基础工单实体")
public class BaseBillEntity extends BaseEntity {

    /**
     * 删除状态：0未删除 1已删除
     */
    @TableLogic
    @ApiModelProperty(value = "删除状态")
    private Integer deleted;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

}
