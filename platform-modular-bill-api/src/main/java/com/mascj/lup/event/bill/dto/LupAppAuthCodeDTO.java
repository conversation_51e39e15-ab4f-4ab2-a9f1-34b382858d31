package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/1/13 16:27
 * @describe
 */
@Data
@ApiModel(value = "应用参数")
public class LupAppAuthCodeDTO {

    @ApiModelProperty(value = "授权码",required = true)
    @NotNull
    @NotBlank
    private String authCode;

}
