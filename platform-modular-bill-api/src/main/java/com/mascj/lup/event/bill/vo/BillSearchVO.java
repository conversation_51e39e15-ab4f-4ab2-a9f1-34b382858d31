package com.mascj.lup.event.bill.vo;

import com.mascj.lup.event.bill.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "事件工单返回数据")
public class BillSearchVO extends BaseVO{

    @ApiModelProperty(value = "事件编号")
    private String billNumber;

    @ApiModelProperty(value = "区域")
    private String areaName;

    @ApiModelProperty(value = "网格")
    private String gridName;

    @ApiModelProperty(value = "事件来源名称")
    private String eventOriginName;

    @ApiModelProperty(value = "事件来源")
    private String eventOrigin;

    @ApiModelProperty(value = "事件标签")
    private String eventLabel;
    @ApiModelProperty(value = "机场名称")
    private String deviceName;


    @ApiModelProperty(value = "处置方式")
    private Integer handleType;

    @ApiModelProperty(value = "处置方式")
    private String handleTypeName;

    private int dataOrigin;

    private BigDecimal locationLng;
    private BigDecimal locationLat;

    @ApiModelProperty(value = "占地面积")
    private BigDecimal locateArea;
    /**
     * 推送状态 0未推送   1已推送   -1推送失败
     */
    @ApiModelProperty(value = " 推送状态 0未推送   1已推送   -1推送失败")
    private Integer pushState;
    @ApiModelProperty(value = " 四平台处理结果 1处理成功  0 退回 2无需推送")
    private Integer fourSuccess;
    @ApiModelProperty(value = "推送失败信息")
    private String  pushMsg;

    /**
     * 距离工单位置
     */
    @ApiModelProperty(value = "距离工单位置")
    private int distance;
    /**
     * 距离工单位置, 单位米
     */
    @ApiModelProperty(value = "距离工单位置, 单位米")
    private String distanceString;

    @ApiModelProperty(value = "已读 true 未读 false")
    private boolean read;

    @ApiModelProperty(value = "流程id",required = true)
    private Long flowEntityId;

    @ApiModelProperty(value = "阶段任务ID")
    private Long processTaskId;

    @ApiModelProperty(value = "阶段任务到期时间")
    private String deadLine;

    @ApiModelProperty(value = "阶段名称")
    private Integer flowState;
    @ApiModelProperty(value = "阶段名称")
    private String flowStateName;

    @ApiModelProperty(value = "后期处理状态：0（未申请，需要展示后期处理的申请按钮） 1审批中（已经申请后期处理） 2审核通过（已经） 3已驳回（审核驳回了申请，需要展示后期处理的申请按钮）")
    private Integer pendingState;

    @ApiModelProperty(value = "是否ocr true 是ocr  为空或者false 不展示ocr标识")
    private Boolean ocr;

    private String extraData;
    private List<String> eventPicUrlList;


}
