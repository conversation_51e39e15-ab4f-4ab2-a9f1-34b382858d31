package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 *
 *
 * <AUTHOR> @since 2024-01-05 14:36:06
 */
@Data
@ApiModel("通用tree vo")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LupCommonTreeVO {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "父节点 parentId")
    private Long parentId;

    @ApiModelProperty(value = "值")
    private String value;

    @ApiModelProperty(value = "儿子节点")
    private List<LupCommonTreeVO> childrenList;

}