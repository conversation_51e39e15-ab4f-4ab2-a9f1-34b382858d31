package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
@Data
@ApiModel(description = "永安分类表")
public class YonganCategory implements Serializable {
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @ApiModelProperty("主键id")
    @TableId(
            value = "id",
            type = IdType.ASSIGN_ID
    )
    private Long id;


    @ApiModelProperty(value = "一级分类ID", required = true)
    private Integer categoryLevel1;

    @ApiModelProperty(value = "一级分类名称", required = true)
    private String categoryLevel1Name;

    @ApiModelProperty(value = "二级分类ID", required = true)
    private String categoryLevel2;

    @ApiModelProperty(value = "二级分类名称", required = true)
    private String categoryLevel2Name;

    @ApiModelProperty(value = "三级分类ID", required = true)
    private String categoryLevel3;

    @ApiModelProperty(value = "三级分类名称", required = true)
    private String categoryLevel3Name;

    @ApiModelProperty(value = "四级分类ID", required = true)
    private String categoryLevel4;

    @ApiModelProperty(value = "四级分类名称", required = true)
    private String categoryLevel4Name;


    @ApiModelProperty(value = "删除标识", example = "0")
    private String isDeleted;

    @ApiModelProperty(value = "租户ID", example = "0")
    private Long tenantId;


    @ApiModelProperty("创建人")
    @TableField(
            value = "create_by",
            fill = FieldFill.INSERT
    )
    private Long createBy;
    @ApiModelProperty("创建时间")
    @TableField(
            value = "create_time",
            fill = FieldFill.INSERT
    )
    private LocalDateTime createTime;
    @ApiModelProperty("更新人")
    @TableField(
            value = "update_by",
            fill = FieldFill.INSERT_UPDATE
    )
    private Long updateBy;
    @ApiModelProperty("更新时间")
    @TableField(
            value = "update_time",
            fill = FieldFill.UPDATE
    )
    private LocalDateTime updateTime;
}
