package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.lup.event.bill.base.BaseVO;
import com.mascj.lup.event.bill.geo.GeoTile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "工单详情")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BillDetailVO extends BaseVO{


    /**
     * 推送状态 0未推送   1已推送   -1推送失败
     */
    @ApiModelProperty(value = " 推送状态 0未推送   1已推送   -1推送失败")
    private Integer pushState;
    @ApiModelProperty(value = "推送失败信息")
    private String  pushMsg;

    /**
     * 数据类型：1图片 2瓦片 3视频 4其他
     */
    @ApiModelProperty(value = "数据类型：1图片 2瓦片 3视频 4其他")
    private Integer dataType;


    private Long dataId;

    /**
     * 比对类型：1 单期比对 2 两期比对
     */
    @ApiModelProperty(value = "比对类型：1 单期比对 2 两期比对")
    private Integer compareType;

    /**
     * 图形json
     */
    @ApiModelProperty(value = "图形json")
    private String shape;

    private  Map<String, Map<String, Object>> formData;
    /**
     * 启动表单
     */
    @ApiModelProperty(value = "启动表单")
    private Map<String, Object> startFormData;



    /**
     * 工单流程
     */
    @ApiModelProperty(value = "工单流程")
    private List<TaskVo> flowTask = new ArrayList<>();
    /**
     * 定位经度
     */
    @ApiModelProperty(value = "定位经度")
    private BigDecimal locationLng;

    /**
     * 定位维度
     */
    @ApiModelProperty(value = "定位纬度")
    private BigDecimal locationLat;

    @ApiModelProperty(value = "事件编号")
    private String billNumber;
    @ApiModelProperty(value = "审核按钮权限  true表示有  false表示没")
    private Boolean check;

    @ApiModelProperty(value = "事件状态")
    private String eventState;


    @ApiModelProperty(value = "processTaskId任务id")
    private String processTaskId;
    @ApiModelProperty(value = "processTaskId任务id")
    private String flowEntityId;



    /**
     * 下拉选择项：1 AI识别、2 人工提报、3 比对识别
     */
    @ApiModelProperty(value = "事件来源下拉选择项：1 AI识别、2 人工提报、3 比对识别")
    private String eventOrigin;

    @ApiModelProperty(value = "区域网格字段")
    private String areaGrid;

    @ApiModelProperty(value = "事件标签")
    private String eventLabel;

    @ApiModelProperty(value = "当前图片列表")
    private List<String> currentPictureList;

    @ApiModelProperty(value = "对比图片列表")
    private List<String> comparePictureList;

    @ApiModelProperty(value = "当前瓦片列表")
    private List<GeoTile> currentTileList;

    @ApiModelProperty(value = "对比瓦片列表")
    private List<GeoTile> compareTileList;

    @ApiModelProperty(value = "事件元 1 AI识别、2 人工提报、3 比对识别")
    private Integer dataOrigin;
    private Long currentBatchId;
    private Long compareBatchId;

    private String areaName;
    private String gridName;

    private Integer flowState;

    private String extraData;

    public ExtraDataVO extraDataVO;
    @ApiModelProperty(value = "事件占地面积")
    private BigDecimal locateArea;

    @ApiModelProperty(value = "标注是否存在")
    private boolean hasDimension = false;

    /**
     * 四平台处理结果 1处理成功  0 退回  2无需推送
     */
    private Integer fourSuccess;

    @ApiModelProperty(value = "飞行任务的id")
    private Long flyTaskId;
    @ApiModelProperty(value = "飞行任务的视频url")
    private String flyTaskVideoUrl;


    @ApiModelProperty(value = "已读 true 未读 false")
    private boolean read;

    @ApiModelProperty(value = "阶段任务到期时间")
    private String deadLine;

    @ApiModelProperty(value = "后期处理状态：0（未申请，需要展示后期处理的申请按钮） 1审批中（已经申请后期处理） 2审核通过（已经） 3已驳回（审核驳回了申请，需要展示后期处理的申请按钮）")
    private Integer pendingState;


    /**
     * 事件发生或者上报时间
     */
    private String happenedTime;
}
