package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "飞控系统数据源参数")
public class FlySystemEventDataSourceDTO {


    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 租户id
     */
    private Long userId;

    @ApiModelProperty(value = "项目id" ,required = true)
    private Long projectId;

    @ApiModelProperty(value = "事件类型 1 AI识别  2 人工提报 3 对比系统 4 飞行素材",required = true)
    private int eventType;

    @ApiModelProperty(value = "事件名称 事件的标签属性" ,required = true)
    private String eventName;

    @ApiModelProperty(value = "对比类型 1单期提取  2 两期对比",required = true)
    private int compareType;

    @ApiModelProperty(value = "当前图像地址",required = true)
    private String currentPictureUrl;

    @ApiModelProperty(value = "对比图像地址 当且仅当 compareType = 2 时  必填")
    private String comparePictureUrl;

    @ApiModelProperty(value = "当前图像的经度  小数点精确到6位" ,required = true)
    private BigDecimal currentPictureLng;
    @ApiModelProperty(value = "当前图像的纬度  小数点精确到6位",required = true)
    private BigDecimal currentPictureLat;

    @ApiModelProperty(value = "可以传一些额外数据 json字符串")
    private String extraData;

    @ApiModelProperty(value = "当前图像飞行拍摄日期 格式：yyyy-MM-dd")
    private String currentFlyDate;
    @ApiModelProperty(value = "对比图像飞行拍摄日期 格式：yyyy-MM-dd")
    private String compareFlyDate;

    /**
     * 原始数据的唯一标识
     */
    @ApiModelProperty(value = "原系统中数据的唯一标识")
    private String originalDataLogo;

    @ApiModelProperty(value = "事件发生的时间")
    private String happenedTime;

    /**
     * 飞行任务id
     */
    @ApiModelProperty(value = "飞行任务id")
    private Long flyTaskId;

    /**
     * 飞行任务名称
     */
    @ApiModelProperty(value = "飞行任务名称")
    private String flyTaskName;

    /**
     * 飞行任务编号
     */
    @ApiModelProperty(value = "飞行任务编号")
    private String flyTaskNumber;

    /**
     * 航线名称
     */
    @ApiModelProperty(value = "航线名称")
    private String airLineName;

    /**
     * 中台的设备id
     */
    @ApiModelProperty(value = "中台的设备id")
    private Long deviceId;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 设备序列号
     */
    @ApiModelProperty(value = "设备序列号")
    private String deviceSn;

    @ApiModelProperty(value = "占地面积 瓦片数据")
    private BigDecimal locateArea;

    @ApiModelProperty(value = "事件描述")
    private String desc;

    @ApiModelProperty(value = " 是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）")
    private Integer generateBill;
}
