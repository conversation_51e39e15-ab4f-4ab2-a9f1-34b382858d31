package com.mascj.lup.event.bill.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 事件标注表
 *
 * <AUTHOR> @since 2024-01-05 10:56:46
 */
@Data
@TableName("lup_event_mark")
@ApiModel(value = "LupEventMark", description = "事件标注表")
public class LupEventMark implements Serializable{

    private static final long serialVersionUID=1L;

    private Long id;
    private Long eventId;
    private Long markId;
    private Long tenantId;
    private Long createBy;
    private Date createTime;
    private Long updateBy;
    private Date updateTime;
    @TableLogic
    private Integer deleted;

}