package com.mascj.lup.event.bill.enums;

//文件模板类型枚举
public enum FileTemplateTypeEnum {

    NoneFileTemplateTypeEnum("None","未知文件模板","")
    ,LsWorkBillFileTemplateTypeEnum("Bill-Excel-Report","BillExcelFile","工单excel导出")
    ,LsWorkYXQBillFileTemplateTypeEnum("Bill-Excel-YXQ-Report","BillExcelFileYXQ","工单excel导出")
    ,DataPushLogFileTemplateTypeEnum("Data-Push-Log-Report","DataPushLogReportService","数据推送记录导出")


    ;

    FileTemplateTypeEnum(String typeName){

    }
    FileTemplateTypeEnum(String typeName, String value, String remark){
        this.typeName = typeName;
        this.value = value;
        this.remark = remark;
    }

    String typeName;
    String value;
    String remark;

    public String getValue() {
        return value;
    }

    public static FileTemplateTypeEnum getByTypeName(String typeName) {

        for (FileTemplateTypeEnum genderEnum : values()) {

            if (genderEnum.typeName.equals(typeName)) {

                return genderEnum;

            }

        }

        return LsWorkBillFileTemplateTypeEnum;
    }
}
