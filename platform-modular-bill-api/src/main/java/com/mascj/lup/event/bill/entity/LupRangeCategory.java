package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/29 09:30
 * @describe
 */
@Data
@TableName("lup_range_category")
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class LupRangeCategory extends BaseEntity {

    /**
     * 删除状态：0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 范围类目名称
     */
    private String name;

    /**
     * 范围类目对应的线的颜色
     */
    private String lineColor;

    /**
     * 范围类目 父级 id
     */
    private Long pid;

    private Long xmlFileId;

    /**
     * 类型 1 目录 2 文件
     */
    private Integer type;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 范围线填充类型
     */
    private Integer fillType;

}
