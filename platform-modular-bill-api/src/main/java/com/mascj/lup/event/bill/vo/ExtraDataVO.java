package com.mascj.lup.event.bill.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/11 11:14
 * @describe
 */
@Data
public class ExtraDataVO {
    public ExtraDataVO(){
        this.eventPictureUrlList = new ArrayList<>();
    }

    //
    // {"eventPictureUrlList":[],"eventPictureUrl":"https://cdn.uav.lyiot.cc/ztxm01/1701414101757292546/AI/AI/voluptate/2023-12/06/389f48b6-b45d-4464-b4a9-7644c5acf89f.jpg"}
    // {"eventPictureUrlList":[这里是把多个url集成一下放在这里],"type":"1 代表全是图片 2 代表全是视频 3 图片和视频混合的多张","eventPictureInfoList":[{"lat":30.213123,"lng":1203434343,"url":"https://fk.com/a.png","type":"1 代表图片 2 代表视频","urlList":[万一多个 就启用这个字段]}]}
    private List<String> eventPictureUrlList;

    private Integer type;

    private String eventPictureUrl;

    private List<EventPictureInfo> eventPictureInfoList;


    private List<String> carNumberList;
    private String parentOriginalDataLogo;
    private List<String> plateTag;

    private boolean ocr;
}
