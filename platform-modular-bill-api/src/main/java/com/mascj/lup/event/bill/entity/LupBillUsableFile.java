package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

/**
 * <p>
 * 工单数据表
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_bill_usable_file")
public class LupBillUsableFile extends BaseEntity{

    private static final long serialVersionUID=1L;

    /**
     * 是否已删除
     */
    private Integer isDeleted;

    /**
     * 租户id 
     */
    private Long tenantId;

    /**
     * bill_usable表主键ID
     */
    private Long billUsableId;

    /**
     * 文件id
     */
    private Long xlmFileId;

    /**
     * 文件链接
     */
    private String fileUrl;


}
