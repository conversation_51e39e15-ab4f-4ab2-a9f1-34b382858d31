package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 工单数据表
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_bill")
public class LupBill extends BaseEntity{

    private static final long serialVersionUID=1L;

    /**
     * 删除状态：0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 租户id 
     */
    private Long tenantId;

    /**
     * 默认1 是否可用 0否 1是
     */
    private Integer usable;

    /**
     * 项目id  
     */
    private Long projectId;

    /**
     * 事件数据id
     */
    private Long dataId;

    /**
     * 网格id
     */
    private Long gridUnitId;

    /**
     * 网格编码
     */
    private String gridCode;

    /**
     * 网格名称
     */
    private String gridName;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 工单状态
     */
    private Integer state;

    /**
     * 流程id
     */
    private Long flowEntityId;

    /**
     * 数据来源类型：1 飞控AI提取   2 飞控人工提报 3 比对系统
     */
    private Integer dataOrigin;

    /**
     * 工单编号
     */
    private String billNumber;

    /**
     * 扩展数据
     */
    private String extendData;

    /**
     * 事件发生或者上报时间
     */
    private String happenedTime;

    /**
     * 后期处理（挂起）状态：0  1已申请后期处理 待审批 2 已审批 3已驳回
     */
    private Integer pendingState;
}
