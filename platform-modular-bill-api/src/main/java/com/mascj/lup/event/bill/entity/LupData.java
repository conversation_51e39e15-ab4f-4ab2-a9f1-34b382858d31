package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.mascj.kernel.database.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 事件数据
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_data")
public class LupData extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 删除状态：0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 租户id 
     */
    private Long tenantId;

    /**
     * 项目id  
     */
    private Long projectId;

    /**
     * 数据来源类型：1 飞控AI提取   2 飞控人工提报 3 比对系统
     */
    private Integer dataOrigin;

    /**
     * 原始数据的唯一标识
     */
    private String originalDataLogo;

    /**
     * 事件发生或者上报时间
     */
    private String happenedTime;

    /**
     * 数据类型：1图片 2瓦片 3视频 4其他
     */
    private Integer dataType;

    /**
     * 比对类型：1 单期比对 2 两期比对
     */
    private Integer compareType;

    @ApiModelProperty(value = "占地面积 瓦片数据")
    private BigDecimal locateArea;

    /**
     * 图形json
     */
    private String shape;

    /**
     * 定位经度
     */
    private BigDecimal locationLng;

    /**
     * 定位维度
     */
    private BigDecimal locationLat;

    /**
     * 当前资源id
     */
    private Long currentBatchId;

    /**
     * 对比资源id
     */
    private Long compareBatchId;

    /**
     * 额外系统
     */
    private String extraData;

    /**
     * 飞行任务id
     */
    private Long flyTaskId;

    /**
     * 飞行任务名称
     */
    private String flyTaskName;

    /**
     * 飞行任务编号
     */
    private String flyTaskNumber;

    /**
     * 航线名称
     */
    private String airLineName;

    /**
     * 中台的设备id
     */
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备序列号
     */
    private String deviceSn;

    /**
     *  推送状态 0未推送   1已推送   -1推送失败  2 无需推送（流程里 的 fourSucess 的2 是无需推送）
     */
    private Integer pushState;
    private String  pushMsg;

    private String address;

    private String eventDesc;

    /**
     * 是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）
     */
    private Integer generateBill;

}
