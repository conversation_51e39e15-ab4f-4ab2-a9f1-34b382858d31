package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.lup.event.bill.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "网格展示出参")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class GridUnitShapeVO  {


    @ApiModelProperty(value = "网格子节点列表")
    private List<GriUnitVO> childUnitList;

    @ApiModelProperty(value = "当前网格")
    private  GriUnitVO currentUnit;


}
