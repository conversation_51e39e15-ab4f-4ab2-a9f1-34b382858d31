package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 流程阶段关联超时时间记录表
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_flow_entity_time_out")
public class LupFlowEntityTimeOut extends BaseEntity {

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 流程当前任务
     */
    private String processTaskId;

    /**
     * 流程ID
     */
    private Long flowEntityId;

    /**
     * 阶段开始时间
     */
    private String processStartTime;

    /**
     * 时间到期时间
     */
    private String deadLine;


}
