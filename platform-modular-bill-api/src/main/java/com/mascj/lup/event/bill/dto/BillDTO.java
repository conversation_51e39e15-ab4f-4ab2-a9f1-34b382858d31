package com.mascj.lup.event.bill.dto;

import com.mascj.lup.event.bill.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(value = "工单参数")
@Data
public class BillDTO extends BaseDTO {

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "业务参数")
    private String bizCode;

    @ApiModelProperty(value = "挂起状态  1 已申请后期处理待审批  2 后期处理已审核通过 ")
    private Integer pendingState;

    @ApiModelProperty(value = "事件标签")
    private String eventLabel;

    @ApiModelProperty(value = "事件标签列表  标签id数组")
    private List<Long> eventLabelList;

    @ApiModelProperty(value = "事件来源列表")
    private List<Integer> eventOriginList;

    @ApiModelProperty(value = "事件来源")
    private String eventOrigin;

    @ApiModelProperty(value = "日期-开始")
    private String dateStart;

    @ApiModelProperty(value = "日期-结束")
    private String dateEnd;

    @ApiModelProperty(value = "网格编码")
    private String gridCode;

    @ApiModelProperty(value = "父级网格编码")
    private String parentOrgCode;

    private List<Long> gridUnitIdList;

    @ApiModelProperty(value = "事件编号 精准查询")
    private String eventBillNumber;

    @ApiModelProperty(value = "占地面积 大于等于")
    private BigDecimal locateArea;

    @ApiModelProperty(value = "最小占地面积 大于等于")
    private BigDecimal locateAreaMin;
    @ApiModelProperty(value = " 最大占地面积 小于等于")
    private BigDecimal locateAreaMax;

    @ApiModelProperty(value = "事件标签id授权")
    private List<Long> labelIdListPerm;

}
