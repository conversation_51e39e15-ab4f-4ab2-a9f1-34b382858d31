package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

/**
 * <p>
 * 事件数据
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_batch_source")
public class LupBatchSource extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 删除状态：0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 租户id 
     */
    private Long tenantId;

    /**
     * 项目id  
     */
    private Long projectId;

    /**
     * 批次id
     */
    private Long batchId;

    /**
     * 资源id
     */
    private Long sourceId;

}
