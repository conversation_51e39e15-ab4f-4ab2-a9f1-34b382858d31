package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/5 11:58
 * @describe
 */
@Data
@ApiModel("工单读取状态入参")
public class BillReadStateCounterDTO {

    @ApiModelProperty(value = "工单读取状态 0 查询未读  1查询已读",required = true)
    private Integer billReadState;
    @ApiModelProperty(value = "流程状态 1 待巡查 " )
    private Integer billFlowState;
}
