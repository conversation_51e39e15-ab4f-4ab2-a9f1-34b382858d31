package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

/**
 * <p>
 * 工单数据表
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_bill_usable")
public class LupBillUsable extends BaseEntity{

    private static final long serialVersionUID=1L;

    /**
     * 是否已删除
     */
    private Integer isDeleted;

    /**
     * 租户id 
     */
    private Long tenantId;

    /**
     * 工单ID
     */
    private Long billId;

    /**
     * 是否可用 0否 1是
     */
    private Integer usable;

    /**
     * 原因
     */
    private String reason;


}
