package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/30 11:39
 * @describe
 */
@Data
@ApiModel("申请挂起工单参数")
public class LupBillPendingApprovalDTO {

    @ApiModelProperty(value = "工单ID",required = true)
    @NotNull
    private Long billId;

    @ApiModelProperty(value = "审核状态  通过 2 已驳回 3",required = true)
    @NotNull
    private Integer approvalState;

    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

}
