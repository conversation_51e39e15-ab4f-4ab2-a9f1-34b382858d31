package com.mascj.lup.event.bill.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.ExcelProperty;

import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.data.HyperlinkData;

import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.base.BaseVO;
import com.mascj.lup.event.bill.constant.EventTipKey;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.math.BigDecimal;

@Data
@ApiModel(value = "事件工单返回数据")
@ContentRowHeight(50)
@ExcelIgnoreUnannotated
public class BillSearchExcelVO{


    @ApiModelProperty(value = "事件编号")
    @ColumnWidth(20)
    @ExcelProperty(value = "${"+EventTipKey.CommonReportFieldEventName+"}", index = 0)
    private String billNumber;

    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportFieldCreateTime+"}", index = 1)
    private String createTime;


    @ApiModelProperty(value = "事件标签")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportFieldEventTag+"}", index = 2)
    private String eventLabel;

    @ApiModelProperty(value = "区域")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportFieldArea+"}", index = 3)
    private String areaName;

    @ApiModelProperty(value = "网格")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportGridUnit+"}", index = 4)
    private String gridName;

    @ApiModelProperty(value = "事件来源名称")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportEventOrigin+"}", index = 5)
    private String eventOriginName;

    @ApiModelProperty(value = "占地面积")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportSquare+"}", index = 6)
    private BigDecimal locateArea;

    @ApiModelProperty(value = "工单阶段状态")
    private Integer flowState;

    private Integer dataOrigin;

    @ApiModelProperty(value = "工单阶段状态")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportEventStatus+"}", index = 7)
    private String flowStateName;

    @ApiModelProperty(value = "经度")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportEventLng+"}", index = 8)
    private BigDecimal locationLng;
    @ApiModelProperty(value = "纬度")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportEventLat+"}", index = 9)
    private BigDecimal locationLat;

    @ApiModelProperty(value = "事件位置")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportLocationAddress+"}", index = 10)
    private  String locationAddress;



    @ExcelIgnore
    private String handleType;



    @ApiModelProperty(value = "是否存在")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportHasEventName+"}", index = 11)
    private String hasEvent;

    @ApiModelProperty(value = "存查内容")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportPatrolContent+"}", index = 12)
    private String patrolContent;




    @ApiModelProperty(value = "巡查照片")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportEventUrlLink+"}", index = 13)
    private String eventUrlAddress;

    @ApiModelProperty(value = "处置方式")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportHandleTyeName+"}", index = 14)
    private String handleTyeName;

    @ExcelIgnore
    private String processInstanceId;
    @ExcelIgnore
    private Long dataId;

    @ExcelIgnore
    private Integer pendingState;

}
