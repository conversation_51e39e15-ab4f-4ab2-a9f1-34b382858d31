package com.mascj.lup.event.bill.feign;

import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.FlyTaskReportDTO;
import com.mascj.lup.event.bill.vo.FlyTaskReportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2024/11/25 15:25
 * @describe
 */
@Api(value = "工单服务",tags = "工单服务")
@FeignClient(value = "lup-event-bill-server")
public interface IFlyTaskReportProvider {

    @ApiOperation(value = "【无人机平台】查询飞行任务报告")
    @PostMapping("/provider/report/queryFlyTaskReport")
    Result<FlyTaskReportVO> queryFlyTaskReport(@RequestBody @Validated FlyTaskReportDTO flyTaskReportDTO);

}
