package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/1/13 16:27
 * @describe
 */
@Data
@ApiModel(value = "应用参数")
public class LupAppDTO {

    @ApiModelProperty(value = "客户应用名称",required = true)
    @NotNull
    @NotBlank
    private String name;

    @ApiModelProperty(value = "有效日期 为null的情况下 无期限要求时传 null ")
    private String validDateTime;

    @ApiModelProperty(value = "备注描述",required = true)
    @NotNull
    @NotBlank
    private String appDesc;
}
