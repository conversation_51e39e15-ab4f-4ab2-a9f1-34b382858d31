package com.mascj.lup.event.bill.entity;

import com.mascj.lup.event.bill.base.BaseBillEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/30 11:07
 * @describe
 */
@Data
@ApiModel(value = "工单挂起记录表")
public class LupBillPending extends BaseBillEntity {

    @ApiModelProperty(value = "工单ID")
    private Long billId;

    @ApiModelProperty(value = "流程当前任务ID")
    private String processTaskId;

    @ApiModelProperty(value = "申请原因")
    private String applyReason;

    @ApiModelProperty(value = "审核人员ID")
    private Long approvalUserId;

    @ApiModelProperty(value = "审核状态 默认0   通过 2 已驳回 3")
    private Integer approvalState;

    @ApiModelProperty(value = "审核时间")
    private String approvalTime;

    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

}
