package com.mascj.lup.event.bill.vo.config;

import com.mascj.lup.event.bill.dto.PushKQDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/18 09:37
 * @describe
 */
@Data
public class HandleModuleConfigVO {

    private Boolean eventSmsSwitchKey;

    private List<String> eventSmsTimeListKey;

    private List<HmcFlowActivityVO> hmcFlowActivityKey;

    private  Map<Integer,HmcFlowActivityVO> hmcFlowActivityVOMap;

    /**
     * 流程时间期限开关
     */
    private Boolean flowEntityTimeOutSwitchKey;

    private List<FlowEntityTimeOutVO> flowEntityTimeOutKey;

    private Map<Integer,FlowEntityTimeOutVO> flowEntityTimeOutVOMap;


    private Integer eventNotificationForDealSourceTypeDictCode;
    private String eventNotificationForDealDictCode;

    private String eventNotificationForRejectDictCode;

    private EventNotificationApprovalRejectVO eventNotificationApprovalRejectKey;

    private String exportBillDataTemplateStrategy;

    /**
     * 事件后期处理的开关
     */
    private Boolean eventHangingSwitch = Boolean.FALSE;

    private PushKQDTO pushKQDTO;

}
