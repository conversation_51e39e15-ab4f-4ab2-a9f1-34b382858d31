package com.mascj.lup.event.bill.enums;

import lombok.Getter;

/**
 * 类型 1  通过original_source_id查瓦片数据  2 图片地址 3 瓦片地址（自定义的）
 */
@Getter
public enum EventSourceType {

    PictureSourceType("图片",2),TileSourceType("通过original_source_id查瓦片数据",1),AutoDefineSourceType("自定义瓦片",3),BuildTileModuleSourceType("建图采集成果瓦片类型",4);

    EventSourceType(String name, int value){
        this.name=name;
        this.value = value;
    }
    private int value;
    private String name;

    public static EventSourceType parse(int value) {
        for (EventSourceType item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

}
