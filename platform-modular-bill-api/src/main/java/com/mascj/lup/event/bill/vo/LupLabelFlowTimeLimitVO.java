package com.mascj.lup.event.bill.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * 标签流程时间期限
 * <AUTHOR> @since 2024-01-05 14:36:06
 */
@Data
public class LupLabelFlowTimeLimitVO {

    @ApiModelProperty("labelFlowId")
    private Long id;

    /**
     * 时间期限
     */
    @ApiModelProperty("时间期限")
    private BigDecimal timeLimit;

    /**
     *
     */
    @ApiModelProperty("时间期限开关")
    private Integer timeSwitch;

    /**
     * 标签id
     */
    private Long labelId;

    /**
     * 流程状态
     */
    private Integer flowState;

    
}