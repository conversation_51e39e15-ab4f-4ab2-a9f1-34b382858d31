package com.mascj.lup.event.bill.dto;

import com.mascj.kernel.database.entity.Search;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:58
 * @describe
 */
@ApiModel(value = "事件分析-事件数据查询参数")
@Data
public class EventDataTaskQueryDTO extends Search {

    private Long flyTaskId;

    @ApiModelProperty(value = "需求的任务id 列表 ")
    private List<Long> flyTaskIdList;

    @ApiModelProperty(value = "事件标签id授权")
    private List<Long> labelIdListPerm;

}
