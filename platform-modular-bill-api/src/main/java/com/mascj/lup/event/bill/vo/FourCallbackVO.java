package com.mascj.lup.event.bill.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Map;

@Data
public class FourCallbackVO implements Serializable {

    @NotBlank
    @ApiModelProperty(value = "推送的源id 工单编号", required = true)
    private String sourceId;

    @ApiModelProperty(value = "反馈结果说明", required = true)
    private String callBackMsg;

    @ApiModelProperty(value = "处理人", required = true)
    private String handName;
    @ApiModelProperty(value = "处理时间  2024-01-10 22:00:00", required = true)
    private String handTime;

    @ApiModelProperty(value = "1是否处理成功  null或者1 成功；  0退回", required = true)
    private Integer success;
    @ApiModelProperty(value = "退回原因" )
    private String fourReturnReason;

    @ApiModelProperty(value = "图片 多个用英文逗号", required = true)
    private String img;

    @ApiModelProperty(value = "反馈文字其他信息", required = false)
    private Map<String, Object> ext;

}
