package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 事件数据标签关联记录表
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_data_label")
public class LupDataLabel extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 删除状态：0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 租户id 
     */
    private Long tenantId;

    /**
     * 项目id  
     */
    private Long projectId;

    /**
     * 数据id
     */
    private Long dataId;

    /**
     * 标签id
     */
    private Long labelId;

}
