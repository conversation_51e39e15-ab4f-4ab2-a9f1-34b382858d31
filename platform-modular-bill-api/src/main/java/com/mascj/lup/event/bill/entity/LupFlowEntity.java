package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 流程记录表
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_flow_entity")
public class LupFlowEntity extends BaseEntity {

    private static final long serialVersionUID=1L;
    /**
     * 流程定义
     */
    private String processDefinitionId;

    /**
     * 流程实例
     */
    private String processInstanceId;

    /**
     * 流程当前任务
     */
    private String processTaskId;
    private String bizCode;

    /**
     * 流程状态
     */
    private Integer flowState;
    /**
     * 四平台处理结果 1处理成功  0 退回  2无需推送
     */
    private Integer fourSuccess;
    /**
     * 归档时间
     */
    private LocalDateTime archivedTime;

    /**
     * 工单流转总时长，单位毫秒
     */
    private Long processingDuration;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 归属部门，哪些部门可以看到此工单
     */
    private String ownership;

    /**
     * 处置方式 0 未处置 1 无需处置 2 已处置 3后期处置
     */
    @TableField(value = "handle_type", updateStrategy= FieldStrategy.IGNORED)
    private Integer handleType;

}
