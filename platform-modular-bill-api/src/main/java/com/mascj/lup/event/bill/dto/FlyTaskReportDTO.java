package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/25 14:59
 * @describe
 */
@Data
@ApiModel(value = "飞行任务报告查询处置系统数据的入参")
public class FlyTaskReportDTO {

    @ApiModelProperty(value = "飞行任务ID 处置系统去业务中台查询数据",required = true)
    @NotNull
    private Long flyTaskId;

    @ApiModelProperty(value = "租户ID",required = true)
    @NotNull
    private Long tenantId;

    @ApiModelProperty(value = "事件流程阶段状态： 1待巡查  5待处理（杨汛桥基层智治项目的第一个阶段） 4代表归档（杨汛桥基层智治的处理完成）  ",required = true)
    @NotNull
    @NotEmpty
    private List<Integer> flowStateList;
}
