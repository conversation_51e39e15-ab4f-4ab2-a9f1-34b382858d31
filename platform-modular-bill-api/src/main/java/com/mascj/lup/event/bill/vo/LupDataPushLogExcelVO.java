package com.mascj.lup.event.bill.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.mascj.lup.event.bill.constant.EventTipKey;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "推送记录")
@ContentRowHeight(50)
@ExcelIgnoreUnannotated
public class LupDataPushLogExcelVO {


    @ApiModelProperty(value = "事件编号")
    @ColumnWidth(20)
    @ExcelProperty(value = "${"+EventTipKey.BillPushLogExcelDataId+"}", index = 0)
    private String dataId;

    @ApiModelProperty(value = "工单编号")
    @ExcelProperty(value = "${"+EventTipKey.BillPushLogExcelBillNumber+"}", index = 1)
    private String billNumber;

    @ApiModelProperty(value = "事件来源")
    @ExcelIgnore
    private Integer dataOrigin;
    @ApiModelProperty(value = "事件来源")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportEventOrigin+"}", index = 2)
    private String eventOrigin;

    @ApiModelProperty(value = "甄别状态 是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）")
    @ExcelIgnore
    private Integer generateBill;

    @ApiModelProperty(value = "甄别状态 是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）")
    @ExcelProperty(value = "${"+EventTipKey.BillPushLogExcelGenerateBillStatus+"}", index = 3)
    private String generateBillName;

    @ApiModelProperty(value = "推送状态 0未推送   1已推送   -1推送失败")
    @ExcelIgnore
    private Integer pushState;
    @ApiModelProperty(value = "推送状态 0未推送   1已推送   -1推送失败")
    @ExcelProperty(value = "${"+EventTipKey.BillPushLogExcelPushState+"}", index = 4)
    private String pushStateName;

    @ApiModelProperty(value = "失败原因 推送结果信息")
    @ExcelProperty(value = "${"+EventTipKey.BillPushLogExcelPushMsg+"}", index = 5)
    private String pushMsg;

    @ApiModelProperty(value = "推送时间")
    @ExcelProperty(value = "${"+EventTipKey.BillPushLogExcelPushTime+"}", index = 6)
    private String pushTime;

    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "${"+EventTipKey.CommonReportFieldCreateTime+"}", index = 7)
    private String createTime;



}
