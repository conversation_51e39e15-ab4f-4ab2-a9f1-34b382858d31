package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.mascj.kernel.database.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 事件资源记录表
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_source")
public class LupSource extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 删除状态：0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 租户id 
     */
    private Long tenantId;

    /**
     * 项目id  
     */
    private Long projectId;

    /**
     * 类型 1 通过original_source_id 中台的数据 查瓦片数据  2 图片地址 3 瓦片地址（自定义的瓦片）  4 建图采集查询瓦片
     */
    private Integer type;


    @ApiModelProperty(value = "瓦片来源类型：1 自定义瓦片 2机场瓦片")
    private Integer sourceType;

    /**
     * 原始飞行任务id
     */
    private Long originalSourceId;

    /**
     * 瓦片地址或者图片地址
     */
    private String url;

    /**
     * 最小缩放
     */
    private Integer minZoom;

    /**
     * 最大缩放
     */
    private Integer maxZoom;

    /**
     * 瓦片范围
     */
    private String bounds;

    /**
     * 飞行日期
     */
    private String flyDate;

    private String sourceName;

    /**
     *
     * 原始的瓦片资源id 可以是机场端的成果id  可以是比对里自定义瓦片的id
     * type = 4  用这个查询瓦片数据
     */
    private Long originalTileSourceId;

}
