package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

/**
 * <p>
 * 工单数据表
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_bill_read")
public class LupBillRead extends BaseEntity{

    private static final long serialVersionUID=1L;

    /**
     * 是否已删除
     */
    private Integer isDeleted;

    /**
     * 租户id 
     */
    private Long tenantId;

    /**
     * 流程id
     */
    private Long flowId;

    /**
     * 流程当前任务
     */
    private Long processTaskId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 查看状态（默认0）：1 已读 0 未读
     */
    private Integer readState;


}
