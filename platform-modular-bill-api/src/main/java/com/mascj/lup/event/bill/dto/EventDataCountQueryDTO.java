package com.mascj.lup.event.bill.dto;

import com.mascj.kernel.database.entity.Search;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:58
 * @describe
 */
@ApiModel(value = "事件分析-事件数据统计参数")
@Data
public class EventDataCountQueryDTO{
    public EventDataCountQueryDTO(){
        this.labelList = new ArrayList<>();
    }

    @ApiModelProperty(value = "1 本周 2 本月 3 本年")
    private int timer;

    private int billYear;

    private String billMonth;

    private String duringStartTime;
    private String duringEndTime;
    private List<String> weekDayKeyList;
    private Map<String,String> weekDay;

    private List<String> monthDayKeyList;
    private Map<String,String> monthDay;

    private List<String> labelList;

    private List<String> monthKeyList;
    private Map<String,String> month;

}
