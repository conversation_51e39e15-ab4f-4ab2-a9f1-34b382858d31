package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:29
 * @describe
 */
@Data
@ApiModel(value = "统计事件数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CountEventDataOriginVO {

    @ApiModelProperty("数量")
    private Integer count;
    @ApiModelProperty("提取方式名称")
    private String name;

    @ApiModelProperty("提取方式编码")
    private Integer code;

}
