package com.mascj.lup.event.bill.feign;

import com.mascj.lup.event.bill.dto.EventDataDTO;
import com.mascj.lup.event.bill.dto.FixEventDataSourceDTO;
import com.mascj.lup.event.bill.dto.FlySystemDelDTO;
import com.mascj.lup.event.bill.dto.FlySystemEventDataSourceDTO;
import com.mascj.kernel.common.api.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api(value = "工单服务",tags = "工单服务")
@FeignClient(value = "lup-event-bill-server")
public interface IDataReceiveProvider {

    @ApiOperation(value = "【飞控系统】提交事件数据")
    @PostMapping("/support/postFlyEventData")
    Result postFlyEventData(@RequestBody FlySystemEventDataSourceDTO dataSourceDTO);

    @ApiOperation(value = "【飞控系统】作废事件数据")
    @PostMapping("/support/delFlyEventData")
    Result delFlyEventData(@RequestBody FlySystemDelDTO flySystemDelDTO);

    @ApiOperation(value = "【比对系统】提交事件数据")
    @PostMapping("/support/postEventData")
    Result postEventData(@RequestBody EventDataDTO eventData);

    @ApiOperation(value = "从比对系统服务事件数据")
    @PostMapping("/support/fixEventData")
    Result fixEventData(@RequestBody FixEventDataSourceDTO fixEventDataSourceDTO);

}
