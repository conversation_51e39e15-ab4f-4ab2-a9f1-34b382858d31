package com.mascj.lup.event.bill.sync;

import lombok.Data;
import org.apache.logging.log4j.util.Strings;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据同步状态
 */
@Data
public class ReportSyncState {

    /**
     * 唯一标识符
     */
    private String uuid;

    /**
     * 任务操作者
     */
    private Long userId;

    private String tenantId;

    /**
     * 应用ID
     */
    private long appId;
    /**
     * 批次代码
     */
    private String batchCode;

    /**
     * 数据总量
     */
    private int total;

    /**
     * 当前进度
     */
    private int current;

    /**
     * 出错数量
     */
    private int errCount;

    private List<String> errDetails = new ArrayList<>();

    /**
     * 是否完成
     */
    private boolean finished;

    /**
     * 是否失败
     */
    private boolean failed;

    /**
     * 出错原因
     */
    private String errMessage = Strings.EMPTY;

    /**
     * 网格id字符串 以逗号隔开
     */
    private String gridIdArray;
}
