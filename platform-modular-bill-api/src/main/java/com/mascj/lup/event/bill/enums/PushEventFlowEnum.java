package com.mascj.lup.event.bill.enums;

import io.swagger.annotations.ApiModel;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/8 18:05
 * @describe
 */
@Getter
@ApiModel(value = "推送事件的流程 默认通用流程 DefaultFlow、推送关联流程（杨汛桥项目的事件流程）PushEventFlow")
public enum PushEventFlowEnum {
    DefaultFlow("DefaultFlow"),PushEventFlow("PushEventFlow");
    String value;
    PushEventFlowEnum(String value){
        this.value = value;
    }

    public static PushEventFlowEnum parse(String code) {
        for (PushEventFlowEnum item : values()) {
            if (item.getValue().equals(code)) {
                return item;
            }
        }
        return DefaultFlow;
    }

}
