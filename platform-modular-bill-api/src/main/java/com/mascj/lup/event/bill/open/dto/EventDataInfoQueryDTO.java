package com.mascj.lup.event.bill.open.dto;

import com.mascj.kernel.database.entity.Search;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:58
 * @describe
 */
@ApiModel(value = "事件数据信息分页查询参数")
@Data
public class EventDataInfoQueryDTO extends Search {

    @ApiModelProperty(value = "事件ID 工单编号")
    private String eventNumber;

    @ApiModelProperty(value = "数据来源类型：1 飞控AI提取   2 飞控人工提报 3 比对系统")
    private Integer dataOrigin;

    @ApiModelProperty(value = "事件开始时间")
    private String duringStartTime;

    @ApiModelProperty(value = "事件结束时间")
    private String duringEndTime;

    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @ApiModelProperty(value = "标签id")
    private Long labelId;

    @ApiModelProperty(value = "航线名称")
    private String airLineName;

    @ApiModelProperty(value = "设备SN")
    private String deviceSn;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "飞行任务名称")
    private String flyTaskName;

    @ApiModelProperty(value = "飞控上报事件备注")
    private String eventDesc;

}
