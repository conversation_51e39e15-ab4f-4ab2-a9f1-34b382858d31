package com.mascj.lup.event.bill.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public class EventDateUtil {

    private static final String defaultDateFormat = "yyyy-MM-dd HH:mm:ss";

    public static LocalDateTime parseLocalDateTime(String dateStr) {
        DateTime parse = DateUtil.parse(dateStr);
        Instant instant = parse.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
        System.out.println(localDateTime);
        return localDateTime;
    }

    /**
     *
     * @param dateStr
     * @return 返回日期格式 yyyy-MM-dd HH:mm:ss
     */
    public static String parseDate(String dateStr) {
        return parseDate(dateStr,defaultDateFormat);
    }

    public static String parseDate(String dateStr,String dateFormat) {
        try {
            DateTime parse = DateUtil.parse(dateStr);
            Instant instant = parse.toInstant();
            ZoneId zoneId = ZoneId.systemDefault();
            LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
            System.out.println(localDateTime);
            return localDateTime.format(DateTimeFormatter.ofPattern(dateFormat));
        }catch (Exception e){
            try {
                dateStr = dateStr + ":00";
                DateTime parse = DateUtil.parse(dateStr);
                Instant instant = parse.toInstant();
                ZoneId zoneId = ZoneId.systemDefault();
                LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
                System.out.println(localDateTime);
                return localDateTime.format(DateTimeFormatter.ofPattern(dateFormat));
            }catch (Exception exp) {
                exp.printStackTrace();
            }
        }
        return "";
    }

}
