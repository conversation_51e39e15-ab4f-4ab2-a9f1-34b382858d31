package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class FlySystemDelDTO {

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 租户id
     */
    private Long userId;

    @ApiModelProperty(value = "项目id" ,required = true)
    private Long projectId;

    @ApiModelProperty(value = "事件类型 1 AI识别  2 人工提报 3 对比系统",required = true)
    private int eventType;

    /**
     * 原始数据的唯一标识
     */
    @ApiModelProperty(value = "原系统中数据的唯一标识",required = true)
    private String originalDataLogo;

    @ApiModelProperty(value = "占地面积 瓦片数据")
    private BigDecimal locateArea;
}
