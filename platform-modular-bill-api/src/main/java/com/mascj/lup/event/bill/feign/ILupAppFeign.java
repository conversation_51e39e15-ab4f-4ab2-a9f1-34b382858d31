package com.mascj.lup.event.bill.feign;

import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.LupAppAuthCodeDTO;
import com.mascj.lup.event.bill.dto.LupAppSignDTO;
import com.mascj.lup.event.bill.entity.LupApp;
import com.mascj.lup.event.bill.vo.EventDataLabelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13 17:02
 * @describe
 */
@Api(value = "应用feign",tags = "应用feign")
@FeignClient(value = "lup-event-bill-server")
public interface ILupAppFeign {

    @ApiOperation(value = "激活授权码")
    @PostMapping("/support/app/active/auth/code")
    Result<LupApp> activeAppAuthCode(@RequestBody LupAppAuthCodeDTO authCodeDTO);


    @ApiOperation(value = "验证APP签名")
    @PostMapping("/support/app/valid")
    Result validApp(@RequestBody LupAppSignDTO appSignDTO);

    @ApiOperation(value = "创建应用签名")
    @GetMapping("/support/app/sign/create")
    Result<String> createSign(String appKey);
}
