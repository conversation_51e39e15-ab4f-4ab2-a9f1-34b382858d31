package com.mascj.lup.event.bill.feign;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:57
 * @describe
 */
@Api(value = "事件数据查询接口",tags = "事件数据查询接口")
@FeignClient(value = "lup-event-bill-server")
public interface IEventDataQueryProvider  {


    @ApiOperation(value = "根据id查询事件数据详情")
    @GetMapping("/support/eventData/labelList")
    Result<List<EventDataLabelVO>> labelList();

    @ApiOperation(value = "查询所有事件数据")
    @PostMapping("/support/eventData/listAll")
    Result<List<EventDataBaseInfoVO>> listAll();

    @ApiOperation(value = "分页查询事件数据")
    @PostMapping("/support/eventData/pageByTask")
    Result<Page<EventDataTaskVO>> pageByTask(@RequestBody EventDataTaskQueryDTO eventDataTaskQueryDTO);


    @ApiOperation(value = "分页查询事件数据")
    @PostMapping("/support/eventData/page")
    Result<IPage<EventDataVO>> page(@RequestBody EventDataQueryDTO eventDataQueryDTO);

    @ApiOperation(value = "根据地块数据分页查询事件数据")
    @PostMapping("/support/eventData/pageForLand")
    Result<Page<EventDataVO>> pageForLand(@RequestBody EventDataForLandQueryDTO eventDataQueryDTO);

    @ApiOperation(value = "根据id查询事件数据详情")
    @GetMapping("/support/eventData/detail/{id}")
    Result<EventDataVO> detail(@PathVariable("id") Long billId);

    @ApiOperation(value = "根据区域统计数据")
    @PostMapping("/support/eventData/countByArea")
    Result<List<CountEventDataVO>> countByArea();

    @ApiOperation(value = "根据标签统计数据")
    @PostMapping("/support/eventData/countByLabel")
    Result<List<CountEventDataVO>> countByLabel();

    @ApiOperation(value = "根据标签统计数据")
    @PostMapping("/support/eventData/countByLabelOnDay")
    Result<List<CountEventDataVO>> countByLabelOnDay();

    @ApiOperation(value = "根据事件来源统计")
    @PostMapping("/support/eventData/countByOrigin")
    Result<CountEventDataOriginGroupVO> countByOrigin(EventDataCountQueryDTO eventDataCountQueryDTO);

    @ApiOperation(value = "根据事件处理结果统计")
    @PostMapping("/support/eventData/countByDealResult")
    Result<CountEventDataDealResultVO> countByDealResult(EventDataCountQueryDTO eventDataCountQueryDTO);


    @ApiOperation(value = "查询事件统计")
    @PostMapping("/support/eventData/billQueryCount")
    Result<List<BillQueryCountVO>> billQueryCount(@RequestBody @Validated BillQueryCountDTO billQueryCountDTO);

    @ApiOperation(value = "查询事件列表")
    @PostMapping("/support/eventData/billQueryList")
    Result<List<BillQueryListItemVO>> billQueryList(@RequestBody @Validated BillQueryCountDTO billQueryCountDTO);
}
