package com.mascj.lup.event.bill.dto;

import com.mascj.lup.event.bill.enums.PictureMergeSourceMode;
import com.mascj.lup.event.bill.geo.DataPicPoint;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/17 10:07
 * @describe
 */
@Data
public class MergePictureTileDTO {

    private String url;

    @ApiModelProperty(value = "是否反转 0否 1是")
    private Integer flipFlag = 0;

    @ApiModelProperty(value = "转存状态(0-全部未转存，1-全部已转存,2-瓦片已转存, 3-tif已转存,10-全部转存中,20-瓦片转存中, 30-tif转存中) ")
    private Integer transferStatus;

}
