package com.mascj.lup.event.bill.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2023/11/29 09:51
 * @describe
 */
@ApiModel(value = "范围线类目")
@Data
public class LupRangeCategoryAddDTO {

    @ApiModelProperty(value = "范围线类目名称")
    private String name;

    @ApiModelProperty(value = "范围线颜色")
    private String lineColor;

    @ApiModelProperty(value = "父级id 没有则不传")
    private Long pid;

    @ApiModelProperty(value = "类型 1:目录 2：文件")
    private Integer type;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "范围线填充类型  默认null 时 不填充")
    private Integer fillType;


    @ApiModelProperty(value = "json文件")
    private MultipartFile file;

    private Long xmlFileId;

    private JSONObject shape;

}
