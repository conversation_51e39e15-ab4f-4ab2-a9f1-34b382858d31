package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 标签关联永安分类表实体类
 */
@Data
@ApiModel(description = "标签关联永安分类表")
public class LupLabelYonganCategory implements Serializable {
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @ApiModelProperty("主键id")
    @TableId(
            value = "id",
            type = IdType.ASSIGN_ID
    )
    private Long id;

    @ApiModelProperty(value = "标签ID")
    private Long labelId;

    @ApiModelProperty(value = "永安分类ID")
    private Long yonganCategoryId;
    @ApiModelProperty(value = " 1 ai识别  2人工提报")
    private Integer codeValue;

    @ApiModelProperty(value = "租户Id", example = "0")
    private Long tenantId;


    @ApiModelProperty("创建人")
    @TableField(
            value = "create_by",
            fill = FieldFill.INSERT
    )
    private Long createBy;
    @ApiModelProperty("创建时间")
    @TableField(
            value = "create_time",
            fill = FieldFill.INSERT
    )
    private LocalDateTime createTime;
    @ApiModelProperty("更新人")
    @TableField(
            value = "update_by",
            fill = FieldFill.INSERT_UPDATE
    )
    private Long updateBy;
    @ApiModelProperty("更新时间")
    @TableField(
            value = "update_time",
            fill = FieldFill.UPDATE
    )
    private LocalDateTime updateTime;
}
