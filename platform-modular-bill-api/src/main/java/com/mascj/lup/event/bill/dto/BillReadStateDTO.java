package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/5 11:58
 * @describe
 */
@Data
@ApiModel("工单状态入参")
public class BillReadStateDTO {

    @ApiModelProperty(value = "流程id",required = true)
    private Long flowId;

    @ApiModelProperty(value = "阶段任务ID")
    private Long processTaskId;

}
