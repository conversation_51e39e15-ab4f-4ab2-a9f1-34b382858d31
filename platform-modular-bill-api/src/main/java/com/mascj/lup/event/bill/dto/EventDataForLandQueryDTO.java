package com.mascj.lup.event.bill.dto;

import com.mascj.kernel.database.entity.Search;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:58
 * @describe
 */
@ApiModel(value = "事件分析-事件数据查询参数")
@Data
public class EventDataForLandQueryDTO extends EventDataQueryDTO {

    @ApiModelProperty(value = "地块范围线信息")
    private String geoInfo;

    @ApiModelProperty(value = "飞行任务ID列表")
    private List<Long> flyTaskIdList;

    @ApiModelProperty(value = "原始的事件唯一标识列表")
    private List<Long> originalEventLogoList;

}
