package com.mascj.lup.event.bill.dto;

import com.mascj.lup.event.bill.enums.PictureMergeSourceMode;
import com.mascj.lup.event.bill.geo.DataPicPoint;
import lombok.Data;

import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/17 10:07
 * @describe
 */
@Data
public class MergePictureDTO {

    private boolean drawLine = true;
    private int renderBuffer = 50;

    private int zoom=20;

    private String lineColor;

    public MergePictureDTO(){
        this.currentTileList = new ArrayList<>();
        this.compareTileList = new ArrayList<>();
        this.currentPicName = "";
        this.comparePicName = "";
    }

    private String tenantId;
    private Long userId;

    private BufferedImage targetBufferedImage;
    private BufferedImage currentBufferedImage;
    private BufferedImage compareBufferedImage;
    private String currentPicFile;
    private List<MergePictureTileDTO> currentTileList;
    private String currentPicName;
    private String comparePicFile;
    private List<MergePictureTileDTO> compareTileList;
    private String comparePicName;
    private String shape;
    private Boolean isCompareMode = false;

    private PictureMergeSourceMode sourceMode;

    private List<DataPicPoint> pixelPointList;

    private String currentPicUrl;

    private String comparePicUrl;

    public String getFileName(){
        String tmp = "-img-";
        if(currentPicName!=null){
            tmp = currentPicName+tmp;
        }
        if(comparePicName!=null){
            tmp = tmp + comparePicName + "-";
        }
        return tmp;
    }

}
