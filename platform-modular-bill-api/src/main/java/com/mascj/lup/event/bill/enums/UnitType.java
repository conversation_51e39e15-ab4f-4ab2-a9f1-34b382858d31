package com.mascj.lup.event.bill.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 网格常量
 *
 * */
@Getter
@AllArgsConstructor
public enum UnitType {

    UNKOWN(-1, "未知"),
    ACRE(0, "地块"),//19,地块


    GRID(1, "网格"),//15,自然村

    VILLAGE(2, "村社区"),//12，行政村

    TOWN(3, "镇街"),//9
    AREA(4, "区县"),//6
    CITY(5, "市"),//4
    PROVINCE(6, "省");//2

    private final Integer code;

    private final String message;

    public static Integer getValue(String level) {
        UnitType[] type = values();
        for (UnitType en : type) {
            if (en.getMessage().equals(level)) {
                return en.getCode();
            }
        }
        return null;
    }
}
