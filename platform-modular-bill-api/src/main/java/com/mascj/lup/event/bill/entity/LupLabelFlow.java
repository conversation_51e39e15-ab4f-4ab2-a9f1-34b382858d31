package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 工单通知
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_label_flow")
public class LupLabelFlow extends BaseEntity{

    private static final long serialVersionUID=1L;

    /**
     * 是否已删除 0否 1是
     */
    private Integer deleted;

    /**
     * 租户id 
     */
    private Long tenantId;

    /**
     * 标签id
     */
    private Long labelId;

    /**
     * 流程状态
     */
    private Integer flowState;

    /**
     * 流程节点ID
     */
    private String flowActivityId;

    /**
     * 时间期限
     */
    private BigDecimal timeLimit;

    /**
     * 开关 默认 0 未开启  1 已开启
     */
    private  Integer timeSwitch;

}
