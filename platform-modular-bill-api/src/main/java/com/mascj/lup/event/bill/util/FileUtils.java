package com.mascj.lup.event.bill.util;

import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.util.exceptions.OperationFailedException;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class FileUtils {
    /**
     * @param path 资源目录
     * @return
     * @throws URISyntaxException
     * @throws FileNotFoundException
     */
    public static String readResourceFile(String path) {

        try {
            InputStream inStream = getResourceStream(path);

            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(inStream, StandardCharsets.UTF_8)
            );

            StringBuilder stringBuilder = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }

            return stringBuilder.toString();

        } catch (IOException e) {
            throw OperationFailedException.format("读取文件失败，message=" + e.getMessage());
        }

    }

    /**
     *
     * @param path
     * @return
     */
    public static InputStream getResourceStream(String path) {
        Assert.hasText(path, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NonePath, Arrays.asList(EventTipKey.RequiredTip)));
        return FileUtils.class.getClassLoader().getResourceAsStream(path);
    }


    /**
     * @param resourcePath
     * @return
     * @throws URISyntaxException
     */
    public static String getLocalPath(String resourcePath) {
        try {
            ClassLoader loader = FileUtils.class.getClassLoader();

            return Objects.requireNonNull(loader.getResource(resourcePath)).toURI().getPath();
        } catch (URISyntaxException e) {
            throw OperationFailedException.format("getLocalPath失败，message=" + e.getMessage());
        }
    }

    /**
     * @param path 本地路径
     * @return
     * @throws FileNotFoundException
     */
    public static String[] readAllLines(String path) {
        Assert.hasText(path, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NonePath, Arrays.asList(EventTipKey.RequiredTip)));

        File file = new File(path);

        if (!file.exists()) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.NoneFileExist) + path);
        }

        List<String> list = new ArrayList<>();

        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(file));

            String tempString = null;

            while ((tempString = reader.readLine()) != null) {
                list.add(tempString);
            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                }
            }
        }

        return list.toArray(new String[0]);
    }

    /**
     * 读取本地文件
     *
     * @param path 文件系统内的文件
     * @return
     * @throws FileNotFoundException
     */
    public static String readAllText(String path) {

        StringBuilder stringBuilder = new StringBuilder();

        String[] lines = readAllLines(path);

        for (String line : lines) {
            stringBuilder.append(line);
        }

        return stringBuilder.toString();
    }


    /**
     * 获取封装得MultipartFile
     *
     * @param inputStream inputStream
     * @param fileName    fileName
     * @return MultipartFile
     */
    public static MultipartFile getMultipartFile(InputStream inputStream, String fileName) {
        FileItem fileItem = createFileItem(inputStream, fileName);
        //CommonsMultipartFile是feign对multipartFile的封装，但是要FileItem类对象
        return new CommonsMultipartFile(fileItem);
    }


    /**
     * FileItem类对象创建
     *
     * @param inputStream inputStream
     * @param fileName    fileName
     * @return FileItem
     */
    public static FileItem createFileItem(InputStream inputStream, String fileName) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "file";
        FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
        int bytesRead = 0;
        byte[] buffer = new byte[10 * 1024 * 1024];
        OutputStream os = null;
        //使用输出流输出输入流的字节
        try {
            os = item.getOutputStream();
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
//            log.error("Stream copy exception", e);
            throw new IllegalArgumentException(LocaleMessageUtil.getMessage(EventTipKey.FailFileUpload));
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
//                    log.error("Stream close exception", e);

                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
//                    log.error("Stream close exception", e);
                }
            }
        }

        return item;
    }
}
