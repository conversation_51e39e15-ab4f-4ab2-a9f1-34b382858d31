package com.mascj.lup.event.bill.vo;

import com.mascj.kernel.common.util.StringUtil;
import com.mascj.lup.event.bill.constant.VariableConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class FlyEventTjQueryVO implements Serializable {

//    @NotBlank
    @ApiModelProperty("查询年")
    private String year;
    @ApiModelProperty("查询数量， 排名使用")
    private Integer top;

    @ApiModelProperty("开始月份  2024-02")
    private String monthStart;
    @ApiModelProperty("结束月份 2024-03")
    private String monthEnd;
    @ApiModelProperty("开始日  2024-02-01")
    private String dateStart;
    @ApiModelProperty("结束月份 2024-02-01")
    private String dateEnd;
    @ApiModelProperty("查询类型 日date  月month")
    private String dateType;

    private Integer flowState;

    private List<Long> labelIdList;


    /**
     * 增加前端查询加入 日的查询
     */
    public void dealQuery(){
        String dateType = this.getDateType();
        if (StringUtil.isNotBlank(dateType)){
            if (VariableConstants.DATATYPE.DATE.equals(dateType)){
                this.setDateStart(this.getMonthStart());
                this.setDateEnd(this.getMonthEnd());
                this.setMonthEnd(null);
                this.setMonthStart(null);
            }
        }
    }
}
