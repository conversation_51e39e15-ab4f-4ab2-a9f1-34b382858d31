package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/13 20:58
 * @describe
 */
@Data
public class ExportDataBaseDTO {

    @ApiModelProperty("数据库名称 lup_biz_aep、lup_biz_comparison、xlm_biz_flyer")
    private String moduleCodeDBName;
    @ApiModelProperty("模块编码 workBill（lup_biz_aep）、bizComparison（lup_biz_comparison）、bizFlyer（xlm_biz_flyer）")
    private String moduleCode;


    private Long lupExportItemId;

    /**
     *  开始时间 2022-02-02
     */
    @ApiModelProperty("开始时间 2022-02-02")
    private String startTime;

    /**
     *  结束时间 2022-02-02
     */
    @ApiModelProperty("结束时间 2022-02-02")
    private String endTime;

    /**
     *  租户ID
     */
    @ApiModelProperty("租户ID")
    private String tenantId;

    private String modular;

    public void setModular(String modular) {
        this.modular = modular;

        this.moduleCode = modular;
        switch (modular){
//            workBill（lup_biz_aep）、bizComparison（lup_biz_comparison）、bizFlyer（xlm_biz_flyer）
            case "workBill": this.moduleCodeDBName = "lup_biz_aep";
                break;
            case "bizComparison": this.moduleCodeDBName = "lup_biz_comparison";
                break;
            case "bizFlyer": this.moduleCodeDBName = "xlm_biz_flyer";
                break;
            default:break;
        }
    }
}
