package com.mascj.lup.event.bill.geo;

import com.fasterxml.jackson.annotation.JsonInclude;

import com.mascj.lup.event.bill.base.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
@ApiModel(value = "瓦片数据展示")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GeoTile extends BaseVO{

    /**
     * 类型 1 通过original_source_id查瓦片数据  2 图片地址 3 瓦片地址（自定义的） 4 建图采集的ID查询
     * 原始的瓦片资源id 可以是机场端的成果id  可以是比对里自定义瓦片的id
     */
    private Long originalTileSourceId;

    @ApiModelProperty(value = "是否反转 0否 1是")
    private Integer flipFlag = 0;

    @ApiModelProperty(value = "转存状态(0-全部未转存，1-全部已转存,2-瓦片已转存, 3-tif已转存,10-全部转存中,20-瓦片转存中, 30-tif转存中) ")
    private Integer transferStatus;

    @ApiModelProperty(value = "瓦片来源类型：1 自定义瓦片 2机场瓦片")
    private Integer sourceType;

    @ApiModelProperty(value = "瓦片地址")
    private String tileUrl;

    @ApiModelProperty(value = "瓦片名称")
    private String tileName;

    @ApiModelProperty(value = "飞行日期")
    private String flyDate;

    @ApiModelProperty(value = "最小缩放")
    private Integer minZoom;

    @ApiModelProperty(value = "最大缩放")
    private Integer maxZoom;

    @ApiModelProperty(value = "瓦片范围")
    private String tileBounds;

    public void setTileUrl(String tileUrl){

        String tmp = tileUrl;
        if(tileUrl.contains("?")){
            tmp = tileUrl.substring(0,tileUrl.indexOf("?"));

            String[] tmp2 = tileUrl.substring(tileUrl.indexOf("?")+1).split("&");
            Map<String,String> data = new HashMap();
            for(String m : tmp2){
                String[] tmp3 = m.split("=");
                data.put(tmp3[0],tmp3[1]);
            }
            this.parameter = data;
        }

        this.tileUrl= tmp;

    }

    @ApiModelProperty(value = "瓦片地址的后缀参数")
    private Object parameter;

}
