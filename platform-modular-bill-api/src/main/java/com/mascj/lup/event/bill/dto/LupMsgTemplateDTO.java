package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/18 15:31
 * @describe
 */
@Data
public class LupMsgTemplateDTO {

    private Map<String , Object> params;

    private String tenantId;

    @ApiModelProperty(value = "模板字典 编码")
    private String type;

    @ApiModelProperty(value = "来源 1.机场 ")
    private Integer sourceType;
    private List<Long> userIds;

}
