package com.mascj.lup.event.bill.vo;

import com.mascj.lup.event.bill.geo.GeoTile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "资源图层数据 瓦片")
@Data
public class SourceTileVO {

    @ApiModelProperty(value = "飞行日期")
    private String flyDate;

    @ApiModelProperty(value = "瓦片列表")
    private List<GeoTile> tileList;

}
