package com.mascj.lup.event.bill.dto;

import com.mascj.kernel.database.entity.Search;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:58
 * @describe
 */
@ApiModel(value = "事件分析-事件数据查询参数")
@Data
public class EventDataQueryDTO extends Search {

    @ApiModelProperty(value = "事件ID 工单编号")
    private String eventNumber;

    @ApiModelProperty(value = "数据来源类型：1 飞控AI提取   2 飞控人工提报 3 比对系统")
    private Integer dataOrigin;

    @ApiModelProperty(value = "事件开始时间")
    private String duringStartTime;

    @ApiModelProperty(value = "事件结束时间")
    private String duringEndTime;

    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @ApiModelProperty(value = "标签id")
    private Long labelId;

    @ApiModelProperty(value = "事件标签id授权")
    private List<Long> labelIdListPerm;

}
