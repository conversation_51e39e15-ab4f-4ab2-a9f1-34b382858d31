package com.mascj.lup.event.bill.enums;

import lombok.Getter;

/**
 * 数据类型：1图片 2瓦片 3视频 4其他
 */
@Getter
public enum EventDataType {

    PictureDataType("图片", 1), TileDataType("瓦片", 2), VideoDataType("视频", 3), OtherDataType("视频", 4), HumanTileDataType("瓦片", 5);

    EventDataType(String name, int value) {
        this.name = name;
        this.value = value;
    }

    private int value;
    private String name;

    public static EventDataType parse(int value) {
        for (EventDataType item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }
}

