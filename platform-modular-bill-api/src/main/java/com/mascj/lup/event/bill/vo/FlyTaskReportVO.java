package com.mascj.lup.event.bill.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/25 15:22
 * @describe
 */
@ApiModel(value = "飞行任务报告VO")
@Data
public class FlyTaskReportVO {

    public FlyTaskReportVO(){
        this.eventCountMap = new HashMap<>();
    }

    @ApiModelProperty(value = "事件数量map，根据事件查询条件中的 flowStateList 的每个item作为key value为对应的事件数量")
    private Map<Integer,Integer> eventCountMap;
}
