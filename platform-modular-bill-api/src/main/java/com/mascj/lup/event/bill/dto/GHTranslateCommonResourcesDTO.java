package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/8 20:29
 * @describe
 */
@Data
public class GHTranslateCommonResourcesDTO {

    @ApiModelProperty(value = "资源类型：1图；2视频；3文件")
    private Integer resourceType = 1;
    @ApiModelProperty(value = "资源名称")
    private String resourceName;
    @ApiModelProperty(value = "资源地址")
    private String resourceUrl;
    @ApiModelProperty(value = "排序")
    private Integer sort;
}
