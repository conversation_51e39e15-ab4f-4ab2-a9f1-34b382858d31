package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 事件数据
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_batch")
public class LupBatch extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 删除状态：0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 租户id 
     */
    private Long tenantId;

    /**
     * 项目id  
     */
    private Long projectId;

    /**
     * 数据批次日期
     */
    private String batchDate;

    /**
     * 数据批次名称 默认与批次日期相同
     */
    private String batchName;

}
