package com.mascj.lup.event.bill.vo.config;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/10/18 09:41
 * @describe
 */
@Data
public class EventNotificationApprovalRejectVO {

    private Integer currentFlowState;
    private Integer approvalFlowState;
    private Boolean immediateSend;

    private String eventNotificationFroRejectDictCodeImmediate;
    private String eventNotificationFroRejectDictCodeTimer;

}
