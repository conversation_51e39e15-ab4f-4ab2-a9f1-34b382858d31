package com.mascj.lup.event.bill.util;

import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.StringUtil;
import com.mascj.kernel.web.util.WebUtil;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;

public class ProjectUtil {
    public static Long getProjectId(){
        return Long.parseLong(LmContextHolder.getTenantId());
    }
}
