package com.mascj.lup.event.bill.enums;

import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import lombok.Getter;

/**
 * 推送状态 0未推送   1已推送   -1推送失败
 * <AUTHOR>
 * @date 2025/1/8 20:03
 * @describe
 */
@Getter
public enum DataPushStateEnum {

    NonePush(0,"未推送",""),PushSuccess(1,"已推送", EventTipKey.BillPushSuccess),PushFail(-1,"推送失败", EventTipKey.BillPushFail);

    DataPushStateEnum(int code,String value,String name){
        this.code = code;
        this.value = value;
        this.name = name;

    }

    public String getName() {
        return LocaleMessageUtil.getMessage(name);
    }

    public static DataPushStateEnum parse(Integer code) {
        for (DataPushStateEnum item : values()) {

            //为空时 默认已甄别 老服务和数据处理用
            if(code == null) return NonePush;

            if (item.getCode() == code) {
                return item;
            }
        }
        return NonePush;
    }

    int code;
    String value;
    String name;
}
