package com.mascj.lup.event.bill.open.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:29
 * @describe
 */
@Data
@ApiModel(value = "事件数据")
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class OpenEventDataVO {

    @ApiModelProperty(value = "事件id")
    private Long billId;

    @ApiModelProperty(value = "流程状态编码")
    private String flowCode;

    @ApiModelProperty(value = "事件标签")
    private String eventLabel;

    @ApiModelProperty(value = "事件编号  工单编号")
    private String billNumber;

    @ApiModelProperty(value = "事件提报时间")
    private String createTime;

    @ApiModelProperty(value = "事件位置经度")
    private String locationLng;

    @ApiModelProperty(value = "事件位置纬度")
    private String locationLat;

    @ApiModelProperty(value = "飞行任务编号")
    private String taskNumber;

    @ApiModelProperty(value = "网格名称")
    private String gridName;

    @ApiModelProperty(value = "航线名称")
    private String airLineName;

    @ApiModelProperty(value = "设备名称 机场名称")
    private String deviceName;

    @ApiModelProperty(value = "飞行任务编号")
    private String flyTaskNumber;

    @ApiModelProperty(value = "飞行任务名称")
    private String flyTaskName;

    @ApiModelProperty(value = "设备序列号")
    private String deviceSn;

    @ApiModelProperty(value = "发生时间 基于一些场景 是准确的  比如交通拥堵时的拍摄时间 识别之后就是发生时间 其他的 静态物品  最多表达为 事件发现时间")
    private String happenedTime;

    @ApiModelProperty(value = "事件图片")
    private String eventPicture;

    @ApiModelProperty(value = "飞控上报事件备注")
    private String eventDesc;

}
