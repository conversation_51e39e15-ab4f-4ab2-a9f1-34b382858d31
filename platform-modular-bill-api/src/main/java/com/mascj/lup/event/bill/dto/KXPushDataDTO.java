package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/1/3 16:11
 * @describe
 */
@Data
public class KXPushDataDTO {

    @ApiModelProperty("告警来源类型（1:ai,2:无人机） 这里设定固定为2")
    private Integer warningSourceType;

    @ApiModelProperty("来源方标识(目前必填) 固定传5" )
    private Integer manufacturerId = 5;

    @ApiModelProperty("业务领域(目前必填)")
    private String businessCode="基层治理";

    @ApiModelProperty("点位")
    private String locationName;

    @ApiModelProperty("设备编号")
    private String deviceCode;
    @ApiModelProperty("设备型号")
    private String deviceType;
    @ApiModelProperty("设备名称")
    private String deviceName;

    private String longitude;
    private String latitude;
    private String geoType = "wgs84";

    @ApiModelProperty("告警类别，存中文 标签名称")
    private String alarmType="";

    @ApiModelProperty("上报时间(格式yyyy-MM-ddTHH:mm:ss.SSSX)")
    private String upTime;

    @ApiModelProperty("事件内容 在【地址】可能发生了【事件标签】事件！")
    private String eventContent;

    @ApiModelProperty("告警状态")
    private String alarmFlag;

    @ApiModelProperty("图片url")
    private String picUrl;

    @ApiModelProperty("缩略图")
    private String thumbnailUrl;


    @ApiModelProperty("告警明细类型")
    private String alarmDetailType;

    @ApiModelProperty("处理状态")
    private Integer handleStatus;

    @ApiModelProperty("省份国标编码(例330000000000)")
    private String provinceCode;

    @ApiModelProperty("城市国标编码(例330600000000)")
    private String cityCode;
    @ApiModelProperty("区县国标编码(例330601000000)")
    private String districtCode;
    @ApiModelProperty("街道国标编码(例330603005000)")
    private String streetCode;

    @ApiModelProperty("社区/村国标编码\n" +
            "(例330603005001)")
    private String communityCode;

    @ApiModelProperty("源主键/源ID/三方业务id 特殊业务需要的话必填  传lup_bill 表主键id")
    private String sourceId;

    @ApiModelProperty("网格名称")
    private String gridName;

}
