package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mascj.lup.event.bill.entity.LupLabel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:29
 * @describe
 */
@Data
@ApiModel(value = "事件数据")
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class EventDataVO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    private Boolean check;

    @ApiModelProperty(value = "流程id")
    private Long flowEntityId;
    @ApiModelProperty(value = "流程当前任务")
    private String processTaskId;
    @ApiModelProperty(value = "流程任务名称")
    private String name;

    @ApiModelProperty(value = "流程节点数值  对应需要重新序列化名称")
    private Integer flowState;

    @ApiModelProperty(value = "处理定义ID")
    private String processDefinitionId;

    @ApiModelProperty(value = "流程状态编码")
    private String flowCode;

    @ApiModelProperty(value = "数据来源类型：1 飞控AI提取   2 飞控人工提报 3 比对系统")
    private Integer dataOrigin;

    @ApiModelProperty(value = "事件标签列表")
    private List<LupLabel> eventLabelList;

    @ApiModelProperty(value = "事件标签")
    private String eventLabel;

    @ApiModelProperty(value = "事件编号  工单编号")
    private String billNumber;

    @ApiModelProperty(value = "事件创建时间")
    private String createTime;

    @ApiModelProperty(value = "事件位置经度")
    private String locationLng;

    @ApiModelProperty(value = "事件位置纬度")
    private String locationLat;

    @ApiModelProperty(value = "事件位置纬度")
    private String taskNumber;

    @ApiModelProperty(value = "网格名称")
    private String gridName;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "航线名称")
    private String airLineName;

    @ApiModelProperty(value = "设备名称 机场名称")
    private String deviceName;

    @ApiModelProperty(value = "设备id")
    private Long deviceId;

    @ApiModelProperty(value = "飞行任务id")
    private Long flyTaskId;

    @ApiModelProperty(value = "飞行任务编号")
    private String flyTaskNumber;
    @ApiModelProperty(value = "飞行任务名称")
    private String flyTaskName;

    @ApiModelProperty(value = "设备序列号")
    private String deviceSn;

    @ApiModelProperty(value = "发生事件 基于一些场景 是准确的  比如交通拥堵时的拍摄时间 识别之后就是发生时间 其他的 静态物品  最多表达为 事件发现时间")
    private String happenedTime;

    @ApiModelProperty(value = "事件流程信息 json格式数据")
    private String eventFlowInfo;

    @ApiModelProperty(value = "扩展类数据：额外的数据")
    private String extraData;

    @ApiModelProperty(value = "扩展类数据：额外的数据")
    public ExtraDataVO extraDataVO;

}
