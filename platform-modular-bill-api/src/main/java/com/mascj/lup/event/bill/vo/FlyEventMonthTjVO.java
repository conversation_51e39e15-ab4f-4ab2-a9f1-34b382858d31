package com.mascj.lup.event.bill.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class FlyEventMonthTjVO implements Serializable {

    public FlyEventMonthTjVO(){

    }
    public FlyEventMonthTjVO(String month){
            this.month = month;
    }
    @ApiModelProperty("月")
    private String month;

    @ApiModelProperty("标签名")
    private String labelName;
    private Long labelId;
    @ApiModelProperty("标签数量")
    private Integer num;

}
