package com.mascj.lup.event.bill.constant;

public class VariableConstants {

    /**
     * 业务代码全局标识符
     */
    @Deprecated
    public static final String LM_HOLD_BIZ_CODE = "HOLD_BIZ_CODE";

    /**
     * 巡查批次代码
     */
    @Deprecated
    public static final String PATROL_BATCH_CODE = "PATROL_BATCH";

    /**
     * 流程标识符
     */
    public static final String FLOW_IDENTIFIER = "flow_identifier";
    /**
     * 审核结果
     */
    public static final String judge_result = "judgeResult";
    /**
     * 通过
     */
    public static final String judge_pass = "pass";
    /**
     * 驳回
     */
    public static final String judge_reject = "reject";

    /**
     * 驳回原因
     */
    public static final String reject_reason = "rejectReason";

    /**
     * 任务处理人的UserId
     */
    public static final String handler_user_id = "handlerUserId";

    /**
     * 任务处理人的姓名
     */
    public static final String handler_user_name = "handlerName";

    /**
     * 任务处理人的联系方式
     */
    public static final String handler_user_phone = "handlerPhone";

    /**
     * 任务处理人的角色
     */
    public static final String handler_user_role = "handlerRole";

    /**
     * 任务处理时间
     */
    public static final String handle_time = "handleTime";
    public static final String handlerName = "handlerName";
    public static final String handlerPhone = "handlerPhone";

    public static final String originalHandleName = "originalHandleName";
    public static final String originalHandlePhone = "originalHandlePhone";

    /**
     * 工单状态
     */
    public static final String bill_state = "billState";

    /**
     * 事件编码
     */
    public static final String event_code = "eventCode";

    /**
     * 处置方式
     */
    public static final String handle_type = "handleType";

    /**
     * 延期
     */
    public static final String delay = "delay";

    /**
     * 延期 日期
     */
    public static final String delay_date = "delayDate";

    /**
     * 溧水 延期时间
     */
    public static final String ls_delay_date = "lsDelayDate";

    /**
     * 溧水 申请延期时间
     */
    public static final String ls_apply_delay_date = "lsApplyDelayDate";

    /**
     * 办结标志
     */
    public static final String ban_jie_flag = "banJieFlag";

    /**
     * 督办标记
     */
    public static final String mark_supervise = "markSupervise";

    /**
     * 紧急标记
     */
    public static final String mark_alert = "markAlert";


    /**
     * 备注
     */
    public static final String remark = "remark";

    /**
     * 操作客户端
     */
    public static final String operatorClient = "operatorClient";

    /**
     * 补充材料描述
     */
    public static final String photoDescription = "photoDescription";

    /**
     * 工单来源编码
     */
    public static final String billSourceCode = "workBillSource";

    /**
     * 土地性质
     */
    public static final String landNatureCode = "landNature";

    /**
     * 巡查表单key
     */
    public static final String patrolCode = "patrol";

    /**
     * 执法表单key
     */
    public static final String handleCode = "handle";

    /**
     * 事件来源
     */
    public static final String EventOriginCode = "eventOrigin";
    /**
     * 巡查内容
     */
    public static final String protal_content = "protal_content";
    /**
     * 处置情况
     */
    public static final String handleReason = "handleReason";
    /**
     * 1 有事件  0没
     */
    public static final String hasEvent = "hasEvent";

    public static final String UNIT_SCOPE_CACHE = "unitScopeCache";

    public static final String UNIT_LEVEL_CACHE = "unitLevelCache";

    public static final String UNKNOWN_AREA_CODE = "WZQY";

    public     class  DATATYPE{
        /**
         *  月份
         */
        public  static final String MONTH = "month";
        /**
         * 日期
         */
        public static final  String DATE = "date";

    }

    //*********************************【S2.3.19】新增Code***********************************

    public static final String lupSystemTenant = "lup-system-tenant";
    public static final String lupHandleSystemTag = "lup-handle-system";
    /**
     * 处置系统模块配置
     */
    public static final String HandleModuleConfigCode = "handleModuleConfig";
    public static final String HandleModuleBillDataCode = "billData";
    public static final String HandleModuleBillDataQueryCode = "billDataQuery";

    public static final String RedisEventDataCode = "RedisEventDataConfig";

    public static final String FlowEntityTimeOutConfigCode = "FlowEntityTimeOut";
    public static final String FlowEntityTimeOutHourLength = "60";

    /**
     * 流程结束的
     */
    public static final Integer flowStateEnd = 4;

    public static final String LupBillTranslateConfigCode = "LupBillTranslate";

    public static final String EventHangingSwitch = "eventHangingSwitch";

    public static final String RabbitMQQueueNameForReceiveEventDataFromDimension= "com.mascj.event.bill.receive.data.from.dimension";
}
