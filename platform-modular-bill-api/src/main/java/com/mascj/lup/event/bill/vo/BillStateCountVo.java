package com.mascj.lup.event.bill.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 工单数量接口
 */
@Data
public class BillStateCountVo {

    @ApiModelProperty(value = "工单状态")
    private int flowState;

    @ApiModelProperty(value = "工单阶段名称")
    private String flowName;

    @ApiModelProperty(value = "标记编码")
    private String tagCode;

    @ApiModelProperty(value = "数量")
    private long count;

    public BillStateCountVo() {
    }

    public BillStateCountVo(int state) {
        this.flowState = state;
    }

    public BillStateCountVo(int state, long count) {
        this.flowState = state;
        this.count = count;
    }
}
