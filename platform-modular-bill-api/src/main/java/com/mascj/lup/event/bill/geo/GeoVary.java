package com.mascj.lup.event.bill.geo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "图斑数据",description = "{ \"type\": \"Feature\", \"properties\": { \"DKMJ\": \"62\", \"TYPE\": \"乱堆乱放\", \"HCPC\": \"2023-03\", \"SJPC\": \"2023-03\", \"WJBJ\": \"-1\", \"Enable\": \"1\", \"LASTTILE\": \"2023-03\", \"NEXTTILE\": \"2023-03\", \"center\": \"[31.73707101,119.05607757]\", \"COLOR\": \"#00ff00\", \"SHOW\": \"1\", \"DKBM\": \"KW00000004\", \"WGMC\": \"长乐社区\", \"WGDM\": \"320117102008000\", \"QYMC\": \"东屏街道\", \"QYDM\": \"320117102000000\", \"BZ\": \"\" }, \"geometry\": { \"type\": \"Polygon\", \"coordinates\": [ [ [ 119.056060, 31.737123 ], [ 119.056120, 31.737099 ], [ 119.056100, 31.737023 ], [ 119.056035, 31.737019 ], [ 119.056060, 31.737123 ] ] ] } }")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GeoVary {

    @ApiModelProperty(value = "类型",example = "Feature")
    private String type;

    @ApiModelProperty(value = "属性")
    private GeoProperty properties;

    @ApiModelProperty(value = "图斑坐标属性")
    private Geometry geometry;
}
