package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * {
 *                 total:100,
 *                 xArr:['11-01','11-02','11-03',……],
 *                 rengongduibi:[10,10,0,……],
 *                 AI:[10,10,0,……],
 *                 rengongtibao:[10,10,0,……],
 *             }
 * <AUTHOR>
 * @date 2023/11/13 19:29
 * @describe
 */
@Data
@ApiModel(value = "统计事件数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CountEventDataByDealResultTimeVO {

    @ApiModelProperty(value = "总数")
    private int total;

    @ApiModelProperty(value = "横坐标")
    private List<String> xLabel;


    @ApiModelProperty(value = "已处理结束 1")
    private List<Integer> dealDoneCount;
    @ApiModelProperty(value = "未处理结束 0")
    private List<Integer> unDealDoneCount;

}
