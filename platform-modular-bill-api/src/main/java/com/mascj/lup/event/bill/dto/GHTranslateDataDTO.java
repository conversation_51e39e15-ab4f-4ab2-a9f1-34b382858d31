package com.mascj.lup.event.bill.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/8 20:27
 * @describe
 */
@Data
public class GHTranslateDataDTO {
    @ApiModelProperty(value = "无人机名称")
    private String uavName;
    @ApiModelProperty(value = "巡查日期: 年月日")
    private String eventDate;
    @ApiModelProperty(value = "采集时间：年月日时分秒")
    private String actionDate;
    @ApiModelProperty(value = "事件标题")
    private String eventTitle;

    @ApiModelProperty(value = "事件描述")
    private String eventDesc;

    @ApiModelProperty(value = "事件GPS位置 举例： 118.55490112304688,31.697551727294922")
    private String location;

    @ApiModelProperty(value = "附件列表")
    private List<GHTranslateCommonResourcesDTO> commonResourcesDTOS;

}
