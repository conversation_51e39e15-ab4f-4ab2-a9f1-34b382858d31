package com.mascj.lup.event.bill.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;

/**
 * <p>
 * 工单通知
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Data
@TableName("lup_bill_notification")
public class LupBillNotification extends BaseEntity{

    private static final long serialVersionUID=1L;

    /**
     * 是否已删除 0否 1是
     */
    private Integer deleted;

    /**
     * 租户id 
     */
    private Long tenantId;

    /**
     * 工单id
     */
    private Long billId;

    /**
     * 流程id
     */
    private Long flowId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 状态：0 未发 1已发
     */
    private Integer status;

    /**
     * 定时类短信  0 否  1 是
     */
    private Integer timerSms;

    /**
     * 流程状态
     */
    private  Integer flowState;

    /**
     * 流程当前任务
     */
    private Long processTaskId;

}
