package com.mascj.lup.event.bill.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.MergePictureDTO;
import com.mascj.lup.event.bill.dto.MergePictureTileDTO;
import com.mascj.lup.event.bill.enums.PictureMergeSourceMode;
import com.mascj.lup.event.bill.geo.DataPicPoint;
import com.mascj.lup.event.bill.util.clip.ClipTailsUtil;
import com.mascj.lup.event.bill.util.clip.ShapeJson;

import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class    MergeImageUtil {

    private static int zoom = 20;

    private static int buffer = 50;

    private static BufferedImage createFile(String fileName, List<MergePictureTileDTO> tileList, String shape, boolean isDashed){
        return createFile(buffer,fileName,tileList,shape,isDashed,null,true);
    }

    private static BufferedImage createFile(Integer renderBuffer,String fileName, List<MergePictureTileDTO> tileList, String shape, boolean isDashed,String lineColor,boolean drawLine){
        try{
            ShapeJson shapeJson = JSONUtil.toBean(shape,ShapeJson.class);
            BufferedImage image = ClipTailsUtil.getBufferedImage(JSONUtil.toJsonStr(
                    shapeJson.getGeometry()), tileList,MergeImageUtil.zoom,renderBuffer,isDashed,lineColor,drawLine);
            return image;
        }catch (Exception exp){
            exp.printStackTrace();
        }
        return null;
    }

    private static BufferedImage createFile(Integer renderBuffer,int zoom,String fileName, List<MergePictureTileDTO> tileList, String shape, boolean isDashed,String lineColor,boolean drawLine){
        try{
            ShapeJson shapeJson = JSONUtil.toBean(shape,ShapeJson.class);
            BufferedImage image = ClipTailsUtil.getBufferedImage(JSONUtil.toJsonStr(
                    shapeJson.getGeometry()), tileList,zoom,renderBuffer,isDashed,lineColor,drawLine);
            return image;
        }catch (Exception exp){
            exp.printStackTrace();
        }
        return null;
    }


    private static InputStream imageToStream(BufferedImage image){
        try
        {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "JPEG", byteArrayOutputStream);
            byte[] bytes = byteArrayOutputStream.toByteArray();
            byteArrayOutputStream.close();
            InputStream inputStream = new ByteArrayInputStream(bytes);
            return inputStream;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public static MultipartFile createMultipartFile(MergePictureDTO mergePictureDTO){

        try {
            InputStream inputStream = null;
            BufferedImage targetBufferedImage = null;

            if(mergePictureDTO.getSourceMode() == PictureMergeSourceMode.TileMode) {

                //瓦片切图
                BufferedImage currentBufferedImage = createFile(mergePictureDTO.getRenderBuffer(),mergePictureDTO.getZoom(),mergePictureDTO.getCurrentPicName(), mergePictureDTO.getCurrentTileList(), mergePictureDTO.getShape(), false,mergePictureDTO.getLineColor(),mergePictureDTO.isDrawLine());
                mergePictureDTO.setCurrentBufferedImage(currentBufferedImage);
                //是否有两期图片
                if (mergePictureDTO.getIsCompareMode()) {
                    BufferedImage compareBufferedImage = createFile(mergePictureDTO.getRenderBuffer(),mergePictureDTO.getZoom(),mergePictureDTO.getComparePicName(), mergePictureDTO.getCompareTileList(), mergePictureDTO.getShape(), true,mergePictureDTO.getLineColor(),mergePictureDTO.isDrawLine());
                    mergePictureDTO.setCompareBufferedImage(compareBufferedImage);
                }

                //生成指定的图片
                targetBufferedImage = mergeImg(mergePictureDTO);

            } else if (mergePictureDTO.getSourceMode() == PictureMergeSourceMode.PicMode) {
                //图片模式的数据
                targetBufferedImage = makePicFile(mergePictureDTO);
            }

            inputStream = imageToStream(targetBufferedImage);

            String imgName = mergePictureDTO.getFileName() + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".jpg";
            if (inputStream != null) {
                MultipartFile file2 = FileUtils.getMultipartFile(inputStream, imgName);
                return file2;
            }

            inputStream.close();
            targetBufferedImage=null;
        }catch (Exception exception){
            exception.printStackTrace();
        }

        return null;
    }


    private static BufferedImage mergeImg(MergePictureDTO mergePictureDTO){

        // 读取两张图像
        BufferedImage image1 = mergePictureDTO.getCurrentBufferedImage();
        BufferedImage image2 = mergePictureDTO.getCompareBufferedImage();
        if(image2==null){
            return produceImg(mergePictureDTO);
        }

        // 计算分割线的左右坐标
        int leftX = image1.getWidth() / 2 - 50; // 分割线左边的位置
        int rightX = image1.getWidth() / 2 + 50; // 分割线右边的位置
        int topY = image1.getHeight() / 2; // 分割线上方的位置
        int bottomY = image1.getHeight() / 2 + 50; // 分割线下方的位置

        // 创建画布并设置画布的背景色为白色
        BufferedImage canvas = new BufferedImage(
                image2.getWidth()+image1.getWidth()+50,
                image1.getHeight(),
                BufferedImage.TYPE_INT_RGB);
        Graphics2D g = canvas.createGraphics();
        g.setColor(Color.white);
        g.fillRect(image1.getWidth(), 0, 50, image1.getHeight());
        // 将两张图像绘制在画布上
        g.drawImage(image1, 0, 0, image1.getWidth(), image1.getHeight(), null);
        g.drawImage(image2, image1.getWidth()+50, 0,image2.getWidth() , image2.getHeight(), null);

        String text1 = mergePictureDTO.getCurrentPicName();
        String text2 = mergePictureDTO.getComparePicName();
        g.setFont(new Font("仿宋", Font.BOLD, 40));
//        g.font

        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,
                RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_SPEED);

        g.setColor(Color.white);
        g.fillRect(image1.getWidth(), 0, 50, image1.getHeight());
        g.setPaint(Color.white);
        g.drawString(text1, image1.getWidth() / 2 - (text1.length()*40)/2, image1.getHeight() - 10);
        g.drawString(text2, image1.getWidth()+50+(image2.getWidth() / 2 - (text2.length()*40)/2), image2.getHeight() - 10);

        // 绘制分割线
        float[] dash = {10.0f}; // 虚线的长度和间隔
        BasicStroke dashedStroke = new BasicStroke(1.0f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER, 10.0f, dash, 0.0f);
        g.setStroke(dashedStroke);
        g.setColor(Color.GRAY);
        g.drawLine((image1.getWidth()+50+image2.getWidth())/2, 0, (image1.getWidth()+50+image2.getWidth())/2, image1.getHeight());


        // 将合成后的图像保存到文件中
        // 释放资源
        g.dispose();
        canvas.flush();

        return canvas;

    }
    private static BufferedImage produceImg(MergePictureDTO mergePictureDTO){

        // 读取两张图像
        BufferedImage image1 = mergePictureDTO.getCurrentBufferedImage();

        // 计算分割线的左右坐标
        int leftX = image1.getWidth() / 2 - 50; // 分割线左边的位置
        int rightX = image1.getWidth() / 2 + 50; // 分割线右边的位置
        int topY = image1.getHeight() / 2; // 分割线上方的位置
        int bottomY = image1.getHeight() / 2 + 50; // 分割线下方的位置

        // 创建画布并设置画布的背景色为白色
        BufferedImage canvas = new BufferedImage(
                image1.getWidth(),
                image1.getHeight(),
                BufferedImage.TYPE_INT_RGB);
        Graphics2D g = canvas.createGraphics();

        // 将两张图像绘制在画布上
        g.drawImage(image1, 0, 0, image1.getWidth(), image1.getHeight(), null);

        String text1 = mergePictureDTO.getCurrentPicName();

        g.setFont(new Font("仿宋", Font.BOLD, 40));

        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,
                RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_SPEED);

        g.setColor(Color.white);
        g.fillRect(image1.getWidth(), 0, 50, image1.getHeight());
        g.setPaint(Color.white);
        g.drawString(text1, image1.getWidth() / 2 - (text1.length()*40)/2, image1.getHeight() - 10);

        // 绘制分割线
        float[] dash = {10.0f}; // 虚线的长度和间隔
        BasicStroke dashedStroke = new BasicStroke(1.0f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER, 10.0f, dash, 0.0f);
        g.setStroke(dashedStroke);
        g.setColor(Color.GRAY);

        // 将合成后的图像保存到文件中
        // 释放资源
        g.dispose();
        canvas.flush();

        return canvas;

    }

    public static BufferedImage makePicFile(MergePictureDTO mergePictureDTO){

        mergePictureDTO.setCurrentBufferedImage(drawingPolygon_Image(mergePictureDTO.getCurrentPicUrl(),mergePictureDTO.getCurrentPicName(),mergePictureDTO.getPixelPointList(),false));

        if(mergePictureDTO.getIsCompareMode()){
            mergePictureDTO.setCompareBufferedImage(
                    drawingPolygon_Image(mergePictureDTO.getComparePicUrl(),mergePictureDTO.getComparePicName(),mergePictureDTO.getPixelPointList(),true)
            );
        }

        return mergeImg(mergePictureDTO);
    }

    /**
     * 绘制图片多边图形
     * @param imgUrl
     * @param pixelPointList
     * @return
     */
    public static BufferedImage drawingPolygon_Image( String imgUrl,String name,List<DataPicPoint> pixelPointList,Boolean isDashed) {
        try {
            Matcher matcher =Pattern.compile("[\\u4e00-\\u9fa5]|[\\u3002\\uff1b\\uff0c\\uff1a\\u201c\\u201d\\uff08\\uff09\\u3001\\uff1f\\u300a\\u300b\\u3010\\u3011]").matcher(imgUrl);
            while (matcher.find()) {
                String tmp = matcher.group();
                try {
                    imgUrl = imgUrl.replaceAll(tmp, java.net.URLEncoder.encode(tmp, "utf-8"));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }


            URL url = new URL(imgUrl);

            BufferedImage image = ImageIO.read(url);
            Graphics2D g =(Graphics2D) image.getGraphics();
            g.setColor(Color.RED);//画笔颜色
            g.setStroke(new BasicStroke(3,BasicStroke.CAP_ROUND,BasicStroke.JOIN_ROUND));//设置线宽

            if(isDashed){
                float[] dash = {10.0f}; // 虚线的长度和间隔
                BasicStroke dashedStroke = new BasicStroke(3.0f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER, 10.0f, dash, 0.0f);
                g.setStroke(dashedStroke);
            }

            int x[] = new int[pixelPointList.size()];

            for (int m =0;m<pixelPointList.size();m++) {
                x[m] = pixelPointList.get(m).getX().intValue();
            }

            int y[] = new int[pixelPointList.size()];
            for (int m =0;m<pixelPointList.size();m++) {
                y[m] = pixelPointList.get(m).getY().intValue();
            }
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            g.drawPolygon(x,y,pixelPointList.size());// 画多边形
            g.dispose();
            return image;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
    public static void main(String[ ] args) throws Exception {

//        MergePictureDTO mergePictureDTO = new MergePictureDTO();
//
//        mergePictureDTO.setSourceMode(PictureMergeSourceMode.PicMode);
//        mergePictureDTO.setShape("[{\"x\":1405.1466052192982,\"y\":996.5646119076378},{\"x\":1507.0358727001626,\"y\":2555.90953352636},{\"x\":3465.0768699189075,\"y\":1009.8545486900753},{\"x\":3075.240166343835,\"y\":2688.8082253930343},{\"x\":1405.1466052192982,\"y\":996.5646119076378}]");
//
//        List<DataPicPoint> shapeJson = JSONUtil.toList(mergePictureDTO.getShape(),DataPicPoint.class);
//        mergePictureDTO.setCurrentPicUrl("https://lywl-uav.oss-cn-shanghai.aliyuncs.com/dj/1663028151688167426/DJI_202305291143_001_1663028151688167426/DJI_20230529114455_0002_W_正射原图.jpeg");
//        mergePictureDTO.setPixelPointList(shapeJson);

        String url="https://lywl-uav.oss-cn-shanghai.aliyuncs.com/dj/1663028151688167426/DJI_202305291143_001_1663028151688167426/DJI_20230529114455_0002_W_正射原图.jpeg";
        Matcher matcher =Pattern.compile("[\\u4e00-\\u9fa5]|[\\u3002\\uff1b\\uff0c\\uff1a\\u201c\\u201d\\uff08\\uff09\\u3001\\uff1f\\u300a\\u300b\\u3010\\u3011]").matcher(url);
        while (matcher.find()) {
            String tmp = matcher.group();
            try {
                url = url.replaceAll(tmp, java.net.URLEncoder.encode(tmp, "utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }

        System.out.println(url);



//        mergePictureDTO.setShape("{\"type\":\"Feature\",\"geometry\":{\"coordinates\":[[[118.46755360116987,31.706283405815853],[118.46751537969138,31.706184713646834],[118.4673893158677,31.70621951263172],[118.4674282078984,31.70631478191696],[118.46755360116987,31.706283405815853]]],\"type\":\"Polygon\"},\"properties\":{}}");
//        mergePictureDTO.setCurrentPicName("2022-10月雨山区");
//        mergePictureDTO.getCurrentTileList().add("https://blmtiles.mascj.com:433/AHS/MASS/ShiChengGuanJuChouCha/2022-10/Tile_20221022_YuShanQu1/{z}/{x}/{y}.png");
//
//        mergePictureDTO.setIsCompareMode(true);
//        mergePictureDTO.setComparePicName("2021-10月雨山区");
//        mergePictureDTO.getCompareTileList().add("https://blmtiles.mascj.com:433/AHS/MASS/ShiChengGuanJuChouCha/2021-10/Tile_20211019_Part_YuShanQu1/{z}/{x}/{y}.png");
//        createMultipartFile(mergePictureDTO);
//        makePicFile(mergePictureDTO);

//        https://blmtiles.mascj.com:433/AHS/MASS/ShiChengGuanJuChouCha/2021-10/Tile_20211019_Part_YuShanQu1/{z}/{x}/{y}.png
//        List<String> tileList = new ArrayList<>();
//        tileList.add("https://blmtiles.mascj.com:433/AHS/MASS/ShiChengGuanJuChouCha/2022-10/Tile_20221022_YuShanQu1/{z}/{x}/{y}.png");
//        String shape = "{\"type\":\"Feature\",\"geometry\":{\"coordinates\":[[[118.46755360116987,31.706283405815853],[118.46751537969138,31.706184713646834],[118.4673893158677,31.70621951263172],[118.4674282078984,31.70631478191696],[118.46755360116987,31.706283405815853]]],\"type\":\"Polygon\"},\"properties\":{}}";
//        createFile("aa",tileList,shape);
//
//        String dir ="/Users/<USER>/Desktop/img-res/";
//        // 读取两张图像
//        BufferedImage image1 = ImageIO.read(new File(dir+"image1.png"));
//        BufferedImage image2 = ImageIO.read(new File(dir+"image2.png"));
//
//        // 计算分割线的左右坐标
//        int leftX = image1.getWidth() / 2 - 50; // 分割线左边的位置
//        int rightX = image1.getWidth() / 2 + 50; // 分割线右边的位置
//        int topY = image1.getHeight() / 2; // 分割线上方的位置
//        int bottomY = image1.getHeight() / 2 + 50; // 分割线下方的位置
//
//        // 创建画布并设置画布的背景色为白色
//        BufferedImage canvas = new BufferedImage(
//                image2.getWidth()+image1.getWidth()+50,
//                image1.getHeight(),
//                BufferedImage.TYPE_INT_RGB);
//        Graphics2D g = canvas.createGraphics();
//        g.setColor(Color.yellow);
//        g.fillRect(image1.getWidth(), 0, 50, image1.getHeight());
//
//        // 将两张图像绘制在画布上
//        g.drawImage(image1, 0, 0, image1.getWidth(), image1.getHeight(), null);
//        g.drawImage(image2, image1.getWidth()+50, 0,image2.getWidth() , image2.getHeight(), null);
//
//        String text1 = "2023-10";
//        String text2 = "2023-05";
//        g.setFont(new Font("宋体", Font.PLAIN, 18));
//
//        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,
//                RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
//        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
//
//        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
//        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_SPEED);
//
//        g.setColor(Color.yellow);
//        g.fillRect(image1.getWidth(), 0, 50, image1.getHeight());
//        g.setPaint(Color.BLACK);
//        g.drawString(text1, image1.getWidth() / 2 - 30, image1.getHeight() - 10);
//        g.drawString(text2, image1.getWidth()+50+(image2.getWidth() / 2 - 30), image2.getHeight() - 10);
//
//        // 绘制分割线
//        float[] dash = {10.0f}; // 虚线的长度和间隔
//        BasicStroke dashedStroke = new BasicStroke(1.0f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER, 10.0f, dash, 0.0f);
//        g.setStroke(dashedStroke);
//        g.setColor(Color.GRAY);
//        g.drawLine((image1.getWidth()+50+image2.getWidth())/2, 0, (image1.getWidth()+50+image2.getWidth())/2, image1.getHeight());
//
//
//        // 将合成后的图像保存到文件中
//        ImageIO.write(canvas, "png", new File(dir+"merged_image.png"));
//
//        // 释放资源
//        g.dispose();
//        canvas.flush();
    }
}