package com.mascj.lup.event.bill.geo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "属性")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GeoProperty {

    @ApiModelProperty(value="地块面积",example = "168")
    private String dkmj;

    @ApiModelProperty(value="图斑类型",example = "例如：乱堆乱放、堆场、乱搭乱建蓝皮房、垃圾污水等等，由数据团队定义的")
    private String type;

    @ApiModelProperty(value="航测批次",example = "2022-08")
    private String hcpc;

    @ApiModelProperty(value="数据批次",example = "2022-08")
    private String sjpc;

    @ApiModelProperty(value="违建标记",example = "-1")
    private String wjbj;

    @ApiModelProperty(value="是否可用",example = "1")
    private String enable;
    @ApiModelProperty(value="对比批次名称",example = "2022-06")
    private String lastBatch;
    @ApiModelProperty(value="当前批次名称",example = "2022-08")
    private String currentBatch;
    @ApiModelProperty(value = "图斑中心",example = "[31.65449417,118.89958307]")
    private String center;
    @ApiModelProperty(value = "图斑颜色",example = "#00ff00")
    private String color;
    @ApiModelProperty(value = "是否展示",example = "1")
    private String show;
    @ApiModelProperty(value = "地块编码")
    private String dkbm;
    @ApiModelProperty(value = "网格名称")
    private String wgmc;
    @ApiModelProperty(value = "网格代码")
    private String wgdm;
    @ApiModelProperty(value = "区域名称")
    private String qymc;

    @ApiModelProperty(value = "区域代码")
    private String qydm;

    @ApiModelProperty(value = "备注信息")
    private String bz;
}
