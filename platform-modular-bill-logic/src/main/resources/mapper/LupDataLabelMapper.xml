<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupDataLabelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mascj.lup.event.bill.entity.LupDataLabel">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="project_id" property="projectId" />
        <result column="data_id" property="dataId" />
        <result column="label_id" property="labelId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, create_by, update_time, update_by, deleted, tenant_id, project_id, data_id, label_id
    </sql>

</mapper>
