<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupBatchSourceMapper">

    <select id="listByBatch" resultType="com.mascj.lup.event.bill.entity.LupSource">
        SELECT DISTINCT source.* from
        lup_source source
         join lup_batch_source batch_source on source.id = batch_source.source_id
        WHERE batch_source.batch_id = #{batchId};
    </select>
</mapper>
