<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupBillReadMapper">


    <select id="countBillUnReadCount" resultType="com.mascj.lup.event.bill.vo.BillReadStateCounterVO">
        SELECT COUNT(1) AS read_state_count FROM (
                                                     SELECT bill.*,IFNULL((
                                                         SELECT bill_read.read_state
                                                         FROM lup_bill_read bill_read
                                                         WHERE
                                                             bill_read.process_task_id = bill.process_task_id and
                                                             bill_read.is_deleted=0 and
                                                             bill_read.flow_id=flow.id AND bill_read.user_id=#{userId}

                                                               <if test="dataDTO.billFlowState != null and dataDTO.billFlowState > 0">
                                                                and bill.flow_state=#{dataDTO.billFlowState}
                                                               </if>

                                                         ORDER BY bill_read.create_time DESC LIMIT 1),0) AS read_state FROM v_bill_data bill JOIN lup_flow_entity flow ON bill.flow_entity_id=flow.id

                                                                                                                       <where>
                                                                                                                           <if test="dataDTO.billFlowState != null and dataDTO.billFlowState > 0">
                                                                                                                               and bill.flow_state=#{dataDTO.billFlowState}
                                                                                                                           </if>
                                                                                                                       </where>

                                                                                                                       ) AS bill_read
                                        WHERE bill_read.read_state=#{dataDTO.billReadState}
    </select>

    <select id="countByUserId" resultType="Integer" >

        SELECT COUNT(1) as c FROM lup_bill_read bill_read WHERE is_deleted=0 and  user_id=#{userId}
                                                            and flow_id=#{dataDTO.flowId}
                                                            and process_task_id=#{dataDTO.processTaskId}
    </select>

</mapper>
