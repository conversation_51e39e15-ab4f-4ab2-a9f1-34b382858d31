<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupLabelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mascj.lup.event.bill.entity.LupLabel">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="project_id" property="projectId" />
        <result column="name" property="name" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, create_by, update_time, update_by, deleted, tenant_id, project_id, name
    </sql>

    <select id="labelList" resultType="com.mascj.lup.event.bill.vo.EventDataLabelVO">
        select id,`name` from lup_label where  lup_label.deleted=0  ORDER BY lup_label.create_time DESC
    </select>

    <select id="listLabel" resultType="com.mascj.lup.event.bill.vo.LabelItemVO">

        SELECT DISTINCT
          label.id, label.`name` as label_name, label.create_time
        from lup_bill bill
        join lup_flow_entity flow on flow.id = bill.flow_entity_id
        join lup_data_label dataLabel on dataLabel.data_id = bill.data_id
        join lup_label label on label.id = dataLabel.label_id
        JOIN lup_data ldata on ldata.id = bill.data_id

        <where>
            bill.deleted = 0 and flow.flow_state>0

            <if test="projectId != null and projectId != ''">
                and  bill.project_id = #{projectId}
            </if>

            <if test=" billDTO.dateStart != '' and billDTO.dateStart != null and billDTO.dateEnd != '' and billDTO.dateEnd != null ">
                and bill.create_time BETWEEN #{billDTO.dateStart} and #{billDTO.dateEnd}
            </if>

            <if test="billDTO.eventLabel != null and billDTO.eventLabel != ''">
                and label.name  like concat('%',#{billDTO.eventLabel},'%')
            </if>

            <if test="billDTO.labelIdListPerm != null and billDTO.labelIdListPerm.size > 0">
                and label.id in
                <foreach collection="billDTO.labelIdListPerm" separator="," open="(" item="item" index="index" close=")">
                    #{item}
                </foreach>
            </if>

        </where>

        order by label.create_time desc

    </select>


    <select id="findLabelByDataId" resultType="com.mascj.lup.event.bill.entity.LupLabel">
        SELECT DISTINCT ll.* FROM lup_data_label ldl join lup_label ll on ll.id = ldl.label_id
        WHERE ldl.data_id=#{dataId} and ldl.deleted=0
    </select>
</mapper>
