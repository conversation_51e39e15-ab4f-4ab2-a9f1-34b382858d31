<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupBillPendingMapper">

    <select id="queryHistoryPendingBill" resultType="com.mascj.lup.event.bill.vo.LupBillPendingVO">
        SELECT DISTINCT
            bill.id,bill.tenant_id,bill.pending_state,flow_entity.process_task_id
        FROM
            lup_flow_entity flow_entity
                JOIN lup_bill bill ON bill.flow_entity_id = flow_entity.id
                JOIN lup_data ldata on ldata.id = bill.data_id
        WHERE
            handle_type = 3 and bill.deleted=0 and ldata.deleted=0 and flow_entity.flow_state !=4
    </select>

</mapper>
