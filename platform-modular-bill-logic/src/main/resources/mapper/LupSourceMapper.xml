<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupSourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mascj.lup.event.bill.entity.LupSource">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="project_id" property="projectId" />
        <result column="type" property="type" />
        <result column="original_source_id" property="originalSourceId" />
        <result column="url" property="url" />
        <result column="min_zoom" property="minZoom" />
        <result column="max_zoom" property="maxZoom" />
        <result column="bounds" property="bounds" />
        <result column="fly_date" property="flyDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, create_by, update_time, update_by, deleted, tenant_id, project_id, type, original_source_id, url, min_zoom, max_zoom, bounds, fly_date
    </sql>

    <select id="listSourceTile" resultType="com.mascj.lup.event.bill.entity.LupSource">

        SELECT *
            FROM
                lup_source source

            <include refid="sourceSearchCondition"></include>

        order by source.fly_date DESC
    </select>


    <sql id="sourceSearchCondition">

        <where>
            source.type IN ( 1,3 ) and
            source.deleted = 0  and  source.project_id = #{billDTO.projectId}

            <if test=" billDTO.dateStart != '' and billDTO.dateStart != null and billDTO.dateEnd != '' and billDTO.dateEnd != null ">
                and source.fly_date BETWEEN #{billDTO.dateStart} and #{billDTO.dateEnd}
            </if>

        </where>

    </sql>

</mapper>
