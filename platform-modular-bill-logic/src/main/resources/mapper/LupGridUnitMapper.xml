<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupGridUnitMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, code, pid, hierarchy, polygon, centriod, geometry_str, centriod_str, grid_area, description, status, sort, create_time, create_by, update_time, update_by, is_deleted, tenant_id
    </sql>



    <insert id="batchImport">
        INSERT INTO lup_grid_unit (
        `name`,`code`,
        `hierarchy`,`polygon`,
        `centriod`,`grid_area`,`description`,`geometry_str`,`centriod_str`,project_id)
        VALUES
        <foreach collection="list" item="unit" separator=",">
            (#{unit.name}, #{unit.code},
            #{unit.hierarchy}, ST_GeomFromText(#{unit.polygon}),
            ST_GeomFromText(#{unit.centriod}), #{unit.gridArea}, #{unit.description}, #{unit.geometryStr},
            #{unit.centriodStr},
            #{unit.projectId})
        </foreach>
    </insert>

    <insert id="batchImportInit">

        INSERT INTO lup_grid_unit (
        `name`,`code`,
        `hierarchy`,`polygon`,`geometry_str`,`centriod_str`,project_id,tenant_id)
        VALUES
        <foreach collection="list" item="unit" separator=",">
            (#{unit.name}, #{unit.code},
            #{unit.hierarchy},ST_GeomFromText(#{unit.polygon}), #{unit.geometryStr},
            #{unit.centriodStr},
            #{unit.projectId},
            #{unit.tenantId})
        </foreach>

    </insert>


    <update id="updateUnitById">
        update lup_grid_unit
        set `name`         = #{unit.name},
        `code`         = #{unit.code},
        project_id = #{unit.projectId},
        `hierarchy`    = #{unit.hierarchy},
        `polygon`= ST_GeomFromText(#{unit.polygon}),
        `centriod`= ST_GeomFromText(#{unit.centriod}),
        `grid_area`= #{unit.gridArea},
        `description`= #{unit.description},
        `geometry_str` = #{unit.geometryStr},
        `centriod_str` = #{unit.centriodStr}
        WHERE id = #{unit.id}
    </update>

    <update id="batchUpdatePid">
        update lup_grid_unit set
        pid =
        <foreach collection="list" item="udo" separator=" " open="case id" close="end">
            when #{udo.id} then #{udo.pid}
        </foreach>
        where id in
        <foreach collection="list" item="udo" separator="," open="(" close=")">
            #{udo.id}
        </foreach>
    </update>

    <update id="batchUpdateHhierarchy">
        update lup_grid_unit set
        hierarchy =
        <foreach collection="list" item="udo" separator=" " open="case id" close="end">
            when #{udo.id} then #{udo.hierarchy}
        </foreach>
        where id in
        <foreach collection="list" item="udo" separator="," open="(" close=")">
            #{udo.id}
        </foreach>
    </update>


    <select id="findGridByPoint" resultType="com.mascj.lup.event.bill.vo.LupGridUnitVO">
        SELECT
        a.id,
        a.NAME,
        a.CODE,
        a.pid
        FROM
        lup_grid_unit a

        WHERE a.is_deleted = 0

        <if test="projectId != null and projectId != ''">
            and a.project_id = #{projectId}
        </if>

        and  ST_Within(ST_GeomFromText(#{centerPoint}), `polygon`)

    </select>

    <select id="findDataScope" resultType="com.mascj.lup.event.bill.vo.LupGridUnitVO">
        SELECT  DISTINCT b.id,b.pid,b.hierarchy from lup_grid_user a
                                                    join lup_grid_unit b on a.grid_unit_id=b.id
        WHERE user_id=#{userId} and a.deleted=0;
    </select>
    <select id="findGridUnitRootNode" resultType="com.mascj.lup.event.bill.vo.LupGridUnitVO">
        SELECT
        a.id,
        a.NAME,
        a.CODE,
        a.pid,
        a.hierarchy
        FROM
        lup_grid_unit a

        WHERE a.is_deleted = 0

        <if test="projectId != null and projectId != ''">
            and a.project_id = #{projectId}
        </if>

        <if test="list != null and list.size > 0">
            and a.id in
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>


        order by a.sort asc
    </select>

    <select id="getOneGridUnitVO" resultType="com.mascj.lup.event.bill.vo.LupGridUnitVO">
        SELECT
            a.id,
            a.NAME,
            a.CODE,
            a.pid,
            a.hierarchy
        FROM
            lup_grid_unit a

        WHERE a.is_deleted = 0
         and a.id = #{id}
    </select>

</mapper>
