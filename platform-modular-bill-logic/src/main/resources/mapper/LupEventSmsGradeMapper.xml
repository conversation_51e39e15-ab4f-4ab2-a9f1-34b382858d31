<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupEventSmsGradeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mascj.lup.event.bill.entity.LupEventSmsGrade">
        <id column="id" property="id" />
        <result column="label_id" property="labelId" />
        <result column="type" property="type" />
        <result column="patrol_sms" property="patrolSms" />
        <result column="handle_sms" property="handleSms" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, label_id, type, patrol_sms, handle_sms, tenant_id, create_by, create_time, update_by, update_time, deleted
    </sql>

</mapper>