<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupLabelFlowMapper">

    <select id="labelFlowPage" resultType="com.mascj.lup.event.bill.vo.LupLabelFlowVO">
        SELECT
            t1.`name` as label_name,t1.id as label_id
        FROM
            lup_label t1
                LEFT JOIN lup_data_label t2 ON t1.id = t2.label_id
                LEFT JOIN lup_data t3 on t3.id = t2.data_id
        WHERE t3.deleted = 0
          and t3.data_origin = #{codeValue} GROUP BY t1.`name` , t1.id
    </select>

    <select id="labelFlowDetailByState" resultType="com.mascj.lup.event.bill.entity.LupLabelFlow">
        SELECT
            DISTINCT	label_flow.*
        FROM
            lup_data_label data_label
                JOIN lup_bill bill ON data_label.data_id = bill.data_id
                JOIN lup_flow_entity flow_entity ON flow_entity.id = bill.flow_entity_id
                JOIN lup_label label ON label.id = data_label.label_id
                JOIN lup_label_flow label_flow ON label_flow.label_id = label.id
                JOIN lup_label_flow label_flow_state ON label_flow_state.flow_state = flow_entity.flow_state
        WHERE
            flow_entity.id = #{flowId}
          AND bill.usable = 1
          AND bill.deleted = 0
          AND label_flow.flow_state = #{flowState}
          AND label_flow.deleted = 0;
    </select>

</mapper>
