<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.DataReportMapper">
    <select id="listPage" resultType="com.mascj.lup.event.bill.vo.DataReportVO">
        SELECT
        a.id,
        a.current_line,
        a.total_line,
        a.file_name,
        a.app_name,
        a.state,
        a.download_url,
        a.search_condition,
        b.id AS template_id,
        b.file_template_url,
        b.report_type_tag,
        b.org_id,
        a.execute_remark,
        a.batch_code
        FROM
        aep_report a
        INNER JOIN aep_report_template b ON a.template_id = b.id
        WHERE
        a.deleted = 0
        AND b.deleted = 0
        <if test="search.fileName != null and search.fileName != ''">
            AND a.file_name like concat('%',#{search.fileName},'%')
        </if>
        <if test="search.appCode != null and search.appCode != ''">
            AND a.app_code = #{search.appCode}
        </if>
        <if test="search.timeValue != null and search.timeValue != ''">
            AND a.create_time >= #{search.beginTime}
                       AND a.create_time <![CDATA[<=]]> #{search.endTime}
        </if>
        ORDER BY a.create_time DESC
    </select>

    <update id="updateSearchParam" >
        UPDATE aep_report SET search_condition = #{searchParam}  WHERE `id` = #{id};
    </update>

    <!-- 数据迁移 -->

    <select id="getAllTableNames" resultType="com.mascj.lup.event.bill.vo.TableDataVO">
        SELECT table_name FROM information_schema.`TABLES` WHERE  information_schema.`TABLES`.TABLE_SCHEMA=#{moduleCodeDBName};
    </select>

    <select id="listAllDataExport" resultType="Map">
        select

            <foreach collection="clist" separator="," open="" item="item" index="index" close="">${item}</foreach>

        from ${tableName}
    </select>

    <!-- 数据迁移 -->
</mapper>
