<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupEventLabelMapper">

    <select id="listPage" resultType="com.mascj.lup.event.bill.vo.LupEventLabelVo">
        SELECT
            t1.`name` as label_name,t1.id as label_id
        FROM
            lup_label t1
                LEFT JOIN lup_data_label t2 ON t1.id = t2.label_id
                LEFT JOIN lup_data t3 on t3.id = t2.data_id  WHERE t3.data_origin = #{codeValue} GROUP BY t1.`name` , t1.id
    </select>

    <select id="listLabelTree" resultType="com.mascj.lup.event.bill.vo.LupCommonTreeVO">
        SELECT
            t1.`name` AS `value`,t3.data_origin as parent_id,
            t1.id AS id
        FROM
            lup_label t1
                LEFT JOIN lup_data_label t2 ON t1.id = t2.label_id
                LEFT JOIN lup_data t3 ON t3.id = t2.data_id

        WHERE t3.deleted=0
        GROUP BY t1.`name` , t1.id,t3.data_origin
    </select>



</mapper>
