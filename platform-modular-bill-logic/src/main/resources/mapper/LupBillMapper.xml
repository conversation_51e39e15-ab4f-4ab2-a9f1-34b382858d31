<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupBillMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mascj.lup.event.bill.entity.LupBill">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="project_id" property="projectId" />
        <result column="data_id" property="dataId" />
        <result column="grid_unit_id" property="gridUnitId" />
        <result column="grid_code" property="gridCode" />
        <result column="grid_name" property="gridName" />
        <result column="area_code" property="areaCode" />
        <result column="area_name" property="areaName" />
        <result column="state" property="state" />
        <result column="flow_entity_id" property="flowEntityId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, create_by, update_time, update_by, deleted, tenant_id, project_id, data_id, grid_unit_id, grid_code, grid_name, area_code, area_name, state, flow_entity_id
    </sql>

    <select id="countBill" resultType="com.mascj.lup.event.bill.vo.BillCountVO">

        SELECT count(DISTINCT bill.id) as bill_count,flow.flow_state as flow_state
        from lup_bill bill join lup_flow_entity flow on flow.id = bill.flow_entity_id
        join lup_data_label dataLabel on dataLabel.data_id = bill.data_id
        join lup_label label on label.id = dataLabel.label_id
        JOIN lup_data ldata on ldata.id = bill.data_id

        <include refid="billSearchCondition"></include>
        GROUP BY flow.flow_state
        ORDER BY flow.flow_state
    </select>

    <select id="countBillForPending" resultType="com.mascj.lup.event.bill.vo.BillCountVO">

        SELECT count(DISTINCT bill.id) as bill_count,bill.pending_state
        from lup_bill bill join lup_flow_entity flow on flow.id = bill.flow_entity_id
        join lup_data_label dataLabel on dataLabel.data_id = bill.data_id
        join lup_label label on label.id = dataLabel.label_id
        JOIN lup_data ldata on ldata.id = bill.data_id

        <include refid="billSearchCondition"></include>
        GROUP BY bill.pending_state
        ORDER BY bill.pending_state
    </select>



    <sql id="billSearchCondition">

        <where>
            bill.deleted = 0 and bill.usable = 1 and flow.flow_state > 0



            <if test="billDTO.projectId != null and billDTO.projectId != ''">
                and  bill.project_id = #{billDTO.projectId}
            </if>

            <if test="billDTO.pendingState != null ">
                and bill.pending_state = #{billDTO.pendingState}
            </if>

            <if test="billDTO.pendingState == null ">
                and bill.pending_state != 2
            </if>

            <if test="billDTO.locateArea != null and billDTO.locateArea >= 0 ">
                 and ldata.locate_area >= #{billDTO.locateArea}
            </if>

            <if test="billDTO.locateAreaMin != null and billDTO.locateAreaMin >= 0 ">
                 and ldata.locate_area >= #{billDTO.locateAreaMin}
            </if>

            <if test="billDTO.locateAreaMax != null and billDTO.locateAreaMax >= 0 ">
                 and ldata.locate_area <![CDATA[ <= ]]> #{billDTO.locateAreaMax}
            </if>

            <if test="billDTO.eventBillNumber != null and billDTO.eventBillNumber != '' ">
                AND bill.bill_number = #{billDTO.eventBillNumber}
            </if>

            <if test=" billDTO.dateStart != '' and billDTO.dateStart != null and billDTO.dateEnd != '' and billDTO.dateEnd != null ">
                and bill.create_time BETWEEN #{billDTO.dateStart} and #{billDTO.dateEnd}
            </if>

            <if test=" billDTO.eventOriginList != null and billDTO.eventOriginList.size > 0 ">
                and bill.data_origin in
                <foreach collection="billDTO.eventOriginList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="billDTO.eventLabel != null and billDTO.eventLabel != ''">
                and label.name  like concat('%',#{billDTO.eventLabel},'%')
            </if>

            <if test="billDTO.labelIdListPerm != null and billDTO.labelIdListPerm.size > 0">
                and label.id in
                <foreach collection="billDTO.labelIdListPerm" separator="," open="(" item="item" index="index" close=")">
                    #{item}
                </foreach>
            </if>

            <if test=" billDTO.eventLabelList != null and billDTO.eventLabelList.size > 0 ">
                and label.id in
                <foreach collection="billDTO.eventLabelList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="billDTO.unitId != null and billDTO.unitId != ''">
                and bill.grid_unit_id = #{billDTO.unitId}
            </if>

            <if test="billDTO.gridUnitIdList != null and billDTO.gridUnitIdList.size > 0">
                and bill.grid_unit_id in
                <foreach collection="billDTO.gridUnitIdList" close=")" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>

        </where>

    </sql>

    <select id="listBill" resultType="com.mascj.lup.event.bill.vo.BillDataVO">

          SELECT DISTINCT bill.bill_number,bill.id,bill.create_time,ldata.location_lng,ldata.location_lat
                          ,label.`name` as label_name,ldata.data_origin,
                        ldata.locate_area
        from lup_bill bill
        join lup_flow_entity flow on flow.id = bill.flow_entity_id
        join lup_data_label dataLabel on dataLabel.data_id = bill.data_id
        join lup_label label on label.id = dataLabel.label_id

        JOIN lup_data ldata on ldata.id = bill.data_id
        <where>
            bill.deleted = 0 and bill.usable = 1 and flow.flow_state>0


            <if test="projectId != null and projectId != ''">
                and  bill.project_id = #{projectId}
            </if>

            and
             bill.grid_unit_id in
                (<trim suffixOverrides=",">
                <include refid="unitIdArr"></include>
            </trim>
                )


            <if test="billDTO.locateArea != null and billDTO.locateArea >= 0 ">
                 and ldata.locate_area >= #{billDTO.locateArea}
            </if>

            <if test="billDTO.locateAreaMin != null and billDTO.locateAreaMin >= 0 ">
                 and ldata.locate_area >= #{billDTO.locateAreaMin}
            </if>

            <if test="billDTO.locateAreaMax != null and billDTO.locateAreaMax >= 0 ">
                 and ldata.locate_area <![CDATA[ <= ]]> #{billDTO.locateAreaMax}
            </if>

            <if test="billDTO.eventBillNumber != null and billDTO.eventBillNumber != '' ">
                AND bill.bill_number = #{billDTO.eventBillNumber}
            </if>

            <if test=" billDTO.dateStart != '' and billDTO.dateStart != null and billDTO.dateEnd != '' and billDTO.dateEnd != null ">
                and bill.create_time BETWEEN #{billDTO.dateStart} and #{billDTO.dateEnd}
            </if>

            <if test=" billDTO.eventOriginList != null and billDTO.eventOriginList.size > 0 ">
                and bill.data_origin in
                <foreach collection="billDTO.eventOriginList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="billDTO.eventLabel != null and billDTO.eventLabel != ''">
                and label.name  like concat('%',#{billDTO.eventLabel},'%')
            </if>

            <if test="billDTO.labelIdListPerm != null and billDTO.labelIdListPerm.size > 0">
                and label.id in
                <foreach collection="billDTO.labelIdListPerm" separator="," open="(" item="item" index="index" close=")">
                    #{item}
                </foreach>
            </if>

            <if test=" billDTO.eventLabelList != null and billDTO.eventLabelList.size > 0 ">
                and label.id in
                <foreach collection="billDTO.eventLabelList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="billDTO.gridUnitIdList != null and billDTO.gridUnitIdList.size > 0">
                and bill.grid_unit_id in
                <foreach collection="billDTO.gridUnitIdList" close=")" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>

        </where>

        order by bill.create_time desc,bill.bill_number desc

    </select>


    <select id="listBillOnlyLocation" resultType="com.mascj.lup.event.bill.vo.BillDataVO">

        SELECT



        DISTINCT bill.bill_number,bill.id,bill.create_time,ldata.location_lng,ldata.location_lat
        ,label.`name` as label_name,ldata.data_origin,
        ldata.locate_area

        from lup_bill bill
        join lup_flow_entity flow on flow.id = bill.flow_entity_id
        join lup_data_label dataLabel on dataLabel.data_id = bill.data_id
        join lup_label label on label.id = dataLabel.label_id

        JOIN lup_data ldata on ldata.id = bill.data_id
        <where>
            bill.deleted = 0 and bill.usable = 1 and flow.flow_state>0

            <if test="projectId != null and projectId != ''">
                and  bill.project_id = #{projectId}
            </if>


            <if test="billDTO.unitId != null and billDTO.unitId > 0">

                and
                bill.grid_unit_id in
                (<trim suffixOverrides=",">
            <include refid="unitIdArr"></include>
        </trim>
                )

            </if>

            <if test=" billDTO.dateStart != '' and billDTO.dateStart != null and billDTO.dateEnd != '' and billDTO.dateEnd != null ">
                and bill.create_time BETWEEN #{billDTO.dateStart} and #{billDTO.dateEnd}
            </if>

            <if test=" billDTO.eventOriginList != null and billDTO.eventOriginList.size > 0 ">
                and bill.data_origin in
                <foreach collection="billDTO.eventOriginList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="billDTO.eventLabel != null and billDTO.eventLabel != ''">
                and label.name  like concat('%',#{billDTO.eventLabel},'%')
            </if>

            <if test="billDTO.labelIdListPerm != null and billDTO.labelIdListPerm.size > 0">
                and label.id in
                <foreach collection="billDTO.labelIdListPerm" separator="," open="(" item="item" index="index" close=")">
                    #{item}
                </foreach>
            </if>


            <if test="billDTO.eventBillNumber != null and billDTO.eventBillNumber != '' ">
                AND bill.bill_number = #{billDTO.eventBillNumber}
            </if>
            <if test=" billDTO.eventLabelList != null and billDTO.eventLabelList.size > 0 ">
                and label.id in
                <foreach collection="billDTO.eventLabelList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="billDTO.gridUnitIdList != null and billDTO.gridUnitIdList.size > 0">
                and bill.grid_unit_id in
                <foreach collection="billDTO.gridUnitIdList" close=")" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>

        </where>



    </select>


    <sql id="unitIdArr">
        SELECT c.id
        FROM
        (
        SELECT a.id,
        IF (
        FIND_IN_SET(a.pid ,@pids) > 0,
        IF (
        length(@pids) - length(
        REPLACE (@pids, a.pid, '')
        ) > 1,
        IF (
        length(@pids) - length(REPLACE(@pids, a.id, '')) > 1 ,@pids ,@pids := concat(@pids, ',', a.id)
        ) ,@pids := concat(@pids, ',', a.id)
        ),
        0
        ) AS 'plist',
        IF (
        FIND_IN_SET(a.pid ,@pids) > 0,
        @pids,
        0
        ) AS ischild
        FROM
        (
        SELECT
        r.id,r.pid
        FROM
        lup_grid_unit r where r.is_deleted ='0'
        ) a,
        (SELECT @pids := #{billDTO.unitId} ) b

        ) c
        WHERE
        c.ischild != 0

        UNION SELECT #{billDTO.unitId} as id
    </sql>


    <select id="countBillByGrid" resultType="com.mascj.lup.event.bill.vo.LupBillCountVo">

        SELECT SUBSTR(bill.grid_code,1,LENGTH(#{billDTO.gridCode})) as code,COUNT(DISTINCT bill.id) as bill_count FROM lup_bill bill

        join lup_flow_entity flow on flow.id = bill.flow_entity_id
        join lup_data_label dataLabel on dataLabel.data_id = bill.data_id
        join lup_label label on label.id = dataLabel.label_id

        JOIN lup_data ldata on ldata.id = bill.data_id


        <where>

<!--             (bill.grid_code ='WZQY'-->
<!--            and bill.deleted = 0 and flow.flow_state>0-->

<!--            <if test="billDTO.labelIdListPerm != null and billDTO.labelIdListPerm.size > 0">-->
<!--                and label.id in-->
<!--                <foreach collection="billDTO.labelIdListPerm" separator="," open="(" item="item" index="index" close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--                                          ) or-->
                               (
            bill.deleted = 0 and bill.usable = 1 and flow.flow_state>0

            <if test="billDTO.projectId != null and billDTO.projectId != ''">
                and  bill.project_id = #{billDTO.projectId}
            </if>

            <if test="billDTO.locateArea != null and billDTO.locateArea >= 0 ">
                 and ldata.locate_area >= #{billDTO.locateArea}
            </if>

            <if test="billDTO.locateAreaMin != null and billDTO.locateAreaMin >= 0 ">
                 and ldata.locate_area >= #{billDTO.locateAreaMin}
            </if>

            <if test="billDTO.locateAreaMax != null and billDTO.locateAreaMax >= 0 ">
                 and ldata.locate_area <![CDATA[ <= ]]> #{billDTO.locateAreaMax}
            </if>

            <if test="billDTO.eventBillNumber != null and billDTO.eventBillNumber != '' ">
                AND bill.bill_number = #{billDTO.eventBillNumber}
            </if>


            <if test=" billDTO.parentOrgCode == null ">
                and bill.grid_code like concat(#{billDTO.gridCode},'%')
            </if>

            <if test=" billDTO.parentOrgCode != null ">
                and SUBSTR(grid_code,1,LENGTH(#{billDTO.parentOrgCode}))=#{billDTO.parentOrgCode}
            </if>


            <if test=" billDTO.dateStart != '' and billDTO.dateStart != null and billDTO.dateEnd != '' and billDTO.dateEnd != null ">
                and bill.create_time BETWEEN #{billDTO.dateStart} and #{billDTO.dateEnd}
            </if>

            <if test=" billDTO.eventOriginList != null and billDTO.eventOriginList.size > 0 ">
                and bill.data_origin in
                <foreach collection="billDTO.eventOriginList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="billDTO.eventLabel != null and billDTO.eventLabel != ''">
                and label.name  like concat('%',#{billDTO.eventLabel},'%')
            </if>

            <if test="billDTO.labelIdListPerm != null and billDTO.labelIdListPerm.size > 0">
                and label.id in
                <foreach collection="billDTO.labelIdListPerm" separator="," open="(" item="item" index="index" close=")">
                    #{item}
                </foreach>
            </if>

            <if test=" billDTO.eventLabelList != null and billDTO.eventLabelList.size > 0 ">
                and label.id in
                <foreach collection="billDTO.eventLabelList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="billDTO.gridUnitIdList != null and billDTO.gridUnitIdList.size > 0">
                and bill.grid_unit_id in
                <foreach collection="billDTO.gridUnitIdList" close=")" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>

            )
        </where>


        GROUP BY code
    </select>


    <select id="countBillByFlowState" resultType="com.mascj.lup.event.bill.vo.LupBillCountByFlowStateVo">

        SELECT flow.flow_state,count(DISTINCT bill.id) bill_count
        FROM lup_bill bill
            JOIN lup_flow_entity flow on bill.flow_entity_id = flow.id
            join lup_data_label dataLabel on dataLabel.data_id = bill.data_id
            join lup_label label on label.id = dataLabel.label_id

        <where>
            bill.deleted = 0 and bill.usable = 1 and flow.flow_state>0


            <if test="gridIdList != null and  gridIdList.size > 0">
                and bill.grid_unit_id in
                <foreach collection="gridIdList" close=")" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="flowStateList != null and  flowStateList.size > 0">
                and flow.flow_state in
                <foreach collection="flowStateList" close=")" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>

             <if test="labelIdListPerm != null and labelIdListPerm.size > 0">
                 and label.id in
                 <foreach collection="labelIdListPerm" separator="," open="(" item="item" index="index" close=")">
                     #{item}
                 </foreach>

             </if>

        </where>

        GROUP BY flow.flow_state

    </select>

    <select id="pagedBill" resultType="com.mascj.lup.event.bill.vo.BillSearchVO">

        SELECT DISTINCT
            bill.id,
            bill.bill_number,
            bill.area_name,
            bill.grid_name,
            bill.data_origin,
        bill.flow_entity_id,
        flow.process_task_id,
        bill.pending_state,
            label.`name` event_label,

            ldata.push_state,
            ldata.push_msg,
            ldata.extra_data,
            bill.create_time,
            flow.handle_type,
            flow.flow_state,
        flow.four_success,
            ldata.location_lng,
            ldata.location_lat,
            ldata.locate_area,

            IFNULL(JSON_EXTRACT(ldata.extra_data, '$.ocr'),'false') `ocr`,
            ldata.device_name,
            flow_entity_time_out.dead_line

            <if test="billDTO.lngLatGeometry != null and billDTO.lngLatGeometry != ''">
                ,ST_DISTANCE_SPHERE ( ST_GEOMFROMTEXT ( #{billDTO.lngLatGeometry} ),
                ST_GEOMFROMTEXT(
                CONCAT('POINT(',ldata.location_lng,' ',ldata.location_lat,')')
                )
                ) AS distance
            </if>
        FROM
            lup_bill bill
            LEFT JOIN lup_flow_entity flow ON flow.id = bill.flow_entity_id
            LEFT JOIN lup_flow_entity_time_out  flow_entity_time_out
            ON flow_entity_time_out.flow_entity_id = flow.id
            and flow_entity_time_out.process_task_id = flow.process_task_id
            JOIN lup_data_label dataLabel ON dataLabel.data_id = bill.data_id
            JOIN lup_label label ON label.id = dataLabel.label_id

            JOIN lup_data ldata on ldata.id = bill.data_id

        <include refid="billPagedSearchCondition"></include>

        order by bill.create_time desc,bill.bill_number desc

    </select>


    <sql id="columnListAllBillList">
        DISTINCT
        bill.id,
        bill.data_id,
        bill.bill_number,
        bill.area_name,
        bill.grid_name,
        bill.data_origin,
        label.`name` event_label,
        bill.pending_state,
        bill.create_time,
        flow.handle_type,
        ldata.location_lng,
        ldata.location_lat,
        ldata.locate_area,
        flow.flow_state,
        flow.process_instance_id

        <if test="billDTO.lngLatGeometry != null and billDTO.lngLatGeometry != ''">
            ,ST_Distance ( ST_GEOMFROMTEXT ( #{billDTO.lngLatGeometry} ),
            ST_GEOMFROMTEXT(
            CONCAT('POINT(',ldata.location_lng,' ',ldata.location_lat,')')
            )
            ) / 0.0111 * 1000 AS distance
        </if>
    </sql>

    <select id="listAllBill" resultType="com.mascj.lup.event.bill.vo.BillSearchExcelVO">

        SELECT
            <include refid="columnListAllBillList"></include>
        FROM
        lup_bill bill
        LEFT JOIN lup_flow_entity flow ON flow.id = bill.flow_entity_id
        JOIN lup_data_label dataLabel ON dataLabel.data_id = bill.data_id
        JOIN lup_label label ON label.id = dataLabel.label_id

        JOIN lup_data ldata on ldata.id = bill.data_id

        <include refid="billPagedSearchCondition"></include>

        order by bill.create_time desc,bill.bill_number desc

    </select>



    <select id="listYXQAllBill" resultType="com.mascj.lup.event.bill.vo.BillSearchExcelYXQVO">

        SELECT
            <include refid="columnListAllBillList"></include>
        FROM
        lup_bill bill
        LEFT JOIN lup_flow_entity flow ON flow.id = bill.flow_entity_id
        JOIN lup_data_label dataLabel ON dataLabel.data_id = bill.data_id
        JOIN lup_label label ON label.id = dataLabel.label_id

        JOIN lup_data ldata on ldata.id = bill.data_id

        <include refid="billPagedSearchCondition"></include>

        order by bill.create_time desc,bill.bill_number desc

    </select>



    <select id="countFlowStateBill" resultType="com.mascj.lup.event.bill.vo.LupBillCountVo">

        SELECT

        count( DISTINCT  bill.id) as billCount

        FROM
        lup_bill bill
        LEFT JOIN lup_flow_entity flow ON flow.id = bill.flow_entity_id
        JOIN lup_data_label dataLabel ON dataLabel.data_id = bill.data_id
        JOIN lup_label label ON label.id = dataLabel.label_id

        JOIN lup_data ldata on ldata.id = bill.data_id

        <include refid="billPagedSearchCondition"></include>

    </select>


    <sql id="billPagedSearchCondition">

        <where>
            bill.deleted = 0 and bill.usable = 1 and flow.flow_state>0

            <if test="projectId != null and projectId != ''">
                and  bill.project_id = #{projectId}
            </if>

            <if test="billDTO.pendingState != null ">
                and bill.pending_state = #{billDTO.pendingState}
            </if>

            <if test="billDTO.unitId != null and billDTO.unitId > 0 ">
            and
            bill.grid_unit_id in
            (<trim suffixOverrides=",">
            <include refid="unitIdArr"></include>
        </trim>
            )
            </if>





            <if test="billDTO.locateArea != null and billDTO.locateArea >= 0 ">
                 and ldata.locate_area >= #{billDTO.locateArea}
            </if>

            <if test="billDTO.locateAreaMin != null and billDTO.locateAreaMin >= 0 ">
                 and ldata.locate_area >= #{billDTO.locateAreaMin}
            </if>

            <if test="billDTO.locateAreaMax != null and billDTO.locateAreaMax >= 0 ">
                 and ldata.locate_area <![CDATA[ <= ]]> #{billDTO.locateAreaMax}
            </if>

            <if test="billDTO.eventBillNumber != null and billDTO.eventBillNumber != '' ">
                AND bill.bill_number = #{billDTO.eventBillNumber}
            </if>


            <if test=" billDTO.dateStart != '' and billDTO.dateStart != null and billDTO.dateEnd != '' and billDTO.dateEnd != null ">
                and bill.create_time BETWEEN #{billDTO.dateStart} and #{billDTO.dateEnd}
            </if>

            <if test="billDTO.billNumber != null and billDTO.billNumber != ''">

                AND bill.bill_number like concat('%',#{billDTO.billNumber},'%')

            </if>

            <if test=" billDTO.gridIdList != null and billDTO.gridIdList.size > 0 ">
                and bill.grid_unit_id in
                <foreach collection="billDTO.gridIdList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test=" billDTO.deviceIdList != null and billDTO.deviceIdList.size > 0 ">
                and ldata.device_id in
                <foreach collection="billDTO.deviceIdList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test=" billDTO.handleTypeList != null and billDTO.handleTypeList.size > 0 ">
                and flow.handle_type in
                <foreach collection="billDTO.handleTypeList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test=" billDTO.fourSuccessList != null and billDTO.fourSuccessList.size > 0 ">
                and flow.four_success in
                <foreach collection="billDTO.fourSuccessList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>


            <if test="billDTO.eventOrigin != null and billDTO.eventOrigin != ''">

                AND bill.data_origin = #{billDTO.eventOrigin}

            </if>
            <if test=" billDTO.eventOriginList != null and billDTO.eventOriginList.size > 0 ">
                and bill.data_origin in
                <foreach collection="billDTO.eventOriginList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="billDTO.eventLabel != null and billDTO.eventLabel != ''">
                and label.id  like concat('%',#{billDTO.eventLabel},'%')
            </if>

            <if test=" billDTO.eventLabelList != null and billDTO.eventLabelList.size > 0 ">
                and label.id in
                <foreach collection="billDTO.eventLabelList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="billDTO.labelIdListPerm != null and billDTO.labelIdListPerm.size > 0">
                and label.id in
                <foreach collection="billDTO.labelIdListPerm" separator="," open="(" item="item" index="index" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="billDTO.flowStateList != null and billDTO.flowStateList.size > 0 ">
                and flow.flow_state in
                <foreach collection="billDTO.flowStateList" index="index" close=")"  open="(" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="billDTO.flowState != null  ">
                and flow.flow_state  = #{billDTO.flowState }
            </if>

            <if test="billDTO.gridUnitIdList != null and billDTO.gridUnitIdList.size > 0">
                and bill.grid_unit_id in
                <foreach collection="billDTO.gridUnitIdList" close=")" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>


        </where>

    </sql>


    <select id="detailByDataId" resultType="com.mascj.lup.event.bill.vo.BillDetailVO">

        SELECT
            bill.id,
            bill.bill_number,
            bill.area_name,
            bill.grid_name,
            bill.create_time,
            bill.flow_entity_id,
            bill.data_id,
            flow.flow_state,
            label.`name` event_label,
            ldata.data_origin,
            ldata.locate_area,
            ldata.compare_batch_id,
            ldata.current_batch_id,
            ldata.shape,
            ldata.compare_type,
            ldata.data_type,
            ldata.location_lng,
            ldata.location_lat,
            ldata.extra_data,
            flow_entity_time_out.dead_line
        FROM
            lup_bill bill
                JOIN lup_data ldata ON ldata.id = bill.data_id
                JOIN lup_data_label data_label ON data_label.data_id = bill.data_id
                JOIN lup_label label ON label.id = data_label.label_id
                JOIN lup_flow_entity flow ON flow.id = bill.flow_entity_id
                LEFT JOIN lup_flow_entity_time_out  flow_entity_time_out
                          ON flow_entity_time_out.flow_entity_id = flow.id
                              and flow_entity_time_out.process_task_id = flow.process_task_id
        WHERE
            ldata.id = #{dataId} and flow.flow_state>0

        order by  bill.create_time desc limit 1
    </select>

    <select id="listBillDetailVOByQueryBillPictureInfo" resultType="com.mascj.lup.event.bill.vo.BillDetailVO">

        SELECT DISTINCT
        bill.data_id,bill.create_time,bill.bill_number
        FROM
        lup_bill bill
        JOIN lup_data ldata ON bill.data_id = ldata.id

        JOIN lup_flow_entity flow on flow.id = bill.flow_entity_id

        <where>
            bill.deleted=0 and ldata.deleted=0 and bill.usable=1

            <if test="data.flowStateList!=null and data.flowStateList.size>0">
                and flow.flow_state in

                <foreach collection="data.flowStateList" separator="," open="(" item="item" index="index" close=")">
                    #{item}
                </foreach>

            </if>

            <if test="data.dateType != null and data.dateType != '' ">
                <if test="data.dateType == 'month' ">
                    AND CONCAT(
                    YEAR ( bill.create_time ),
                    '-',
                    MONTH ( bill.create_time ))
                </if>
                <if test="data.dateType == 'date' ">
                    and date(bill.create_time)
                </if>

                BETWEEN #{data.monthStart}
                AND #{data.monthEnd}

            </if>

        </where>

        order by bill.create_time desc,bill.bill_number desc
    </select>
    <select id="detailById" resultType="com.mascj.lup.event.bill.vo.BillDetailVO">

        SELECT
         bill.id,
         bill.bill_number,
         bill.area_name,
         bill.grid_name,
         bill.create_time,
         bill.flow_entity_id,
         bill.data_id,
         flow.flow_state,
         bill.pending_state,
         label.`name` event_label,
         ldata.data_origin,
         ldata.locate_area,
         ldata.compare_batch_id,
         ldata.current_batch_id,
        ldata.shape,
         ldata.happened_time,
         ldata.compare_type,
         ldata.data_type,
         ldata.location_lng,
         ldata.location_lat,
         ldata.extra_data,
         flow_entity_time_out.dead_line
        FROM
         lup_bill bill
         JOIN lup_data ldata ON ldata.id = bill.data_id
         JOIN lup_data_label data_label ON data_label.data_id = bill.data_id
         JOIN lup_label label ON label.id = data_label.label_id
         JOIN lup_flow_entity flow ON flow.id = bill.flow_entity_id
         LEFT JOIN lup_flow_entity_time_out  flow_entity_time_out
                   ON flow_entity_time_out.flow_entity_id = flow.id
                       and flow_entity_time_out.process_task_id = flow.process_task_id
        WHERE
         bill.id = #{billId} and flow.flow_state>0


    </select>

    <!-- 24小时 每天的两小时 统计一个数量 -->
    <select id="countByHour">
        SELECT
            bill_hour,count(1) as bil_count
        FROM v_bill_data
        WHERE v_bill_data.create_time BETWEEN '2023-11-12' and '2023-11-13'
        GROUP BY bill_hour
        ORDER BY bill_hour
    </select>
    <select id="detailNoTenantIdById" resultType="com.mascj.lup.event.bill.entity.LupBill">
        SELECT
            bill.*
        FROM
            lup_bill bill
        WHERE
            bill.id = #{billId}

    </select>

    <select id="pagedThirdParty" resultType="com.mascj.lup.event.bill.vo.QueryBillDataEntityVO">
        SELECT DISTINCT
        ldata.address,ldata.event_desc,
        ldata.extra_data,
        label.`name` AS label_name,
        bill.id AS bill_id,
        bill.bill_number,
        ldata.location_lng longitude,
        ldata.location_lat latitude,
        bill.create_time
        FROM
        lup_bill bill
        JOIN lup_flow_entity flow on flow.id = bill.flow_entity_id
        JOIN lup_data ldata ON ldata.id = bill.data_id
        JOIN lup_data_label data_label ON data_label.data_id = bill.data_id
        JOIN lup_label label ON label.id = data_label.label_id
        WHERE

        bill.deleted=0 and flow.flow_state>0 and bill.usable=1 and ldata.deleted=0 and

        <if test="data.dateStart != null and data.dateStart != '' and data.dateStart != null and data.dateStart != '' ">
                 bill.create_time  BETWEEN #{data.dateStart}
                AND #{data.dateEnd}
            </if>

            <if test="data.billNumber != null and data.billNumber != '' ">
                AND bill.bill_number LIKE concat('%',#{data.billNumber},'%')
            </if>

            <if test="data.locateAreaMin != null ">
                AND ldata.locate_area >= #{data.locateAreaMin}
            </if>
            <if test="data.locateAreaMax != null ">
                AND ldata.locate_area <![CDATA[<=]]> #{data.locateAreaMax}
            </if>

            <if test="data.eventOriginList != null and data.eventOriginList.size > 0 ">
                AND ldata.data_origin IN
                    <foreach collection="data.eventOriginList" separator="," open="(" item="item" index="index" close=")" >
                        #{item}
                    </foreach>
            </if>
            <if test="data.eventLabelNameList != null and data.eventLabelNameList.size > 0 ">
                AND label.`name` IN
                <foreach collection="data.eventLabelNameList" separator="," open="(" item="item" index="index" close=")" >
                    #{item}
                </foreach>
            </if>

        order by  bill.create_time DESC
    </select>


    <select id="listBillUserLabelVO" resultType="com.mascj.lup.event.bill.vo.BillUserLabelVO">

        SELECT DISTINCT
            grid_user.user_id,
            data_label.label_id
        FROM
            lup_bill bill
                JOIN lup_flow_entity flow ON flow.id = bill.flow_entity_id
                JOIN lup_grid_user grid_user ON grid_user.grid_unit_id = bill.grid_unit_id
                JOIN lup_data_label data_label ON data_label.data_id = bill.data_id
        WHERE
            flow.flow_state > 0
          and data_label.deleted=0
          AND bill.deleted = 0 and data_label.deleted=0
          AND flow.id = #{flowId}
    </select>

    <select id="listBillUserLabelOneVO" resultType="com.mascj.lup.event.bill.vo.BillUserLabelVO">

        SELECT DISTINCT
            bill.id bill_id,flow.process_task_id
                ,
            bill.bill_number event_no,bill.data_origin,
            bill.grid_name,label.`name` event_label
        FROM
            lup_bill bill
                JOIN lup_flow_entity flow ON flow.id = bill.flow_entity_id
                JOIN lup_grid_user grid_user ON grid_user.grid_unit_id = bill.grid_unit_id
                JOIN lup_data_label data_label ON data_label.data_id = bill.data_id
                JOIN lup_label label on label.id = data_label.label_id
        WHERE
            flow.flow_state > 0
          and data_label.deleted=0
          AND bill.deleted = 0 and data_label.deleted=0
          AND flow.id = #{flowId}
    </select>
    <select id="listBillLabelAndDataIdOneVO" resultType="com.mascj.lup.event.bill.vo.BillUserLabelVO" >

        SELECT DISTINCT
            bill.id bill_id,flow.process_task_id,bill.tenant_id
                ,
            bill.bill_number event_no,data_label.data_id ,
            bill.grid_name,label.`name` event_label,data_label.label_id
        FROM
            lup_bill bill
                JOIN lup_flow_entity flow ON flow.id = bill.flow_entity_id
                JOIN lup_data_label data_label ON data_label.data_id = bill.data_id
                JOIN lup_label label on label.id = data_label.label_id
        WHERE
            flow.flow_state > 0
          and data_label.deleted=0
          AND bill.deleted = 0 and data_label.deleted=0
          AND flow.id = #{flowId}

    </select>

</mapper>
