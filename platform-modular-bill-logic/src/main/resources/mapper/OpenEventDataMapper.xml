<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.open.OpenEventDataMapper">
    <select id="pageOpenEventData" resultType="com.mascj.lup.event.bill.open.vo.OpenEventDataVO">
        SELECT
            bill.id AS billId,
            COALESCE(flow.flow_state, 0) AS flowCode,
            COALESCE(label.name, '未分类') AS eventLabel,
            bill.bill_number AS billNumber,
            DATE_FORMAT(bill.create_time, '%Y-%m-%d %H:%i:%s') AS createTime,
            CAST(ldata.location_lng AS CHAR) AS locationLng,
            CAST(ldata.location_lat AS CHAR) AS locationLat,
            COALESCE(ldata.fly_task_number, '') AS taskNumber,
            COALESCE(bill.grid_name, '') AS gridName,
            COALESCE(ldata.air_line_name, '') AS airLineName,
            COALESCE(ldata.device_name, '') AS deviceName,
            COALESCE(ldata.fly_task_number, '') AS flyTaskNumber,
            COALESCE(ldata.fly_task_name, '') AS flyTaskName,
            COALESCE(ldata.device_sn, '') AS deviceSn,
            DATE_FORMAT(COALESCE(ldata.happened_time, bill.create_time), '%Y-%m-%d %H:%i:%s') AS happenedTime,
            COALESCE(
                JSON_UNQUOTE(JSON_EXTRACT(ldata.extra_data, '$.eventPictureUrl')),
                JSON_UNQUOTE(JSON_EXTRACT(ldata.extra_data, '$.eventPicture')),
                ''
            ) AS eventPicture,
            COALESCE(
                ldata.event_desc,
                JSON_UNQUOTE(JSON_EXTRACT(ldata.extra_data, '$.description')),
                CONCAT('AI识别事件：', label.name),
                ''
            ) AS eventDesc
        FROM lup_bill bill
        JOIN lup_data ldata ON ldata.id = bill.data_id
        LEFT JOIN lup_flow_entity flow ON flow.id = bill.flow_entity_id
        LEFT JOIN lup_data_label data_label ON data_label.data_id = bill.data_id AND data_label.deleted = 0
        LEFT JOIN lup_label label ON label.id = data_label.label_id AND label.deleted = 0
        WHERE bill.deleted = 0
          AND ldata.deleted = 0
          AND bill.usable = 1
        <!-- 事件编号查询 -->
        <if test="data.eventNumber != null and data.eventNumber != ''">
            AND bill.bill_number LIKE CONCAT('%', #{data.eventNumber}, '%')
        </if>

        <!-- 数据来源类型查询 -->
        <if test="data.dataOrigin != null">
            AND ldata.data_origin = #{data.dataOrigin}
        </if>

        <!-- 事件时间范围查询 -->
        <if test="data.duringStartTime != null and data.duringStartTime != ''">
            AND bill.create_time >= #{data.duringStartTime}
        </if>
        <if test="data.duringEndTime != null and data.duringEndTime != ''">
            AND bill.create_time &lt;= #{data.duringEndTime}
        </if>

        <!-- 标签名称查询 -->
        <if test="data.labelName != null and data.labelName != ''">
            AND label.name LIKE CONCAT('%', #{data.labelName}, '%')
        </if>

        <!-- 标签ID查询 -->
        <if test="data.labelId != null">
            AND label.id = #{data.labelId}
        </if>

        <!-- 标签ID权限列表查询 -->
        <if test="data.labelIdListPerm != null and data.labelIdListPerm.size() > 0">
            AND label.id IN
            <foreach collection="data.labelIdListPerm" separator="," open="(" item="item" index="index" close=")">
                #{item}
            </foreach>
        </if>

        ORDER BY bill.create_time DESC
    </select>

    <select id="detailEventData" resultType="com.mascj.lup.event.bill.open.vo.OpenEventDataDetailVO">
        SELECT
            bill.id AS id,
            CASE
                WHEN flow.flow_state = 0 THEN '待处理'
                WHEN flow.flow_state = 1 THEN '处理中'
                WHEN flow.flow_state = 2 THEN '已完成'
                ELSE '待处理'
            END AS name,
            COALESCE(flow.flow_state, 0) AS flowCode,
            ldata.data_origin AS dataOrigin,
            COALESCE(label.name, '未分类') AS eventLabel,
            bill.bill_number AS billNumber,
            DATE_FORMAT(bill.create_time, '%Y-%m-%d %H:%i:%s') AS createTime,
            CAST(ldata.location_lng AS CHAR) AS locationLng,
            CAST(ldata.location_lat AS CHAR) AS locationLat,
            COALESCE(ldata.fly_task_number, '') AS taskNumber,
            COALESCE(bill.grid_name, '') AS gridName,
            COALESCE(bill.area_name, '') AS areaName,
            COALESCE(ldata.air_line_name, '') AS airLineName,
            COALESCE(ldata.device_name, '') AS deviceName,
            ldata.device_id AS deviceId,
            ldata.fly_task_id AS flyTaskId,
            COALESCE(ldata.fly_task_number, '') AS flyTaskNumber,
            COALESCE(ldata.fly_task_name, '') AS flyTaskName,
            COALESCE(ldata.device_sn, '') AS deviceSn,
            DATE_FORMAT(COALESCE(ldata.happened_time, bill.create_time), '%Y-%m-%d %H:%i:%s') AS happenedTime,
            COALESCE(
                JSON_UNQUOTE(JSON_EXTRACT(ldata.extra_data, '$.eventPictureUrl')),
                JSON_UNQUOTE(JSON_EXTRACT(ldata.extra_data, '$.eventPicture')),
                ''
            ) AS eventPicture,
            COALESCE(
                ldata.event_desc,
                JSON_UNQUOTE(JSON_EXTRACT(ldata.extra_data, '$.description')),
                CONCAT('AI识别事件：', label.name),
                ''
            ) AS eventDesc
        FROM lup_bill bill
        JOIN lup_data ldata ON ldata.id = bill.data_id
        LEFT JOIN lup_flow_entity flow ON flow.id = bill.flow_entity_id
        LEFT JOIN lup_data_label data_label ON data_label.data_id = bill.data_id AND data_label.deleted = 0
        LEFT JOIN lup_label label ON label.id = data_label.label_id AND label.deleted = 0
        WHERE bill.deleted = 0
          AND ldata.deleted = 0
          AND bill.usable = 1
          AND bill.id = #{billId}
        LIMIT 1
    </select>
</mapper>
