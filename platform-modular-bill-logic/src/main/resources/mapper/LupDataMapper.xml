<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mascj.lup.event.bill.entity.LupData">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="project_id" property="projectId" />
        <result column="data_origin" property="dataOrigin" />
        <result column="original_data_logo" property="originalDataLogo" />
        <result column="data_type" property="dataType" />
        <result column="compare_type" property="compareType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, create_by, update_time, update_by, deleted, tenant_id, project_id, data_origin, original_data_logo, data_type, compare_type
    </sql>

    <resultMap id="getDataInfo" type="com.mascj.lup.event.bill.vo.EventDataVO" >
        <result column="id" property="id" />
        <result column="bill_number" property="billNumber" />
        <result column="data_origin" property="dataOrigin" />
        <result column="create_time" property="createTime" />
        <result column="location_lng" property="locationLng" />
        <result column="location_lat" property="locationLat" />
        <result column="grid_name" property="gridName" />
        <result column="area_name" property="areaName" />
        <result column="air_line_name" property="airLineName" />
        <result column="device_name" property="deviceName" />
        <result column="device_id" property="deviceId" />
        <result column="fly_task_id" property="flyTaskId" />
        <result column="fly_task_number" property="flyTaskNumber" />
        <result column="fly_task_name" property="flyTaskName" />
        <result column="flow_entity_id" property="flowEntityId" />
        <result column="name" property="name" />
        <result column="flow_code" property="flowCode" />
        <result column="flow_state" property="flowState" />

        <result column="device_sn" property="deviceSn" />
        <result column="happened_time" property="happenedTime" />
        <result column="extra_data" property="extraData" />

        <collection property="eventLabelList" javaType="ArrayList" ofType="com.mascj.lup.event.bill.entity.LupLabel">
            <result column="label_name" property="name" />
        </collection>
    </resultMap>
    <resultMap id="getDataBaseInfo" type="com.mascj.lup.event.bill.vo.EventDataBaseInfoVO" >
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="bill_number" property="billNumber" />
        <result column="data_origin" property="dataOrigin" />
        <result column="create_time" property="createTime" />
        <result column="location_lng" property="locationLng" />
        <result column="location_lat" property="locationLat" />
        <result column="grid_name" property="gridName" />
    </resultMap>

    <select id="listAllEventData" resultMap="getDataBaseInfo" >
        SELECT label.`name` label_name,bill.* FROM v_bill_data bill
        JOIN lup_data_label dlabel on dlabel.data_id = bill.data_id
        JOIN lup_label label on label.id = dlabel.label_id
    </select>

    <resultMap id="getDataInfoByTask" type="com.mascj.lup.event.bill.vo.EventDataTaskVO" >
        <result column="id" property="id" />
        <result column="bill_number" property="billNumber" />
        <result column="data_origin" property="dataOrigin" />
        <result column="create_time" property="createTime" />
        <result column="extra_data" property="extraData" />
        <result column="label_name" property="labelName" />
    </resultMap>

    <select id="pageByTask" resultMap="getDataInfoByTask" >
        SELECT label.`name` label_name,bill.* FROM v_bill_data bill
        JOIN lup_data_label dlabel on dlabel.data_id = bill.data_id
        JOIN lup_label label on label.id = dlabel.label_id

        <where>
            <if test="data.flyTaskId != null and data.flyTaskId > 0">
                and bill.fly_task_id = #{data.flyTaskId}
            </if>

            <if test="data.flyTaskIdList != null and data.flyTaskIdList.size > 0">
                and bill.fly_task_id in
                <foreach collection="data.flyTaskIdList" separator="," open="(" item="item" index="index" close=")">
                #{item}
                </foreach>
            </if>

            <if test="data.labelIdListPerm != null and data.labelIdListPerm.size > 0">
                and label.id in
                <foreach collection="data.labelIdListPerm" separator="," open="(" item="item" index="index" close=")">
                    #{item}
                </foreach>
            </if>

        </where>
    </select>
    <select id="pageEventData" resultMap="getDataInfo" >
        SELECT label.`name` label_name,bill.* FROM v_bill_data bill
                                            JOIN lup_data_label dlabel on dlabel.data_id = bill.data_id
                                            JOIN lup_label label on label.id = dlabel.label_id

        <where>
            <if test=" data.eventNumber !=null and data.eventNumber != '' ">
                and bill.bill_number like concat('%',#{data.eventNumber},'%')
            </if>

            <if test=" data.dataOrigin !=null ">
                and bill.data_origin = #{data.dataOrigin}
            </if>

            <if test=" data.labelName !=null ">
                and label.`name` = like concat('%',#{data.labelName},'%')
            </if>

            <if test=" data.labelId !=null ">
                and label.id = #{data.labelId}
            </if>

            <if test="data.labelIdListPerm != null and data.labelIdListPerm.size > 0">
                and label.id in
                <foreach collection="data.labelIdListPerm" separator="," open="(" item="item" index="index" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="data.duringStartTime != null and data.duringStartTime != '' ">
                and date(bill.create_time) >= date(#{data.duringStartTime})
            </if>
            <if test="data.duringEndTime != null and data.duringEndTime != '' ">
                and date(bill.create_time) &lt;= date(#{data.duringEndTime})
            </if>

        </where>
        order by bill.create_time desc
    </select>

    <select id="pageEventDataForLand" resultMap="getDataInfo" >
        SELECT label.`name` label_name,bill.* FROM v_bill_data bill
        JOIN lup_data_label dlabel on dlabel.data_id = bill.data_id
        JOIN lup_label label on label.id = dlabel.label_id

        <where>

            <if test=" data.eventNumber !=null and data.eventNumber != '' ">
                and bill.bill_number like concat('%',#{data.eventNumber},'%')
            </if>

            <if test=" data.dataOrigin !=null ">
                and bill.data_origin = #{data.dataOrigin}
            </if>

            <if test=" data.labelName !=null ">
                and label.`name` = like concat('%',#{data.labelName},'%')
            </if>

            <if test=" data.labelId !=null ">
                and label.id = #{data.labelId}
            </if>

            <if test="data.labelIdListPerm != null and data.labelIdListPerm.size > 0">
                and label.id in
                <foreach collection="data.labelIdListPerm" separator="," open="(" item="item" index="index" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="data.duringStartTime != null and data.duringStartTime != '' ">
                and date(bill.create_time) >= date(#{data.duringStartTime})
            </if>
            <if test="data.duringEndTime != null and data.duringEndTime != '' ">
                and date(bill.create_time) &lt;= date(#{data.duringEndTime})
            </if>

            <if test="data.geoInfo == null or data.geoInfo == '' or data.flyTaskIdList == null or data.flyTaskIdList.size == 0">
                and 1!=1
            </if>

            <if test="data.geoInfo != null and data.geoInfo != '' and (data.flyTaskIdList == null or data.flyTaskIdList.size == 0 )">
                and  ST_Contains (
                ST_GeomFromGeoJSON ( #{data.geoInfo} ),
                ST_GeomFromTEXT (
                CONCAT( 'POINT(', location_lat, ' ', location_lng, ')' ),
                4326
                )
                )=1
            </if>

            <if test="data.flyTaskIdList != null and data.flyTaskIdList.size > 0 and (data.geoInfo == null or data.geoInfo == '')">
                and bill.fly_task_id in
                <foreach collection="data.flyTaskIdList" close=")" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="data.flyTaskIdList != null and data.flyTaskIdList.size > 0 and data.geoInfo != null and data.geoInfo != ''">
                and (
                bill.fly_task_id in
                <foreach collection="data.flyTaskIdList" close=")" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
                or
                ST_Contains (
                ST_GeomFromGeoJSON ( #{data.geoInfo} ),
                ST_GeomFromTEXT (
                CONCAT( 'POINT(', location_lat, ' ', location_lng, ')' ),
                4326
                )
                )=1

                )
            </if>
        </where>
        order by bill.create_time desc
    </select>
    <select id="queryFlyTaskReportVO" resultType="com.mascj.lup.event.bill.vo.FlyTaskReportBillCountVO">
        SELECT
            count( DISTINCT bill.id ) AS bill_count,
            bill.flow_state
        FROM
            v_bill_data bill
                JOIN lup_data_label dlabel ON dlabel.data_id = bill.data_id
                JOIN lup_label label ON label.id = dlabel.label_id
        WHERE
            bill.tenant_id = #{data.tenantId}
          AND bill.fly_task_id= #{data.flyTaskId}

        AND flow_state IN
          <foreach collection="data.flowStateList" close=")" index="index" item="item" open="(" separator=",">
            #{item}
          </foreach>
        GROUP BY
            bill.flow_state
    </select>

    <select id="detail" resultMap="getDataInfo">
        SELECT label.`name` label_name,bill.* FROM v_bill_data bill
        JOIN lup_data_label dlabel on dlabel.data_id = bill.data_id
        JOIN lup_label label on label.id = dlabel.label_id

        <where>
            bill.id = #{billId}
        </where>
    </select>

    <select id="countByArea" resultType="com.mascj.lup.event.bill.vo.CountEventDataVO">
        SELECT
            count( id ) `count`,
            grid_name `name`
        FROM
            v_bill_data bill
        GROUP BY
            grid_name
        ORDER BY
            `count` DESC
    </select>

    <select id="countByLabel" resultType="com.mascj.lup.event.bill.vo.CountEventDataVO">
        SELECT
            count( bill.id ) `count`,
            label.`name` `name`
        FROM
            v_bill_data bill
                JOIN lup_data_label dlabel ON dlabel.data_id = bill.data_id
                JOIN lup_label label ON label.id = dlabel.label_id
        GROUP BY
            label.`name`
        ORDER BY
            `count` DESC
    </select>

    <select id="countByLabelOnDay" resultType="com.mascj.lup.event.bill.vo.CountEventDataVO">
        SELECT
            count( bill.id ) `count`,
            label.`name` `name`
        FROM
            v_bill_data bill
                JOIN lup_data_label dlabel ON dlabel.data_id = bill.data_id
                JOIN lup_label label ON label.id = dlabel.label_id
        where date(bill.create_time) = date(now())
        GROUP BY
            label.`name`
        ORDER BY
            `count` DESC
    </select>



    <select id="countByOriginByDate" resultType="com.mascj.lup.event.bill.vo.CountEventDataVO">

        SELECT
            count( id ) `count`,
            data_origin `name`,
            bill.bill_date
        FROM
            v_bill_data bill
        <where>

            <if test="data.duringStartTime != null and data.duringStartTime != '' ">
                and date(bill.create_time) >= date(#{data.duringStartTime})
            </if>

            <if test="data.duringEndTime != null and data.duringEndTime != '' ">
                and date(bill.create_time) &lt;= date(#{data.duringEndTime})
            </if>
        </where>
        GROUP BY
            data_origin,bill.bill_date
        ORDER BY
            bill.bill_date ASC
    </select>

    <select id="countByOriginByMonth" resultType="com.mascj.lup.event.bill.vo.CountEventDataVO">

        SELECT
        count( id ) `count`,
        data_origin `name`,
        bill.bill_date
        FROM
        v_bill_data bill
        <where>

            <if test="data.duringStartTime != null and data.duringStartTime != '' ">
                and date(bill.create_time) >= date(#{data.duringStartTime})
            </if>

            <if test="data.duringEndTime != null and data.duringEndTime != '' ">
                and date(bill.create_time) &lt;= date(#{data.duringEndTime})
            </if>
        </where>
        GROUP BY
        data_origin,bill.bill_date
        ORDER BY
        bill.bill_date ASC
    </select>

    <select id="countByOriginByYear" resultType="com.mascj.lup.event.bill.vo.CountEventDataVO">

        SELECT
        count( id ) `count`,
        data_origin `name`,bill.bill_month
        FROM
        v_bill_data bill
        <where>
            bill.bill_year = #{data.billYear}

            <if test="data.duringStartTime != null and data.duringStartTime != '' ">
                and date(bill.create_time) >= date(#{data.duringStartTime})
            </if>

            <if test="data.duringEndTime != null and data.duringEndTime != '' ">
                and date(bill.create_time) &lt;= date(#{data.duringEndTime})
            </if>
        </where>
        GROUP BY
        data_origin,bill.bill_month
        ORDER BY
        bill.bill_month ASC
    </select>




    <select id="countByDealResultByDate" resultType="com.mascj.lup.event.bill.vo.CountEventDataDealResultVO">

        SELECT
        count( id ) `count`,
        bill_deal_result `name`,
        bill.bill_date
        FROM
        v_bill_data bill
        <where>

            <if test="data.duringStartTime != null and data.duringStartTime != '' ">
                and date(bill.create_time) >= date(#{data.duringStartTime})
            </if>

            <if test="data.duringEndTime != null and data.duringEndTime != '' ">
                and date(bill.create_time) &lt;= date(#{data.duringEndTime})
            </if>
        </where>
        GROUP BY
        bill_deal_result,bill.bill_date
        ORDER BY
        bill.bill_date ASC
    </select>

    <select id="countByDealResultByMonth" resultType="com.mascj.lup.event.bill.vo.CountEventDataDealResultVO">

        SELECT
        count( id ) `count`,
        bill_deal_result `name`,
        bill.bill_date
        FROM
        v_bill_data bill
        <where>

            <if test="data.duringStartTime != null and data.duringStartTime != '' ">
                and date(bill.create_time) >= date(#{data.duringStartTime})
            </if>

            <if test="data.duringEndTime != null and data.duringEndTime != '' ">
                and date(bill.create_time) &lt;= date(#{data.duringEndTime})
            </if>
        </where>
        GROUP BY
        bill_deal_result,bill.bill_date
        ORDER BY
        bill.bill_date ASC
    </select>

    <select id="countByDealResultByYear" resultType="com.mascj.lup.event.bill.vo.CountEventDataDealResultVO">

        SELECT
        count( id ) `count`,
        bill_deal_result `name`,bill.bill_month
        FROM
        v_bill_data bill
        <where>
            bill.bill_year = #{data.billYear}

            <if test="data.duringStartTime != null and data.duringStartTime != '' ">
                and date(bill.create_time) >= date(#{data.duringStartTime})
            </if>

            <if test="data.duringEndTime != null and data.duringEndTime != '' ">
                and date(bill.create_time) &lt;= date(#{data.duringEndTime})
            </if>
        </where>
        GROUP BY
        bill_deal_result,bill.bill_month
        ORDER BY
        bill.bill_month ASC
    </select>


    <select id="countByOriginDataFetchWayByDate" resultType="com.mascj.lup.event.bill.vo.CountEventDataOriginVO">

        SELECT
        count( id ) `count`,
        data_origin `name`
        FROM
        v_bill_data bill
        <where>

            <if test="data.duringStartTime != null and data.duringStartTime != '' ">
                and date(bill.create_time) >= date(#{data.duringStartTime})
            </if>

            <if test="data.duringEndTime != null and data.duringEndTime != '' ">
                and date(bill.create_time) &lt;= date(#{data.duringEndTime})
            </if>

            <if test="data.billYear != null and data.billYear != '' ">
                and bill.bill_year = #{data.billYear}
            </if>

            <if test="data.billMonth != null and data.billMonth != '' ">
                and bill.bill_month = #{data.billMonth}
            </if>
        </where>
        GROUP BY
        data_origin
        ORDER BY
        data_origin ASC
    </select>

    <select id="dealOldDataPic" resultType="com.mascj.lup.event.bill.entity.LupData">

select  * from
    lup_data ldata where ldata.data_origin = 4 and DATE(ldata.create_time) <![CDATA[ <= ]]> #{endDate}

    </select>


    <select id="billQueryCount" resultType="com.mascj.lup.event.bill.vo.BillQueryCountVO">



        SELECT bill.grid_name,label.`name` event_type_name,COUNT(bill.id) as bill_count  FROM lup_data ldata join lup_bill bill on bill.data_id = ldata.id
                                                                                                             JOIN lup_data_label data_label on data_label .data_id = ldata.id
                                                                                                             JOIN lup_label label on label .id = data_label.label_id

        WHERE bill.deleted = 0 and bill.usable = 1 and  ldata.generate_bill=1
          and ldata.deleted=0 and label.deleted=0 and ldata.tenant_id=#{data.tenantId}  and ldata.original_data_logo in
                                                                                                               <foreach
                                                                                                                       collection="data.originalDataLogoList" close=")" index="index" item="item" open="(" separator=",">
                                                                                                               #{item}
                                                                                                               </foreach>
        GROUP BY bill.grid_name,label.`name`

    </select>

    <select id="billQueryList" resultType="com.mascj.lup.event.bill.vo.BillQueryListItemVO">

        SELECT DISTINCT bill.grid_name,label.`name` event_type_name,ldata.original_data_logo FROM lup_data ldata join lup_bill bill on bill.data_id = ldata.id
                                                                                                                 JOIN lup_data_label data_label on data_label .data_id = ldata.id
                                                                                                                 JOIN lup_label label on label .id = data_label.label_id

        WHERE bill.deleted = 0 and bill.usable = 1 and  ldata.generate_bill=1
          and ldata.deleted=0 and label.deleted=0 and ldata.tenant_id=#{data.tenantId}  and ldata.original_data_logo in
        <foreach
                collection="data.originalDataLogoList" close=")" index="index" item="item" open="(" separator=",">
            #{item}
        </foreach>


    </select>

</mapper>
