<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupDataPushLogMapper">

    <select id="pagedDataPushLog" resultType="com.mascj.lup.event.bill.vo.LupDataPushLogPagedItemVO">

        SELECT
        data_push_log.pushable,
        IFNULL( bill.create_time, ldata.create_time ) create_time,
        data_push_log.id,
        data_push_log.data_id,
        data_push_log.data_origin,
        data_push_log.generate_bill,
        IFNULL( data_push_log.push_time, data_push_log.create_time ) push_time,
        bill.bill_number,
        data_push_log.push_state,
        data_push_log.push_msg
        FROM
        lup_data_push_log data_push_log
        JOIN lup_data ldata ON ldata.id = data_push_log.data_id
        LEFT JOIN lup_bill bill ON bill.data_id = data_push_log.data_id
        WHERE
            data_push_log.deleted = 0

          <if test="data.number !=null and data.number != ''">
            and ( data_push_log.bill_number like concat('%',#{data.number},'%') or data_push_log.data_id like concat('%',#{data.number},'%')
                or bill.bill_number like concat('%',#{data.number},'%')
                )
          </if>

        <if test="data.startTime !=null and data.startTime != '' and data.endTime !=null and data.endTime != ''">
            and date(data_push_log.create_time) BETWEEN #{data.startTime} and #{data.endTime}
        </if>
        <if test="data.generateBill !=null">
          and data_push_log.generate_bill = #{data.generateBill}
        </if>

        <if test="data.generateBillList !=null and data.generateBillList.size > 0">
            and data_push_log.generate_bill in
            <foreach collection="data.generateBillList" separator="," close=")" index="index" item="item" open="(">#{item}</foreach>
        </if>



        <if test="data.pushState !=null">
            and data_push_log.push_state = #{data.pushState}
        </if>

        <if test="data.pushStateList !=null and data.pushStateList.size > 0">
            and data_push_log.push_state in
            <foreach collection="data.pushStateList" separator="," close=")" index="index" item="item" open="(">#{item}</foreach>
        </if>


        <if test="data.dataOrigin !=null">
            and data_push_log.data_origin = #{data.dataOrigin}
        </if>

        <if test="data.dataOriginList !=null and data.dataOriginList.size > 0">
            and data_push_log.data_origin in
            <foreach collection="data.dataOriginList" separator="," close=")" index="index" item="item" open="(">#{item}</foreach>
        </if>


        order by data_push_log.create_time desc,data_push_log.id desc


    </select>


    <select id="listDataPushLog"  resultType="com.mascj.lup.event.bill.vo.LupDataPushLogExcelVO">

        SELECT
        data_push_log.pushable,
        data_push_log.data_id,
        IFNULL( bill.create_time, ldata.create_time ) create_time,
        data_push_log.id,
        data_push_log.data_origin,
        data_push_log.generate_bill,
        IFNULL( data_push_log.push_time, data_push_log.create_time ) push_time,
        bill.bill_number,
        data_push_log.push_state,
        data_push_log.push_msg
        FROM
        lup_data_push_log data_push_log
        JOIN lup_data ldata ON ldata.id = data_push_log.data_id
        LEFT JOIN lup_bill bill ON bill.data_id = data_push_log.data_id

        <if test="data.number !=null and data.number != ''">
            and ( data_push_log.bill_number like concat('%',#{data.number},'%') or data_push_log.data_id like concat('%',#{data.number},'%'))
        </if>

        <if test="data.startTime !=null and data.startTime != '' and data.endTime !=null and data.endTime != ''">
            and date(data_push_log.create_time) BETWEEN #{data.startTime} and #{data.endTime}
        </if>
        <if test="data.generateBill !=null">
            and data_push_log.generate_bill = #{data.generateBill}
        </if>

        <if test="data.generateBillList !=null and data.generateBillList.size > 0">
            and data_push_log.generate_bill in
            <foreach collection="data.generateBillList" separator="," close=")" index="index" item="item" open="(">#{item}</foreach>
        </if>



        <if test="data.pushState !=null">
            and data_push_log.push_state = #{data.pushState}
        </if>

        <if test="data.pushStateList !=null and data.pushStateList.size > 0">
            and data_push_log.push_state in
            <foreach collection="data.pushStateList" separator="," close=")" index="index" item="item" open="(">#{item}</foreach>
        </if>


        <if test="data.dataOrigin !=null">
            and data_push_log.data_origin = #{data.dataOrigin}
        </if>

        <if test="data.dataOriginList !=null and data.dataOriginList.size > 0">
            and data_push_log.data_origin in
            <foreach collection="data.dataOriginList" separator="," close=")" index="index" item="item" open="(">#{item}</foreach>
        </if>

        order by data_push_log.create_time desc,data_push_log.id desc
    </select>

</mapper>
