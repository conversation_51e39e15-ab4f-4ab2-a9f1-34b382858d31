<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.DataReportTemplateMapper">
    <select id="listPage" resultType="com.mascj.lup.event.bill.vo.DataReportTemplateVO">
        SELECT
        a.id,
        a.report_type_tag,
        a.file_template_name,
               a.org_id
        FROM
        aep_report_template a WHERE a.deleted = 0
        <if test="search.fileTemplateName != null and search.fileTemplateName != ''">
            AND a.file_template_name like concat('%',#{search.fileTemplateName},'%')
        </if>
        <if test="search.reportTypeTag != null and search.reportTypeTag != ''">
            AND a.report_type_tag = #{reportTypeTag}
        </if>
        ORDER BY a.id ASC
    </select>
</mapper>
