<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.EventTJMapper">

    <select id="eventRank" resultType="com.mascj.lup.event.bill.vo.FlyEventRankVO">
        SELECT aa.*,l.`name` labelName from
       (
            SELECT ld.label_id,COUNT(1) num  from lup_bill b
              left join  lup_data_label ld on ld.data_id = b.data_id
                left join lup_flow_entity dd on dd.id = b.flow_entity_id
              where  1=1

        <if test="flowState != null">
            and dd.flow_state = #{flowState}
        </if>
        <if  test="monthStart != null and monthStart != '' ">
            and  #{monthStart} &lt;= DATE_FORMAT(b.create_time, '%Y-%m')
        </if>
        <if  test="monthEnd != null and monthEnd != '' ">
            and DATE_FORMAT(b.create_time, '%Y-%m')  &lt;=  #{monthEnd}
        </if>

        <if  test="dateStart != null and dateStart != '' ">
            and  #{dateStart} &lt;= DATE_FORMAT(b.create_time, '%Y-%m-%d')
        </if>
        <if  test="dateEnd != null and dateEnd != '' ">
            and DATE_FORMAT(b.create_time, '%Y-%m-%d')  &lt;=  #{dateEnd}
        </if>

                and b.deleted=0
            GROUP BY ld.label_id

        )  aa
            LEFT JOIN  lup_label l  on aa.label_id = l.id
          where l.deleted = 0

        order by aa.num desc
        limit #{top}

    </select>
    <select id="eventRankLine" resultType="com.mascj.lup.event.bill.vo.FlyEventMonthTjVO">

        SELECT
            aa.*,
            l.`name` labelName
        FROM
            (
                SELECT
                    ld.label_id,
                    COUNT( 1 ) num,
                <if test="dateType != null and dateType=='month' ">
                     DATE_FORMAT(b.create_time,'%Y-%m') month
                </if>
                <if test="dateType != null and dateType=='date' ">
                     DATE_FORMAT(b.create_time,'%Y-%m-%d') month
                </if>
                FROM
                    lup_bill b
                    left join lup_flow_entity dd on dd.id = b.flow_entity_id
                        LEFT JOIN lup_data_label ld ON ld.data_id = b.data_id
                where  1=1
                    <if test="flowState != null">
                        and dd.flow_state = #{flowState}
                    </if>
                    <if  test="monthStart != null and monthStart != '' ">
                        and  #{monthStart} &lt;= DATE_FORMAT(b.create_time, '%Y-%m')
                    </if>
                    <if  test="monthEnd != null and monthEnd != '' ">
                        and DATE_FORMAT(b.create_time, '%Y-%m')  &lt;=  #{monthEnd}
                    </if>
                <if  test="dateStart != null and dateStart != '' ">
                    and  #{dateStart} &lt;= DATE_FORMAT(b.create_time, '%Y-%m-%d')
                </if>
                <if  test="dateEnd != null and dateEnd != '' ">
                    and DATE_FORMAT(b.create_time, '%Y-%m-%d')  &lt;=  #{dateEnd}
                </if>

                  and b.deleted=0  and b.usable = 1
                and ld.label_id in
                 <foreach collection="labelIdList" close=")" item="item" open="(" separator=",">
                      #{item}
                  </foreach>
                GROUP BY
                    `month`,
                    ld.label_id
            ) aa
                LEFT JOIN lup_label l ON aa.label_id = l.id
        where l.deleted = 0

    </select>
    <select id="eventTj" resultType="com.mascj.lup.event.bill.vo.FlyEventTjResVO">
        SELECT  SUM(IF(dd.flow_state=1,1,0)) eventNum ,   SUM(IF(dd.flow_state=4,1,0)) eventEndNum
        from lup_bill b
        left join lup_flow_entity dd on dd.id = b.flow_entity_id
        where  1=1  and b.deleted=0 and b.usable = 1
        <if test="flowState != null">
            and dd.flow_state = #{flowState}
        </if>
        <if  test="monthStart != null and monthStart != '' ">
            and  #{monthStart} &lt;= DATE_FORMAT(b.create_time, '%Y-%m')
        </if>
        <if  test="monthEnd != null and monthEnd != '' ">
            and DATE_FORMAT(b.create_time, '%Y-%m')  &lt;=  #{monthEnd}
        </if>

        <if  test="dateStart != null and dateStart != '' ">
            and  #{dateStart} &lt;= DATE_FORMAT(b.create_time, '%Y-%m-%d')
        </if>
        <if  test="dateEnd != null and dateEnd != '' ">
            and DATE_FORMAT(b.create_time, '%Y-%m-%d')  &lt;=  #{dateEnd}
        </if>

    </select>
</mapper>
