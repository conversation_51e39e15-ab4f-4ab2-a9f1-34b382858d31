<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupFlowEntityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mascj.lup.event.bill.entity.LupFlowEntity">
        <id column="id" property="id" />
        <result column="process_definition_id" property="processDefinitionId" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="process_task_id" property="processTaskId" />
        <result column="flow_state" property="flowState" />
        <result column="archived_time" property="archivedTime" />
        <result column="processing_duration" property="processingDuration" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="ownership" property="ownership" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, process_definition_id, process_instance_id, process_task_id, flow_state, archived_time, processing_duration, tenant_id, create_by,
            create_time, update_by, update_time, ownership
    </sql>
    <select id="getByOne" resultType="com.mascj.lup.event.bill.entity.LupFlowEntity">

        select  * from  lup_flow_entity
        where 1=1
        <if test="processInstanceId != null">
            and process_instance_id =#{processInstanceId}
        </if>
        <if test="processTaskId != null and processTaskId != ''">
            and process_task_id =#{processTaskId}
        </if>
    </select>


    <select id="list_deal_four" resultType="com.mascj.lup.event.bill.entity.LupFlowEntity">
        select  b.id,b.tenant_id  from  lup_bill b
        left join  lup_flow_entity f on f.id = b.flow_entity_id
        where f.id is null and b.deleted = 0
    </select>

</mapper>
