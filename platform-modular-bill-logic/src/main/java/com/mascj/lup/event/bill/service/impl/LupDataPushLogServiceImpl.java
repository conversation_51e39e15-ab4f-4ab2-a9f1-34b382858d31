package com.mascj.lup.event.bill.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.ExportFileDTO;
import com.mascj.lup.event.bill.dto.LupDataPushLogPagedDTO;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.entity.LupDataPushLog;
import com.mascj.lup.event.bill.entity.LupFlowEntity;
import com.mascj.lup.event.bill.enums.DataPushStateEnum;
import com.mascj.lup.event.bill.enums.GenerateBillEnum;
import com.mascj.lup.event.bill.mapper.LupDataMapper;
import com.mascj.lup.event.bill.mapper.LupDataPushLogMapper;
import com.mascj.lup.event.bill.service.ILupBillService;
import com.mascj.lup.event.bill.service.ILupDataPushLogService;
import com.mascj.lup.event.bill.service.ILupDataService;
import com.mascj.lup.event.bill.service.ILupFlowEntityService;
import com.mascj.lup.event.bill.vo.LupDataPushLogExcelVO;
import com.mascj.lup.event.bill.vo.LupDataPushLogPagedItemVO;
import com.mascj.platform.system.entity.Dict;
import com.mascj.platform.system.feign.ISysDictProvider;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/8 19:59
 * @describe
 */
@Service
@AllArgsConstructor
public class LupDataPushLogServiceImpl  extends ServiceImpl<LupDataPushLogMapper, LupDataPushLog> implements ILupDataPushLogService {

    private final ISysDictProvider dictProvider;

    @Override
    public Page<LupDataPushLogPagedItemVO> pagedDataPushLog(LupDataPushLogPagedDTO dataPushLogPagedDTO) {

        Map<String,String> eventOriginalMap = fetchEventOriginal();

        Page<LupDataPushLogPagedItemVO> page = new Page<>(dataPushLogPagedDTO.getCurrent(),dataPushLogPagedDTO.getSize());

        List<LupDataPushLogPagedItemVO> pushLogPagedItemVOList = baseMapper.pagedDataPushLog(page,dataPushLogPagedDTO);

        pushLogPagedItemVOList.forEach(item->{

            item.setEventOrigin(eventOriginalMap.get(item.getDataOrigin().toString()));

            GenerateBillEnum generateBillEnum = GenerateBillEnum.parse(item.getGenerateBill());
            item.setGenerateBillName(generateBillEnum.getName());
            DataPushStateEnum pushStateEnum =DataPushStateEnum.parse(item.getPushState());
            item.setPushStateName(pushStateEnum.getName());

        });

        page.setRecords(pushLogPagedItemVOList);

        return page;

    }

    private Map<String,String> fetchEventOriginal(){
        Map<String,String> eventOriginalMap = new HashMap<>();
        //查询字典
        Result<List<Dict>> result = dictProvider.getList(VariableConstants.EventOriginCode);
        if(result.isSuccess()) {
            Map<String, Dict>  dictMap= result.getData().stream()
                    .filter(s -> s.getDictKey() != null && !"".equals(s.getDictKey()))
                    .collect(Collectors.toMap(Dict::getDictKey, each -> each, (value1, value2) -> value1));
            dictMap.keySet().forEach(key->{
                eventOriginalMap.put(key,dictMap.get(key).getDictValue());
            });

        }

        return eventOriginalMap;
    }

    @Override
    public List<LupDataPushLogExcelVO> exportDataToExcel(ExportFileDTO exportFileDTO) {

        Map<String,String> eventOriginalMap = fetchEventOriginal();

        List<LupDataPushLogExcelVO> list = baseMapper.listDataPushLog(exportFileDTO);

        list.forEach(item->{
            item.setEventOrigin(eventOriginalMap.get(item.getDataOrigin().toString()));
            GenerateBillEnum generateBillEnum = GenerateBillEnum.parse(item.getGenerateBill());
            item.setGenerateBillName(generateBillEnum.getName());
            DataPushStateEnum pushStateEnum =DataPushStateEnum.parse(item.getPushState());
            item.setPushStateName(pushStateEnum.getName());

        });



        return list;
    }
}
