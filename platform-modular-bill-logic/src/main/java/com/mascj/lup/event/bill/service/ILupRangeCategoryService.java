package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.LupRangeCategoryAddDTO;
import com.mascj.lup.event.bill.entity.LupRangeCategory;
import com.mascj.lup.event.bill.entity.LupRangeLine;
import com.mascj.lup.event.bill.vo.LupRangeCategoryVO;

import java.util.List;

/**
 * <p>
 * 事件标签 按名字去重 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface ILupRangeCategoryService extends IService<LupRangeCategory> {

    /**
     * 新增文件类目
     * @param rangeCategoryAddDTO
     * @return
     */
    Result add(LupRangeCategoryAddDTO rangeCategoryAddDTO);

    List<LupRangeCategoryVO> listAll();
}
