package com.mascj.lup.event.bill.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.QueryBillPictureInfoDTO;
import com.mascj.lup.event.bill.service.ILupBillService;
import com.mascj.lup.event.bill.service.ILupDataService;
import com.mascj.lup.event.bill.service.ILupQueryBillService;
import com.mascj.lup.event.bill.vo.BillDetailVO;
import com.mascj.lup.event.bill.vo.QueryBillPictureInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/26 15:16
 * @describe
 */
@Service
@AllArgsConstructor
public class LupQueryBillServiceImpl implements ILupQueryBillService {

    private final ILupBillService billService;

    private final ILupDataService dataService;


    @Override
    public IPage<QueryBillPictureInfoVO> queryBillPictureInfo(QueryBillPictureInfoDTO queryBillPictureInfoDTO) {

        final List<QueryBillPictureInfoVO> list = new ArrayList<>();
        List<QueryBillPictureInfoVO> finalList = new ArrayList<>();
        List<String>listPicUrl = new ArrayList<>();

        Page<QueryBillPictureInfoVO> pageQueryBillPictureInfoVO = new Page<>(queryBillPictureInfoDTO.getCurrent(),queryBillPictureInfoDTO.getSize());
        /**
         * 1 处置系统
         * 2 中台
         * 3 飞控
         */
        switch (queryBillPictureInfoDTO.getOriginType()){
            case 1://1 处置系统


                Result<BillDetailVO> billDetailVOResult =  billService.detailByDataId(queryBillPictureInfoDTO.getOriginType(),queryBillPictureInfoDTO.getEventId());
                BillDetailVO billDetailVO = billDetailVOResult.getData();

                Map<String, Object> patrolMap =   billDetailVO.getFormData().get("patrol");
                if(patrolMap!=null && patrolMap.containsKey("photos") && !"".equals(patrolMap.get("photos"))) {
                    JSONArray array = JSONUtil.parseArray(JSONUtil.toJsonStr(patrolMap.get("photos")));

                    array.forEach(item -> {

                        QueryBillPictureInfoVO queryBillPictureInfoVO = new QueryBillPictureInfoVO();
                        queryBillPictureInfoVO.setPicUrl(item.toString());

                        list.add(queryBillPictureInfoVO);
                    });
                }

                pageQueryBillPictureInfoVO.setTotal(1);
                finalList = list;
                break;
            case 2://2 中台


                Page page = new Page<>(queryBillPictureInfoDTO.getCurrent(),queryBillPictureInfoDTO.getSize());
                List<BillDetailVO> billDetailVOList = billService.listBillDetailVOByQueryBillPictureInfo(page,queryBillPictureInfoDTO);


                billDetailVOList.forEach(item->{

                    if(item.getExtraDataVO()!=null && ObjectUtil.isNotEmpty(item.getExtraDataVO().getEventPictureUrl())){

                        if(item.getExtraDataVO().getEventPictureUrlList() == null ){
                            item.getExtraDataVO().setEventPictureInfoList(new ArrayList<>());
                        }
                        if(!item.getExtraDataVO().getEventPictureUrlList().contains(item.getExtraDataVO().getEventPictureUrl())){
                            item.getExtraDataVO().getEventPictureUrlList().add(item.getExtraDataVO().getEventPictureUrl());
                        }

                    }

                    if(item.getExtraDataVO()!=null && item.getExtraDataVO().getEventPictureUrlList()!=null) {
                        item.getExtraDataVO().getEventPictureUrlList().forEach(e -> {

                            if(!listPicUrl.contains(e)) {
                                listPicUrl.add(e);
                                QueryBillPictureInfoVO queryBillPictureInfoVO = new QueryBillPictureInfoVO();
                                queryBillPictureInfoVO.setBillNumber(item.getBillNumber());
                                queryBillPictureInfoVO.setPicUrl(e);
                                queryBillPictureInfoVO.setCreateTime(item.getCreateTime());
                                queryBillPictureInfoVO.setEventType(item.getEventLabel());

                                list.add(queryBillPictureInfoVO);

                            }
                        });
                    }



                });

                page.setRecords(billDetailVOList);

                pageQueryBillPictureInfoVO.setTotal(page.getTotal());

                finalList = list.stream().sorted(Comparator.comparing(QueryBillPictureInfoVO::getBillNumber)).collect(Collectors.toList());
                break;
            case 3: //3 飞控


                break;
        }

        pageQueryBillPictureInfoVO.setRecords(finalList);

        return pageQueryBillPictureInfoVO;
    }
}
