package com.mascj.lup.event.bill.service.impl;

import cc.lyiot.framework.common.pojo.CommonResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.event.dimension.enumeration.ModuleDataSourceTypeEnum;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.redis.core.RedisService;
import com.mascj.kernel.tools.RedisKey;
import com.mascj.lup.cds.survey.feign.SurveyAchievementFeign;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.dto.LupBillPendingApplyDTO;
import com.mascj.lup.event.bill.dto.LupBillPendingApprovalDTO;
import com.mascj.lup.event.bill.dto.LupBillPendingOldDataDTO;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupBillPending;
import com.mascj.lup.event.bill.entity.LupBillPendingFile;
import com.mascj.lup.event.bill.entity.LupSource;
import com.mascj.lup.event.bill.enums.EventSourceType;
import com.mascj.lup.event.bill.enums.PendingStateEnum;
import com.mascj.lup.event.bill.geo.GeoTile;
import com.mascj.lup.event.bill.mapper.LupBillMapper;
import com.mascj.lup.event.bill.mapper.LupBillPendingMapper;
import com.mascj.lup.event.bill.mapper.LupSourceMapper;
import com.mascj.lup.event.bill.service.ILupBillPendingFileService;
import com.mascj.lup.event.bill.service.ILupBillPendingService;
import com.mascj.lup.event.bill.service.ILupSourceService;
import com.mascj.lup.event.bill.util.ProjectUtil;
import com.mascj.lup.event.bill.util.ReadConfigUtil;
import com.mascj.lup.event.bill.vo.LupBillPendingDataItemVO;
import com.mascj.lup.event.bill.vo.LupBillPendingVO;
import com.mascj.lup.event.bill.vo.SourceTileVO;
import com.mascj.lup.event.bill.vo.config.HandleModuleConfigVO;
import com.mascj.lup.event.feign.EventStreamFeign;
import com.mascj.lup.event.vo.AchievementPreviewVO;
import com.mascj.platform.system.entity.User;
import com.mascj.platform.system.feign.ISysUserProvider;
import com.mascj.support.file.api.feign.IFileProvider;
import com.mascj.support.file.api.model.vo.PreviewUrlVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 事件资源记录表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
@Slf4j
@AllArgsConstructor
public class LupBillPendingServiceImpl extends ServiceImpl<LupBillPendingMapper, LupBillPending> implements ILupBillPendingService {

    private ILupBillPendingFileService pendingFileService;
    private LupBillMapper billMapper;
    private final ReadConfigUtil readConfigUtil;
    private final ISysUserProvider userProvider;
    private final IFileProvider fileProvider;
    private final RedisService redisPlane;


    @Override
    @Transactional
    public Boolean applyPending(LupBillPendingApplyDTO applyDTO) {

        clearBillCache(applyDTO.getBillId());

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();

        LupBill bill = billMapper.selectById(applyDTO.getBillId());
        if( handleModuleConfigVO.getEventHangingSwitch() && bill != null) {
            PendingStateEnum pendingStateEnum = PendingStateEnum.parseFromState(bill.getPendingState());
            if (pendingStateEnum != PendingStateEnum.PendingStateEnumWaitViewing && pendingStateEnum != PendingStateEnum.PendingStateEnumViewingAccess){

                LupBillPending billPending = BeanUtil.toBean(applyDTO,LupBillPending.class);

                save(billPending);

                if(ObjectUtil.isNotEmpty(applyDTO.getFileIdList())){
                    List<LupBillPendingFile> pendingFileList = new ArrayList<>();
                    applyDTO.getFileIdList().forEach(fileId->{
                        LupBillPendingFile pendingFile = new LupBillPendingFile();
                        pendingFile.setPendingId(billPending.getId());
                        pendingFile.setFileId(fileId);
                        pendingFileList.add(pendingFile);
                    });
                    pendingFileService.saveBatch(pendingFileList);
                }

                LupBill tmp = new LupBill();
                tmp.setId(applyDTO.getBillId());
                tmp.setPendingState(PendingStateEnum.PendingStateEnumWaitViewing.getPendingState());

                billMapper.updateById(tmp);

                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean approval(LupBillPendingApprovalDTO approvalDTO) {

        clearBillCache(approvalDTO.getBillId());

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();
        LupBill bill = billMapper.selectById(approvalDTO.getBillId());
        if(handleModuleConfigVO.getEventHangingSwitch() && bill.getPendingState()!=null) {
            PendingStateEnum pendingStateEnum = PendingStateEnum.parseFromState(bill.getPendingState());
            if (pendingStateEnum == PendingStateEnum.PendingStateEnumWaitViewing) {

                List<LupBillPending> billPendingList = list(Wrappers.<LupBillPending>lambdaQuery()
                        .eq(LupBillPending::getBillId,approvalDTO.getBillId())
                        .eq(LupBillPending::getApprovalState,PendingStateEnum.PendingStateEnumNone.getPendingState())
                        .orderByDesc(LupBillPending::getCreateTime).orderByDesc(LupBillPending::getId));

                if(billPendingList.size()>0){

                    LupBillPending billPending = new LupBillPending();
                    billPending.setId(billPendingList.get(0).getId());

                    billPending.setApprovalTime(DateUtil.now());
                    billPending.setApprovalState(approvalDTO.getApprovalState());
                    billPending.setApprovalUserId(LmContextHolder.getUserId());
                    billPending.setRejectReason(approvalDTO.getRejectReason());

                    updateById(billPending);

                    LupBill tmp = new LupBill();
                    tmp.setId(approvalDTO.getBillId());
                    //审核通过 2  审核驳回 3
                    tmp.setPendingState(approvalDTO.getApprovalState());

                    billMapper.updateById(tmp);
                    return Boolean.TRUE;
                }
            }
        }

        return Boolean.FALSE;
    }

    @Override
    public Boolean resetBillPending(Long billId) {

        clearBillCache(billId);

        //查询事件
        LupBill bill = billMapper.selectById(billId);
        if(bill.getPendingState()!=null) {
            PendingStateEnum pendingStateEnum = PendingStateEnum.parseFromState(bill.getPendingState());
            if (pendingStateEnum == PendingStateEnum.PendingStateEnumWaitViewing
                    || pendingStateEnum == PendingStateEnum.PendingStateEnumViewingAccess) {
                //恢复数据
                bill = new LupBill();
                bill.setId(billId);
                bill.setPendingState(PendingStateEnum.PendingStateEnumNone.getPendingState());
                billMapper.updateById(bill);

                //处理待申请的数据
                List<LupBillPending> billPendingList = list(Wrappers.<LupBillPending>lambdaQuery()
                        .eq(LupBillPending::getBillId,billId)
                        .eq(LupBillPending::getApprovalState,PendingStateEnum.PendingStateEnumNone.getPendingState())
                        .orderByDesc(LupBillPending::getCreateTime).orderByDesc(LupBillPending::getId));

                if(billPendingList.size()>0){
                    billPendingList.forEach(e->{
                        LupBillPending billPending = new LupBillPending();
                        billPending.setId(e.getId());
                        billPending.setDeleted(1);
                        updateById(billPending);
                    });
                }



                return Boolean.TRUE;
            }
        }

        //查询流程期限 修改流程期限



        return Boolean.FALSE;
    }

    @Override
    public Boolean dealHistoryPendingBill(LupBillPendingOldDataDTO billPendingOldDataDTO) {

        List<LupBillPendingVO> billList = baseMapper.queryHistoryPendingBill(billPendingOldDataDTO);

        if(ObjectUtil.isNotEmpty(billList)){

            billList.forEach(e->{
                LmContextHolder.setTenantId(e.getTenantId().toString());

                HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();

                if(handleModuleConfigVO.getEventHangingSwitch()) {
                    //填写申请记录
                    LupBillPendingApplyDTO applyDTO = new LupBillPendingApplyDTO();
                    applyDTO.setBillId(e.getId());
                    applyDTO.setApplyReason("【系统补充】后期处理申请操作");
                    applyDTO.setProcessTaskId(e.getProcessTaskId());

                    applyPending(applyDTO);

                }
            });


            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    public Map<String, List<LupBillPendingDataItemVO>> mapLupBillPendingDataItemVOByFlowTaskId(Long billId) {

        Map<String, List<LupBillPendingDataItemVO>> resultMap = new HashMap<>();

        List<LupBillPending> allBillPendingList = list(Wrappers
                .<LupBillPending>lambdaQuery().eq(LupBillPending::getBillId,billId).orderByAsc(LupBillPending::getCreateTime));

        Map<String, List<LupBillPending>>  orginMap =   allBillPendingList.stream().collect(Collectors.groupingBy(LupBillPending::getProcessTaskId));

        orginMap.keySet().forEach(key->{

            List<LupBillPending> billPendingList = orginMap.get(key);
            List<LupBillPendingDataItemVO> list = new ArrayList<>();

            billPendingList.forEach(item->{
                LupBillPendingDataItemVO billPendingDataItemVO = new LupBillPendingDataItemVO();
                Result<User> userResult = userProvider.getUserById(item.getCreateBy());
                billPendingDataItemVO.setApplyUserName(userResult.getData().getName());
                billPendingDataItemVO.setApplyReason(item.getApplyReason());
                billPendingDataItemVO.setCreateTime(item.getCreateTime());

                List<LupBillPendingFile> pendingFileList = pendingFileService.list(Wrappers.<LupBillPendingFile>lambdaQuery().eq(LupBillPendingFile::getPendingId,item.getId()));

                if(pendingFileList.size()>0) {
                    Result<List<PreviewUrlVO>> fileResult = fileProvider.getFileUrlMore(pendingFileList.stream().map(LupBillPendingFile::getFileId).collect(Collectors.toList()));

                    List<String> fileList = fileResult.getData().stream().map(PreviewUrlVO::getPublicFileUrl).collect(Collectors.toList());

                    billPendingDataItemVO.setFileList(fileList);
                }

                if(item.getApprovalUserId() != null) {
                     userResult = userProvider.getUserById(item.getApprovalUserId());
                    billPendingDataItemVO.setApprovalUserName(userResult.getData().getName());
                    billPendingDataItemVO.setApprovalTime(item.getApprovalTime());


                    PendingStateEnum viewPendingStateEnum = PendingStateEnum.parseFromState(item.getApprovalState());
                    if(viewPendingStateEnum == PendingStateEnum.PendingStateEnumViewingReject ||
                            viewPendingStateEnum == PendingStateEnum.PendingStateEnumViewingAccess) {
                        billPendingDataItemVO.setApprovalResult(viewPendingStateEnum.getName());
                        billPendingDataItemVO.setApprovalResultState(viewPendingStateEnum);
                        billPendingDataItemVO.setRejectReason(item.getRejectReason());
                    }
                }

                list.add(billPendingDataItemVO);

            });


            resultMap.put(key,list);
        });




        return resultMap;
    }

    public void clearBillCache(Long billId){
        RedisKey redisKey = RedisKey.forService(VariableConstants.lupHandleSystemTag, LupBillServiceImpl.class)
                .forParameter(VariableConstants.HandleModuleConfigCode,VariableConstants.HandleModuleBillDataCode, String.valueOf(LmContextHolder.getTenantId()),String.valueOf(billId));
        redisPlane.del(redisKey.toString());
        redisKey = RedisKey.forService(VariableConstants.lupHandleSystemTag, LupBillServiceImpl.class)
                .forParameter(VariableConstants.HandleModuleConfigCode,VariableConstants.HandleModuleBillDataQueryCode, String.valueOf(LmContextHolder.getTenantId()),String.valueOf(billId));
        redisPlane.del(redisKey.toString());
    }
}
