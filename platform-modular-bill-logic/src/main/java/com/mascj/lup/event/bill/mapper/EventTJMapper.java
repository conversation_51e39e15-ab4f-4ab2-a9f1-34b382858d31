package com.mascj.lup.event.bill.mapper;

import com.mascj.lup.event.bill.vo.FlyEventMonthTjVO;
import com.mascj.lup.event.bill.vo.FlyEventRankVO;
import com.mascj.lup.event.bill.vo.FlyEventTjQueryVO;
import com.mascj.lup.event.bill.vo.FlyEventTjResVO;

import java.util.List;

public interface EventTJMapper {

    List<FlyEventRankVO> eventRank(FlyEventTjQueryVO flyEventTjQueryVO);
    List<FlyEventMonthTjVO> eventRankLine(FlyEventTjQueryVO flyEventTjQueryVO);

    FlyEventTjResVO eventTj(FlyEventTjQueryVO flyEventTjQueryVO);
}
