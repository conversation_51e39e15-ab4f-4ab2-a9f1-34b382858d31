package com.mascj.lup.event.bill.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.lup.event.bill.entity.LupBatch;
import com.mascj.lup.event.bill.entity.LupBatchSource;
import com.mascj.lup.event.bill.entity.LupSource;
import com.mascj.lup.event.bill.mapper.LupBatchMapper;
import com.mascj.lup.event.bill.mapper.LupBatchSourceMapper;
import com.mascj.lup.event.bill.service.ILupBatchService;
import com.mascj.lup.event.bill.service.ILupBatchSourceService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 事件数据 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
public class LupBatchSourceServiceImpl extends ServiceImpl<LupBatchSourceMapper, LupBatchSource> implements ILupBatchSourceService {

    @Override
    public List<LupSource> listByBatch(Long batchId) {
        return baseMapper.listByBatch(batchId);
    }
}
