package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.mascj.lup.event.bill.dto.ReportTemplateSearchDTO;
import com.mascj.lup.event.bill.entity.DataReportTemplate;
import com.mascj.lup.event.bill.vo.DataReportTemplateVO;
import org.apache.ibatis.annotations.Param;

public interface DataReportTemplateMapper extends BaseMapper<DataReportTemplate> {

    IPage<DataReportTemplateVO> listPage(Page<DataReportTemplateVO> page, @Param("search") ReportTemplateSearchDTO search);
}
