package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.mascj.lup.event.bill.entity.LupLabelFlow;
import com.mascj.lup.event.bill.vo.LupLabelFlowVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 标签 关联流程数据 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface LupLabelFlowMapper extends BaseMapper<LupLabelFlow> {
    IPage<LupLabelFlowVO> labelFlowPage(@Param("codeValue") Integer codeValue, @Param("page") Page<Object> page);

    List<LupLabelFlow> labelFlowDetailByState(@Param("flowId") Long flowId,@Param("flowState") Integer flowState);

}
