package com.mascj.lup.event.bill.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.CollectionUtil;
import com.mascj.kernel.common.util.StringUtil;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupFlowEntity;
import com.mascj.lup.event.bill.mapper.LupBillMapper;
import com.mascj.lup.event.bill.mapper.LupFlowEntityMapper;
import com.mascj.lup.event.bill.service.ILupFlowService;
import com.mascj.lup.event.bill.vo.BillDetailVO;
import com.mascj.lup.event.bill.vo.FourCallbackVO;
import com.mascj.lup.event.bill.vo.LupSubmitForm;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class FourCallbakService {

    @Resource
    private LupBillMapper lupBillMapper;
    @Resource
    private LupFlowEntityMapper lupFlowEntityMapper;
    @Resource
    private ILupFlowService lupFlowService;


    public Result addFourCallbak(FourCallbackVO fourCallbackVO){
        String tenantId = LmContextHolder.getTenantId();

        String sourceId = fourCallbackVO.getSourceId();
        LupBill lupBill = lupBillMapper.selectOne(new LambdaQueryWrapper<>(new LupBill())
                .eq(LupBill::getBillNumber, sourceId));
        if (lupBill == null){
            return Result.fail();
        }
        if (  StringUtil.isNotBlank(tenantId) &&  !lupBill.getTenantId().toString().equals(tenantId)){
            return Result.fail();
        }
        LmContextHolder.setTenantId(lupBill.getTenantId().toString());

        LupFlowEntity lupFlowEntity = lupFlowEntityMapper.selectById(lupBill.getFlowEntityId());

        LupSubmitForm ee = new LupSubmitForm();

        Map<String, Object> formData = ee.getFormData();
        if (formData == null){
            formData = new HashMap<>();
        }
        String img = fourCallbackVO.getImg();
        if (StringUtil.isNotBlank(img)){
            String[] split = img.split(",");
            formData.put("photos", split);
        }
        String handName = fourCallbackVO.getHandName();
        if (StringUtil.isNotBlank(handName)){
            formData.put("four_handName", "处理人:"+handName);
        }else {
            formData.put("four_handName", "处理人:");
        }

        String handTime = fourCallbackVO.getHandTime();
        if (StringUtil.isNotBlank(handTime)){
            formData.put("four_handTime", "处理时间:"+handTime);
        }else {
            formData.put("four_handTime", "处理时间:" );
        }
        Integer success = fourCallbackVO.getSuccess();
        if (success == null){
            success =1 ;
        }
        formData.put("four_success_result", "处理结果:"+ (success == 1 ? "处理成功" : "已退回"));
        formData.put("fourSuccess", success);

        String fourReturnReason = fourCallbackVO.getFourReturnReason();
        if (StringUtil.isNotBlank(fourReturnReason) && success == 0){
            formData.put("four_return_reason", "退回原因:"+ fourReturnReason);
        }
        String callBackMsg = fourCallbackVO.getCallBackMsg();
        if (StringUtil.isNotBlank(callBackMsg) ){
            formData.put("four_callBackMsg", "结果概述:"+callBackMsg);
        }



        ee.setProcessInstanceId(lupFlowEntity.getProcessInstanceId());
        ee.setComment(fourCallbackVO.getCallBackMsg());
        ee.setFormData(fourCallbackVO.getExt());
        ee.setFormData(formData);
        lupFlowService.submitTaskByProcessId(ee);

        LupFlowEntity ee1 = new LupFlowEntity();
        ee1.setId(lupFlowEntity.getId());
        ee1.setFourSuccess(success);
        lupFlowEntityMapper.updateById(ee1);

        return Result.data(null);
    }

    public Result dealOldData() {
        List<LupFlowEntity> lupFlowEntities = lupFlowEntityMapper.list_deal_four();
        if (CollectionUtil.isNotEmpty(lupFlowEntities)){
            for (LupFlowEntity lupFlowEntity : lupFlowEntities) {
                try {
                    Long id = lupFlowEntity.getId();
                    Result<String> stringResult = lupFlowService.startupAepFlow(id, null);

                    Thread.sleep(1000L);

                    LupSubmitForm ee = new LupSubmitForm();
                    ee.setProcessInstanceId(stringResult.getData());
                    lupFlowService.submitTaskByProcessId(ee);


                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }

            }
        }
        return Result.data("");
    }
}
