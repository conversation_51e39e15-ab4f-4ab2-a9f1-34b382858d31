package com.mascj.lup.event.bill.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.BillReadStateCounterDTO;
import com.mascj.lup.event.bill.dto.BillReadStateDTO;
import com.mascj.lup.event.bill.entity.LupBillNotification;
import com.mascj.lup.event.bill.entity.LupBillRead;
import com.mascj.lup.event.bill.enums.BillReadStateEnum;
import com.mascj.lup.event.bill.mapper.LupBillNotificationMapper;
import com.mascj.lup.event.bill.mapper.LupBillReadMapper;
import com.mascj.lup.event.bill.service.ILupBillNotificationService;
import com.mascj.lup.event.bill.service.ILupBillReadService;
import com.mascj.lup.event.bill.vo.BillReadStateCounterVO;
import com.mascj.support.config.feign.IAepConfigProvider;
import com.mascj.support.config.feign.IConfigValueProvider;
import com.mascj.support.config.vo.ConfigInVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工单数据表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
@AllArgsConstructor
public class LupBillNotificationServiceImpl extends ServiceImpl<LupBillNotificationMapper, LupBillNotification> implements ILupBillNotificationService{

    private final IConfigValueProvider configValueProvider;

    private final IAepConfigProvider configProvider;
    /**
     *
     */
    @Override
    public void notificationProducer() {

        configProvider.getActivityList(VariableConstants.handleCode);

    }
}
