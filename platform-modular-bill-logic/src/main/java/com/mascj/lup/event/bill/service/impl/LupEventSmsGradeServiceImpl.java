package com.mascj.lup.event.bill.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.database.entity.Search;
import com.mascj.kernel.database.util.PageUtil;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.entity.*;
import com.mascj.lup.event.bill.mapper.*;
import com.mascj.lup.event.bill.service.ILupEventSmsGradeService;
import com.mascj.lup.event.bill.task.INotificationTaskService;
import com.mascj.lup.event.bill.vo.BillUserLabelVO;
import com.mascj.lup.event.bill.vo.LupEventLabelVo;
import com.mascj.platform.micro.entity.SysTenant;
import com.mascj.platform.micro.feign.ISysTenantProvider;
import com.mascj.platform.system.entity.Dict;
import com.mascj.platform.system.feign.ISysDictProvider;
import com.mascj.platform.system.feign.ISysUserProvider;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.mascj.lup.event.bill.constant.EventTipKey.UnknownAreaName;

/**
 * <p>
 * 处置分级短信 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2024-01-01
 */
@Slf4j
@Service
@AllArgsConstructor
public class LupEventSmsGradeServiceImpl extends ServiceImpl<LupEventSmsGradeMapper, LupEventSmsGrade> implements ILupEventSmsGradeService {

    @Resource
    private LupEventLabelMapper lupEventLabelMapper;
    @Resource
    private LupBillMapper lupBillMapper;
    @Resource
    private LupDataMapper lupDataMapper;
    @Resource
    private ISysTenantProvider sysTenantProvider;
    @Resource
    private ISysDictProvider sysDictProvider;
    @Resource
    private LupLabelFlowMapper lupLabelFlowMapper;
    @Resource
    private LupGridUserMapper lupGridUserMapper;
    @Resource
    private ISysUserProvider sysUserProvider;
    @Resource
    private INotificationTaskService notificationTaskService;


    @Override
    public Result set(LupEventSmsGrade lupEventSmsGrade) {
        Integer type = lupEventSmsGrade.getType();
        if (type == null) {
            return Result.fail("类型不能空");
        }
        Long labelId = lupEventSmsGrade.getLabelId();
        if (labelId == null) {
            return Result.fail("标签ID不能为空");
        }

        Integer codeValue = lupEventSmsGrade.getCodeValue();
        if (codeValue == null) {
            return Result.fail("事件来源不能为空");
        }
        if(type==2){
            Integer handleSms = lupEventSmsGrade.getHandleSms();
            Integer patrolSms = lupEventSmsGrade.getPatrolSms();
            if(handleSms==null && patrolSms==null){
                return Result.fail("紧急处置和巡查不能都为空");
            }
        }
        baseMapper.delete(new LambdaQueryWrapper<>(new LupEventSmsGrade())
                .eq(LupEventSmsGrade::getLabelId, lupEventSmsGrade.getLabelId())
                .eq(LupEventSmsGrade::getCodeValue, lupEventSmsGrade.getCodeValue()));

        baseMapper.insert(lupEventSmsGrade);
        return Result.success("成功");
    }

    @Override
    public Result<IPage<LupEventLabelVo>> listPage(Integer codeValue, Search search) {
        // 查询标签
        IPage<LupEventLabelVo> page = lupEventLabelMapper.listPage(codeValue, PageUtil.getPage(search));
        List<LupEventLabelVo> records = page.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            List<Long> collect = records.stream().map(LupEventLabelVo::getLabelId).collect(Collectors.toList());

            List<LupEventSmsGrade> smsGradeList = baseMapper.selectList(new LambdaQueryWrapper<LupEventSmsGrade>()
                   .eq(LupEventSmsGrade::getDeleted, 0).in(LupEventSmsGrade::getLabelId, collect)
            .eq(LupEventSmsGrade::getCodeValue, codeValue));
            if (CollectionUtil.isNotEmpty(smsGradeList)) {
                Map<Long, LupEventSmsGrade> collect1 = smsGradeList.stream().collect(Collectors.toMap(LupEventSmsGrade::getLabelId, e -> e,
                        (v1, v2) -> v1));
                records.forEach(record -> {record.setLupEventSmsGrade(collect1.get(record.getLabelId()));});
            }
        }
        return Result.data(page);
    }

    @Async("syncProduceImageExecutorPoolSms")
    @Override
    public void smsGrade(Long flowEntityId, Integer currentFlowState, Date date) {
        log.error("flowEntityId: {}", flowEntityId);
        List<BillUserLabelVO> billUserLabelVOS = lupBillMapper.listBillLabelAndDataIdOneVO(flowEntityId);
        log.error("billUserLabelVOS: {}", JSON.toJSONString(billUserLabelVOS));
        if (CollectionUtil.isEmpty(billUserLabelVOS)) {
           return;
        }
        BillUserLabelVO billUserLabelVO = billUserLabelVOS.get(0);

        LmContextHolder.setTenantId(billUserLabelVO.getTenantId().toString());

        LupData lupData = lupDataMapper.selectById(billUserLabelVO.getDataId());
        Integer dataOrigin = lupData.getDataOrigin();


        LupEventSmsGrade lupEventSmsGrade = baseMapper.selectOne(new LambdaQueryWrapper<>(new LupEventSmsGrade())
                .eq(LupEventSmsGrade::getCodeValue, dataOrigin)
                .eq(LupEventSmsGrade::getLabelId, billUserLabelVO.getLabelId()));
        if (lupEventSmsGrade == null) {

            log.error("没有配置：{}", JSON.toJSONString(billUserLabelVO));
            return;
        }
        //类型 1：一般(待办，要求事件计划执行，会发送事件处置的汇总通知短信；) 2紧急(速办，要求事件尽快处理，会实时发送巡查和处置短信。)
        Integer type = lupEventSmsGrade.getType();
        //处置短信是否开启 1开 0不开
        Integer handleSms = lupEventSmsGrade.getHandleSms() == null ? 0 : lupEventSmsGrade.getHandleSms();
        //处置短信是否开启 1开 0不开
        Integer patrolSms = lupEventSmsGrade.getPatrolSms() == null ? 0 : lupEventSmsGrade.getPatrolSms();

        if (type == 2 ) {
            if ( (handleSms == 1  && currentFlowState == 2 ) || (patrolSms == 1 && currentFlowState == 1) ) {
                // 发送短信



                Result<SysTenant> byId = sysTenantProvider.getById(billUserLabelVO.getTenantId());

                SysTenant data = byId.getData();
                String s = DateUtil.formatDateTime(date);

                Map<String,String> map = new HashMap<>();
                map.put("projectname",data.getName());
                map.put("createTime",s);
                map.put("gridName", billUserLabelVO.getGridName().equals(UnknownAreaName) ? "未知区域" : billUserLabelVO.getGridName() );//网格名称
                map.put("eventLabel", billUserLabelVO.getEventLabel());//事件标签

                Map<String, String> stringStringMap = listOrigin();
                map.put("eventOrigin", stringStringMap.get(dataOrigin.toString()) );//事件标签

                String deadLine = "24小时";

                List<LupLabelFlow> labelFlowList = lupLabelFlowMapper.selectList(new LambdaQueryWrapper<LupLabelFlow>()
                        .eq(LupLabelFlow::getDeleted, 0).eq(LupLabelFlow::getLabelId, billUserLabelVO.getLabelId())
                        .eq(LupLabelFlow::getFlowState, currentFlowState));
                if (CollectionUtil.isNotEmpty(labelFlowList)) {
                    LupLabelFlow lupLabelFlow = labelFlowList.get(0);
                    deadLine = lupLabelFlow.getTimeLimit().toString().replace(".0","小时");
                }
                map.put("deadLine", deadLine);
                //OFFLINE_INS_DEAL	事件线下处置
                //OFFLINE_INS_EVENT	 事件线下巡查


                LupBill lupBill = lupBillMapper.selectById(billUserLabelVO.getBillId());
                map.put("areaName", lupBill.getAreaName().equals(UnknownAreaName) ? "未知区域" : lupBill.getAreaName() );//区域名称

                Long gridUnitId = lupBill.getGridUnitId();
                List<LupGridUser> lupGridUsers = lupGridUserMapper.selectList(new LambdaQueryWrapper<>(new LupGridUser())
                        .eq(LupGridUser::getGridUnitId, gridUnitId));
                List<Long> userIds = null;
                //事件巡查处置通知的发送规则默认实时；默认发送对象为事件的网格人员，若未配置网格人员，则默认发送给角色事件处置执行员event_patrol；支持修改为自定义组织或角色或指定用户。
                if (CollectionUtil.isNotEmpty(lupGridUsers)) {
                    userIds = lupGridUsers.stream().map(LupGridUser::getUserId).collect(Collectors.toList());
                }else {

                    Result<List<Long>> eventPatrol = sysUserProvider.userListByRoleCodes(Arrays.asList("event_patrol"), Long.parseLong(LmContextHolder.getTenantId()));
                    userIds = eventPatrol.getData();
                }


                String code = "";
                if (currentFlowState ==1){
                    code = "OFFLINE_INS_EVENT";
                }else if (currentFlowState ==2){
                    code = "OFFLINE_INS_DEAL";
                }

                notificationTaskService.sendSms(userIds, 2,code, map);


                // 事件线下处置	SMS_489230040	  尊敬的用户您好，${projectname}于${createTime}在${areaName}附近${gridName}网格${eventOrigin}发现的${eventLabel}已巡查确认存在，处理期限${deadLine}，请及时前往现场进行处置！
                // 事件线下巡查	SMS_489260028	  尊敬的用户您好，${projectname}于${createTime}在${areaName}附近${gridName}网格${eventOrigin}发现了${eventLabel}，处理期限${deadLine}，请及时前往现场巡查确认！




            }
        }


    }


    public Map<String,String> listOrigin(){

        Map<String,String> eventOriginalMap = new HashMap<>();
        //查询字典
        Result<List<Dict>> result = sysDictProvider.getList(VariableConstants.EventOriginCode);
        if(result.isSuccess()) {
            Map<String, Dict>  dictMap= result.getData().stream()
                    .filter(s -> s.getDictKey() != null && !"".equals(s.getDictKey()))
                    .collect(Collectors.toMap(Dict::getDictKey, each -> each, (value1, value2) -> value1));
            dictMap.keySet().forEach(key->{
                eventOriginalMap.put(key,dictMap.get(key).getDictValue());
            });

        }

        return eventOriginalMap;
    }
}