package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lup.event.bill.dto.ExportFileDTO;
import com.mascj.lup.event.bill.dto.LupDataPushLogPagedDTO;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.entity.LupDataPushLog;
import com.mascj.lup.event.bill.vo.LupDataPushLogExcelVO;
import com.mascj.lup.event.bill.vo.LupDataPushLogPagedItemVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/8 19:57
 * @describe
 */
public interface LupDataPushLogMapper   extends BaseMapper<LupDataPushLog> {

    List<LupDataPushLogPagedItemVO> pagedDataPushLog(Page page, @Param("data") LupDataPushLogPagedDTO dataPushLogPagedDTO);
    List<LupDataPushLogExcelVO> listDataPushLog(@Param("data") ExportFileDTO dataPushLogPagedDTO);
}
