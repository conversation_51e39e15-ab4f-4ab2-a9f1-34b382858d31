package com.mascj.lup.event.bill.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.enums.GenerateBillEnum;
import com.mascj.lup.event.bill.mapper.LupDataMapper;
import com.mascj.lup.event.bill.service.ILupDataService;
import com.mascj.lup.event.bill.vo.*;

import com.mascj.platform.system.entity.Dict;
import com.mascj.platform.system.feign.ISysDictProvider;
import com.mascj.support.file.api.feign.IFileTransferProvider;
import com.mascj.support.file.api.vo.FileTransferReqVO;
import com.mascj.support.file.api.vo.FileTransferResVO;
import io.jsonwebtoken.lang.Assert;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.TextStyle;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mascj.kernel.common.util.LocaleMessageUtil.getMessage;

/**
 * <p>
 * 事件数据 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
@Slf4j
@AllArgsConstructor
public class LupDataServiceImpl extends ServiceImpl<LupDataMapper, LupData> implements ILupDataService {

    private final ISysDictProvider dictProvider;
    private final IFileTransferProvider fileTransferProvider;

    @Override
    public List<LupData> listByOriginalDataLogo(String originalDataLogo) {
        List<LupData> dataList = list(Wrappers.<LupData>lambdaQuery()
                .eq(LupData::getDeleted,0).eq(LupData::getOriginalDataLogo,originalDataLogo)
        );
        return dataList;
    }

    @Override
    public List<EventDataBaseInfoVO> listAllEventData(){
        List<EventDataBaseInfoVO> list =baseMapper.listAllEventData();
        list.forEach(dataVO->{
            if(dataVO.getGridName().equals(EventTipKey.UnknownAreaName)){
                dataVO.setGridName(getMessage(EventTipKey.UnknownAreaName));
            }
        });
        return list;
    }

    @Override
    public IPage<EventDataTaskVO> pageByTask(IPage<EventDataTaskVO> page, EventDataTaskQueryDTO eventDataTaskQueryDTO){
        page.setRecords(baseMapper.pageByTask(page,eventDataTaskQueryDTO));


        return page;
    }
    @Override
    public IPage<EventDataVO> pageEventData(IPage<EventDataVO> page, EventDataQueryDTO eventDataQueryDTO) {

        page.setRecords(baseMapper.pageEventData(page,eventDataQueryDTO));


        return page;
    }

    @Override
    public IPage<EventDataVO> pageEventDataForLand(IPage<EventDataVO> page, EventDataForLandQueryDTO eventDataQueryDTO) {

        page.setRecords(baseMapper.pageEventDataForLand(page,eventDataQueryDTO));


        return page;
    }

    @Override
    public EventDataVO detail(Long billId) {
        return baseMapper.detail(billId);
    }

    @Override
    public List<CountEventDataVO> countByArea() {
        return baseMapper.countByArea();
    }

    @Override
    public List<CountEventDataVO> countByLabel() {
        return baseMapper.countByLabel();
    }
    @Override
    public List<CountEventDataVO> countByLabelOnDay() {
        return baseMapper.countByLabelOnDay();
    }

    @Override
    public CountEventDataVO countByOrigin(EventDataCountQueryDTO eventDataCountQueryDTO) {

        //数据来源类型：1 飞控AI提取   2 飞控人工提报 3 比对系统
        CountEventDataVO eventDataVO = new CountEventDataVO();

        CountEventDataByTimeVO week = new CountEventDataByTimeVO();
        eventDataVO.setWeekCount(week);

        //计算本周
        buildEventDataCountQueryDTO(eventDataCountQueryDTO);

        List<CountEventDataVO> weekCount = baseMapper.countByOriginByDate(eventDataCountQueryDTO);

        Map<String, List<CountEventDataVO>> weekGroupMap = weekCount.stream().collect(Collectors.groupingBy(CountEventDataVO::getName));

        int sumWeekCount = 0;
        for (CountEventDataVO countEventDataVO : weekCount) {
            sumWeekCount = sumWeekCount + countEventDataVO.getCount();
        }
        week.setTotal(sumWeekCount);
        week.setXLabel(eventDataCountQueryDTO.getLabelList());

        List<CountEventDataVO> countEventDataVOList = weekGroupMap.get("1");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataVO::getBillDate));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getWeekDayKeyList().forEach(weekDay->{

                if(data.get(weekDay) != null){
                    int m = 0;
                    for (CountEventDataVO countEventDataVO : data.get(weekDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });
            week.setAiCount(tmp);
        }else {
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            week.setAiCount(tmp);
        }

        countEventDataVOList = weekGroupMap.get("2");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataVO::getBillDate));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getWeekDayKeyList().forEach(weekDay->{
                if(data.get(weekDay) != null){
                    int m = 0;
                    for (CountEventDataVO countEventDataVO : data.get(weekDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });

            week.setHumanReportCount(tmp);
        }else {

            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            week.setHumanReportCount(tmp);
        }
        countEventDataVOList = weekGroupMap.get("3");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataVO::getBillDate));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getWeekDayKeyList().forEach(weekDay->{
                if(data.get(weekDay) != null){
                    int m = 0;
                    for (CountEventDataVO countEventDataVO : data.get(weekDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });
            week.setCompareCount(tmp);
        }else {
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            week.setCompareCount(tmp);
        }

        //计算本月
        getMonthDayList(eventDataCountQueryDTO);
        CountEventDataByTimeVO month = new CountEventDataByTimeVO();
        eventDataVO.setMonthCount(month);

        List<CountEventDataVO> monthCount = baseMapper.countByOriginByMonth(eventDataCountQueryDTO);

        Map<String, List<CountEventDataVO>> monthGroupMap = monthCount.stream().collect(Collectors.groupingBy(CountEventDataVO::getName));

        int sumMonthCount = 0;
        for (CountEventDataVO countEventDataVO : monthCount) {
            sumMonthCount = sumMonthCount + countEventDataVO.getCount();
        }
        month.setTotal(sumMonthCount);
        month.setXLabel(eventDataCountQueryDTO.getLabelList());


        countEventDataVOList = monthGroupMap.get("1");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataVO::getBillDate));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getMonthDayKeyList().forEach(monthDay->{
                if(data.get(monthDay) != null){
                    int m = 0;
                    for (CountEventDataVO countEventDataVO : data.get(monthDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });
            month.setAiCount(tmp);
        }else {
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            month.setAiCount(tmp);
        }

        countEventDataVOList = monthGroupMap.get("2");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataVO::getBillDate));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getMonthDayKeyList().forEach(monthDay->{
                if(data.get(monthDay) != null){
                    int m = 0;
                    for (CountEventDataVO countEventDataVO : data.get(monthDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });
            month.setHumanReportCount(tmp);
        }else {
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            month.setHumanReportCount(tmp);
        }

        countEventDataVOList = monthGroupMap.get("3");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataVO::getBillDate));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getMonthDayKeyList().forEach(monthDay->{
                if(data.get(monthDay) != null){
                    int m = 0;
                    for (CountEventDataVO countEventDataVO : data.get(monthDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });
            month.setCompareCount(tmp);
        }else {
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            month.setCompareCount(tmp);
        }

        //计算本年
        getMonthList(eventDataCountQueryDTO);
        eventDataCountQueryDTO.setBillYear(
                Integer.parseInt(new SimpleDateFormat("yyyy").format(new Date()))
                );
        eventDataCountQueryDTO.setDuringStartTime(null);
        eventDataCountQueryDTO.setDuringEndTime(null);
        List<CountEventDataVO> yearCount = baseMapper.countByOriginByYear(eventDataCountQueryDTO);
        CountEventDataByTimeVO year = new CountEventDataByTimeVO();
        eventDataVO.setYearCount(year);

        Map<String, List<CountEventDataVO>> yearGroupMap = yearCount.stream().collect(Collectors.groupingBy(CountEventDataVO::getName));
        int sumYearCount = 0;
        for (CountEventDataVO countEventDataVO : yearCount) {
            sumYearCount = sumYearCount + countEventDataVO.getCount();
        }
        year.setTotal(sumYearCount);
        year.setXLabel(eventDataCountQueryDTO.getLabelList());

        countEventDataVOList = yearGroupMap.get("1");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataVO::getBillMonth));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getMonthKeyList().forEach(monthDay->{
                if(data.get(monthDay) != null){
                    int m = 0;
                    for (CountEventDataVO countEventDataVO : data.get(monthDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });
            year.setAiCount(tmp);
        }else {
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            year.setAiCount(tmp);
        }
//        year.setAiCount(yearGroupMap.get("1").stream().map(CountEventDataVO::getCount).collect(Collectors.toList()));

        countEventDataVOList = yearGroupMap.get("2");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataVO::getBillMonth));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getMonthKeyList().forEach(monthDay->{
                if(data.get(monthDay) != null){
                    int m = 0;
                    for (CountEventDataVO countEventDataVO : data.get(monthDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });
            year.setHumanReportCount(tmp);
        }else {
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            year.setHumanReportCount(tmp);
        }
//        year.setHumanReportCount(yearGroupMap.get("2").stream().map(CountEventDataVO::getCount).collect(Collectors.toList()));

        countEventDataVOList = yearGroupMap.get("3");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataVO::getBillMonth));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getMonthKeyList().forEach(monthDay->{
                if(data.get(monthDay) != null){
                    int m = 0;
                    for (CountEventDataVO countEventDataVO : data.get(monthDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });
            year.setCompareCount(tmp);
        }else {
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            year.setCompareCount(tmp);
        }
//        year.setCompareCount(yearGroupMap.get("3").stream().map(CountEventDataVO::getCount).collect(Collectors.toList()));



        return eventDataVO;
    }

    @Override
    public CountEventDataOriginGroupVO countByOriginDataFetchWay(EventDataCountQueryDTO eventDataCountQueryDTO) {

        Result<List<Dict>> dictRes = dictProvider.getList("eventOrigin");

        Assert.notNull(dictRes.getData(), LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneQuery, Arrays.asList(EventTipKey.NoneTip)));

        dictRes.getData().sort(Comparator.comparing(Dict::getSort).thenComparing(Dict::getId));

        CountEventDataOriginGroupVO countEventDataOriginGroupVO = new CountEventDataOriginGroupVO();
        //本周
        //计算本周
        buildEventDataCountQueryDTO(eventDataCountQueryDTO);
        List<CountEventDataOriginVO> weekCount = baseMapper.countByOriginDataFetchWayByDate(eventDataCountQueryDTO);
        Map<String,CountEventDataOriginVO> weekCountMap = weekCount.stream() .collect(
                Collectors.toMap(CountEventDataOriginVO::getName, Function.identity(), (u1, u2) -> u1)
        );

        countEventDataOriginGroupVO.setOriginDataFetchWayListForWeek(new ArrayList<>());

        dictRes.getData().forEach(e->{
            if(e.getDictKey() != null){
                CountEventDataOriginVO vo =  weekCountMap.get(e.getDictKey());
                if(vo == null){
                    vo = new CountEventDataOriginVO();
                    vo.setCount(0);
                }
                vo.setName(e.getDictValue());

                if(e.getDictKey()!=null && !"".equals(e.getDictKey()))
                    vo.setCode(Integer.parseInt(e.getDictKey()));

                countEventDataOriginGroupVO.getOriginDataFetchWayListForWeek().add(vo);

            }
        });


        eventDataCountQueryDTO.setDuringStartTime(null);
        eventDataCountQueryDTO.setDuringEndTime(null);
        LocalDate currentDate = LocalDate.now();

        //本年
        countEventDataOriginGroupVO.setOriginDataFetchWayListForYear(new ArrayList<>());
        int currentYear = currentDate.getYear();
        eventDataCountQueryDTO.setBillYear(currentYear);
        List<CountEventDataOriginVO> yearCount = baseMapper.countByOriginDataFetchWayByDate(eventDataCountQueryDTO);

        Map<String,CountEventDataOriginVO> yearCountMap = yearCount.stream() .collect(
                Collectors.toMap(CountEventDataOriginVO::getName, Function.identity(), (u1, u2) -> u1)
        );

        dictRes.getData().forEach(e->{
            if(e.getDictKey() != null){
                CountEventDataOriginVO vo =  yearCountMap.get(e.getDictKey());
                if(vo == null){
                    vo = new CountEventDataOriginVO();
                    vo.setCount(0);
                }
                vo.setName(e.getDictValue());
                if(e.getDictKey()!=null && !"".equals(e.getDictKey()))
                    vo.setCode(Integer.parseInt(e.getDictKey()));
                countEventDataOriginGroupVO.getOriginDataFetchWayListForYear().add(vo);

            }
        });


        countEventDataOriginGroupVO.setOriginDataFetchWayListForMonth(new ArrayList<>());
        //本月
        int currentMonth = currentDate.getMonthValue();
        eventDataCountQueryDTO.setBillMonth(String.format("%02d", currentMonth));
        List<CountEventDataOriginVO> monthCount = baseMapper.countByOriginDataFetchWayByDate(eventDataCountQueryDTO);

        Map<String,CountEventDataOriginVO> monthCountMap = monthCount.stream() .collect(
                Collectors.toMap(CountEventDataOriginVO::getName, Function.identity(), (u1, u2) -> u1)
        );

        dictRes.getData().forEach(e->{
            if(e.getDictKey() != null){
                CountEventDataOriginVO vo =  monthCountMap.get(e.getDictKey());
                if(vo == null){
                    vo = new CountEventDataOriginVO();
                    vo.setCount(0);

                }
                if(e.getDictKey()!=null && !"".equals(e.getDictKey()))
                    vo.setCode(Integer.parseInt(e.getDictKey()));
                vo.setName(e.getDictValue());
                countEventDataOriginGroupVO.getOriginDataFetchWayListForMonth().add(vo);

            }
        });
        return countEventDataOriginGroupVO;
    }

    @Override
    public CountEventDataDealResultVO countByDealResult(EventDataCountQueryDTO eventDataCountQueryDTO) {
        //数据处理结果： 0否 未处理完 1 已处理完 或者 已归档
        CountEventDataDealResultVO eventDataVO = new CountEventDataDealResultVO();

        CountEventDataByDealResultTimeVO week = new CountEventDataByDealResultTimeVO();
        eventDataVO.setWeekCount(week);

        //计算本周
        buildEventDataCountQueryDTO(eventDataCountQueryDTO);

        List<CountEventDataDealResultVO> weekCount = baseMapper.countByDealResultByDate(eventDataCountQueryDTO);

        Map<String, List<CountEventDataDealResultVO>> weekGroupMap = weekCount.stream().collect(Collectors.groupingBy(CountEventDataDealResultVO::getName));

        int sumWeekCount = 0;
        for (CountEventDataDealResultVO countEventDataVO : weekCount) {
            sumWeekCount = sumWeekCount + countEventDataVO.getCount();
        }
        week.setTotal(sumWeekCount);
        week.setXLabel(eventDataCountQueryDTO.getLabelList());

        List<CountEventDataDealResultVO> countEventDataVOList = weekGroupMap.get("0");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataDealResultVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataDealResultVO::getBillDate));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getWeekDayKeyList().forEach(weekDay->{

                if(data.get(weekDay) != null){
                    int m = 0;
                    for (CountEventDataDealResultVO countEventDataVO : data.get(weekDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });
            week.setUnDealDoneCount(tmp);
        }else {
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            week.setUnDealDoneCount(tmp);
        }

        countEventDataVOList = weekGroupMap.get("1");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataDealResultVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataDealResultVO::getBillDate));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getWeekDayKeyList().forEach(weekDay->{
                if(data.get(weekDay) != null){
                    int m = 0;
                    for (CountEventDataDealResultVO countEventDataVO : data.get(weekDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });

            week.setDealDoneCount(tmp);
        }else {

            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            week.setDealDoneCount(tmp);
        }

        //计算本月
        getMonthDayList(eventDataCountQueryDTO);
        CountEventDataByDealResultTimeVO month = new CountEventDataByDealResultTimeVO();
        eventDataVO.setMonthCount(month);

        List<CountEventDataDealResultVO> monthCount = baseMapper.countByDealResultByMonth(eventDataCountQueryDTO);

        Map<String, List<CountEventDataDealResultVO>> monthGroupMap = monthCount.stream().collect(Collectors.groupingBy(CountEventDataDealResultVO::getName));

        int sumMonthCount = 0;
        for (CountEventDataDealResultVO countEventDataVO : monthCount) {
            sumMonthCount = sumMonthCount + countEventDataVO.getCount();
        }
        month.setTotal(sumMonthCount);
        month.setXLabel(eventDataCountQueryDTO.getLabelList());


        countEventDataVOList = monthGroupMap.get("0");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataDealResultVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataDealResultVO::getBillDate));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getMonthDayKeyList().forEach(monthDay->{
                if(data.get(monthDay) != null){
                    int m = 0;
                    for (CountEventDataDealResultVO countEventDataVO : data.get(monthDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });
            month.setUnDealDoneCount(tmp);
        }else {
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            month.setUnDealDoneCount(tmp);
        }

        countEventDataVOList = monthGroupMap.get("1");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataDealResultVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataDealResultVO::getBillDate));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getMonthDayKeyList().forEach(monthDay->{
                if(data.get(monthDay) != null){
                    int m = 0;
                    for (CountEventDataDealResultVO countEventDataVO : data.get(monthDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });
            month.setDealDoneCount(tmp);
        }else {
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            month.setDealDoneCount(tmp);
        }

        //计算本年
        getMonthList(eventDataCountQueryDTO);
        eventDataCountQueryDTO.setBillYear(
                Integer.parseInt(new SimpleDateFormat("yyyy").format(new Date()))
        );
        eventDataCountQueryDTO.setDuringStartTime(null);
        eventDataCountQueryDTO.setDuringEndTime(null);
        List<CountEventDataDealResultVO> yearCount = baseMapper.countByDealResultByYear(eventDataCountQueryDTO);
        CountEventDataByDealResultTimeVO year = new CountEventDataByDealResultTimeVO();
        eventDataVO.setYearCount(year);

        Map<String, List<CountEventDataDealResultVO>> yearGroupMap = yearCount.stream().collect(Collectors.groupingBy(CountEventDataDealResultVO::getName));
        int sumYearCount = 0;
        for (CountEventDataDealResultVO countEventDataVO : yearCount) {
            sumYearCount = sumYearCount + countEventDataVO.getCount();
        }
        year.setTotal(sumYearCount);
        year.setXLabel(eventDataCountQueryDTO.getLabelList());

        countEventDataVOList = yearGroupMap.get("0");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataDealResultVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataDealResultVO::getBillMonth));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getMonthKeyList().forEach(monthDay->{
                if(data.get(monthDay) != null){
                    int m = 0;
                    for (CountEventDataDealResultVO countEventDataVO : data.get(monthDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });
            year.setUnDealDoneCount(tmp);
        }else {
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            year.setUnDealDoneCount(tmp);
        }
//        year.setAiCount(yearGroupMap.get("1").stream().map(CountEventDataVO::getCount).collect(Collectors.toList()));

        countEventDataVOList = yearGroupMap.get("1");
        if(countEventDataVOList!=null) {
            Map<String,List<CountEventDataDealResultVO>> data =  countEventDataVOList.stream().collect(Collectors.groupingBy(CountEventDataDealResultVO::getBillMonth));
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getMonthKeyList().forEach(monthDay->{
                if(data.get(monthDay) != null){
                    int m = 0;
                    for (CountEventDataDealResultVO countEventDataVO : data.get(monthDay)) {
                        m = m + countEventDataVO.getCount();
                    }
                    tmp.add(m);
                }else {
                    tmp.add(0);
                }
            });
            year.setDealDoneCount(tmp);
        }else {
            List<Integer> tmp = new ArrayList<>();
            eventDataCountQueryDTO.getLabelList().forEach(e->{
                tmp.add(0);
            });
            year.setDealDoneCount(tmp);
        }

        return eventDataVO;
    }

    private void buildEventDataCountQueryDTO(EventDataCountQueryDTO eventDataCountQueryDTO){
        getWeekDayList(eventDataCountQueryDTO);
    }
    public static Map<String,String> getWeekDayList(EventDataCountQueryDTO eventDataCountQueryDTO){

        eventDataCountQueryDTO.setWeekDayKeyList(new ArrayList<>());

        Map<String,String> map = new HashMap<String,String>();
        Calendar calendarWeek = Calendar.getInstance();
        calendarWeek.setTime(new Date());

        calendarWeek.add(Calendar.WEEK_OF_MONTH,0);
        calendarWeek.setFirstDayOfWeek(Calendar.MONDAY);
        calendarWeek.set(Calendar.DAY_OF_WEEK,Calendar.MONDAY);
        String day = DateUtil.format(calendarWeek.getTime(),"yyyy-MM-dd");
        map.put(day,"周一");
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateMonday));
        eventDataCountQueryDTO.getWeekDayKeyList().add(day);
        eventDataCountQueryDTO.setDuringStartTime(day);

        calendarWeek.set(Calendar.DAY_OF_WEEK,Calendar.TUESDAY);
        day = DateUtil.format(calendarWeek.getTime(),"yyyy-MM-dd");
        eventDataCountQueryDTO.getWeekDayKeyList().add(day);
        map.put(day,"周二");
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateTuesday));

        calendarWeek.set(Calendar.DAY_OF_WEEK,Calendar.WEDNESDAY);
        day = DateUtil.format(calendarWeek.getTime(),"yyyy-MM-dd");
        eventDataCountQueryDTO.getWeekDayKeyList().add(day);
        map.put(day,"周三");
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateWednesday));

        calendarWeek.set(Calendar.DAY_OF_WEEK,Calendar.THURSDAY);
        day = DateUtil.format(calendarWeek.getTime(),"yyyy-MM-dd");
        eventDataCountQueryDTO.getWeekDayKeyList().add(day);
        map.put(day,"周四");
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateThursday));

        calendarWeek.set(Calendar.DAY_OF_WEEK,Calendar.FRIDAY);
        day = DateUtil.format(calendarWeek.getTime(),"yyyy-MM-dd");
        eventDataCountQueryDTO.getWeekDayKeyList().add(day);
        map.put(day,"周五");
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateFriday));

        calendarWeek.set(Calendar.DAY_OF_WEEK,Calendar.SATURDAY);
        day = DateUtil.format(calendarWeek.getTime(),"yyyy-MM-dd");
        eventDataCountQueryDTO.getWeekDayKeyList().add(day);
        map.put(day,"周六");
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateSaturday));

        calendarWeek.set(Calendar.DAY_OF_WEEK,Calendar.SUNDAY);
        String endDay =  DateUtil.format(calendarWeek.getTime(),"yyyy-MM-dd");
        eventDataCountQueryDTO.getWeekDayKeyList().add(endDay);
        map.put(endDay,"周日");
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateSunday));
        eventDataCountQueryDTO.setDuringEndTime(endDay);
        eventDataCountQueryDTO.setWeekDay(map);
        return map;
    }

    private static void getMonthDayList(EventDataCountQueryDTO eventDataCountQueryDTO) {
        eventDataCountQueryDTO.setLabelList(new ArrayList<>());
        eventDataCountQueryDTO.setMonthDayKeyList(new ArrayList<>());
        Map<String,String> map = new HashMap<String,String>();
        eventDataCountQueryDTO.setMonthDay(map);
        LocalDate currentDate = LocalDate.now();
        int currentMonth = currentDate.getMonthValue();
        int currentYear = currentDate.getYear();

        YearMonth yearMonthObject = YearMonth.of(currentYear, currentMonth);
        int daysInMonth = yearMonthObject.lengthOfMonth();

        for(int i=1;i<=daysInMonth;i++){
            String date = currentYear+"-"+String.format("%02d", currentMonth) +"-"+ String.format("%02d", i);
            String date1 = String.format("%02d", currentMonth) +"-"+ String.format("%02d", i);
            map.put(date,date1);
            eventDataCountQueryDTO.getLabelList().add(date1);
            eventDataCountQueryDTO.getMonthDayKeyList().add(date);
        }

        eventDataCountQueryDTO.setDuringStartTime(eventDataCountQueryDTO.getMonthDayKeyList().get(0));
        eventDataCountQueryDTO.setDuringEndTime(eventDataCountQueryDTO.getMonthDayKeyList().get(eventDataCountQueryDTO.getMonthDayKeyList().size()-1));
        System.out.println("当月的天数：" + daysInMonth);
    }

    private static void getMonthList(EventDataCountQueryDTO eventDataCountQueryDTO){

        eventDataCountQueryDTO.setLabelList(new ArrayList<>());
        eventDataCountQueryDTO.setMonthKeyList(new ArrayList<>());
        for(int i=1;i<=12;i++){
            eventDataCountQueryDTO.getMonthKeyList().add(String.format("%02d", i));
        }

        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateJanuary));
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateFebruary));
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateMarch));
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateApril));
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateMay));
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateJune));
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateJuly));
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateAugust));
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateSeptember));
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateOctober));
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateNovember));
        eventDataCountQueryDTO.getLabelList().add(LocaleMessageUtil.getMessage(EventTipKey.DateDecember));


    }

    @Override
    public List<FlyTaskReportBillCountVO> queryFlyTaskReportVO(FlyTaskReportDTO flyTaskReportDTO) {
        return baseMapper.queryFlyTaskReportVO(flyTaskReportDTO);
    }

    @Override
    public Boolean dealOldDataPic(String endDate) {

        //查询历史所有素材的事件 针对事件的图片转存成处置系统的图片保存;
        List<LupData> dataList =  baseMapper.dealOldDataPic(endDate);
        log.info("dataList.size="+dataList.size());

        Integer c = 1;
        for (LupData item : dataList) {

            LmContextHolder.setTenantId(item.getTenantId().toString());

            if(ObjectUtil.isNotEmpty(item.getExtraData())) {
                DataPicDTO dataPicDTO = JSONUtil.toBean(item.getExtraData(), DataPicDTO.class);

                if (ObjectUtil.isNotEmpty(dataPicDTO.getEventPictureUrl())) {
                    dataPicDTO.setEventPictureUrl(transferPicFile(item.getTenantId().toString(), dataPicDTO.getEventPictureUrl()));
                }

                if (ObjectUtil.isNotEmpty(dataPicDTO.getEventPictureUrlList())) {
                    List<String> eventPictureUrlList = new ArrayList<>();

                    for (String s : dataPicDTO.getEventPictureUrlList()) {
                        eventPictureUrlList.add(transferPicFile(item.getTenantId().toString(), s));
                    }
                    dataPicDTO.setEventPictureUrlList(eventPictureUrlList);
                }

                item.setExtraData(JSONUtil.toJsonStr(dataPicDTO));
                updateById(item);

            }
            log.info("dataList.current="+c+"/"+dataList.size());
            c++;
        }
        log.info("dataList.deal---->end");

        return Boolean.TRUE;
    }
    private String transferPicFile(String tenantId,String picUrl){

        FileTransferReqVO fileTransferReqVO = new FileTransferReqVO();
        fileTransferReqVO.setSourcePath(picUrl);
        fileTransferReqVO.setTentantId(tenantId);
        fileTransferReqVO.setModule(VariableConstants.lupHandleSystemTag);
        Result<FileTransferResVO> fileTransferResVOResult = fileTransferProvider.fileTransfer(fileTransferReqVO);

        return fileTransferResVOResult.getData().getTargetPath();
    }


    @Override
    public List<BillQueryCountVO> billQueryCount(BillQueryCountDTO billQueryCountDTO) {
        return baseMapper.billQueryCount(billQueryCountDTO);
    }

    @Override
    public List<BillQueryListItemVO> billQueryList(BillQueryCountDTO billQueryCountDTO) {
        return baseMapper.billQueryList(billQueryCountDTO);
    }
}
