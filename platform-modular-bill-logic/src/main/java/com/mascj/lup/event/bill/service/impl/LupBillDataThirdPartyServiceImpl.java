package com.mascj.lup.event.bill.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.lup.event.bill.dto.QueryBillSearchDTO;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.mapper.LupBillMapper;
import com.mascj.lup.event.bill.service.ILupBillDataThirdPartyService;
import com.mascj.lup.event.bill.service.ILupBillService;
import com.mascj.lup.event.bill.vo.BillSearchVO;
import com.mascj.lup.event.bill.vo.QueryBillDataEntityVO;
import com.mascj.lup.event.bill.vo.QueryBillDataExtraDataEntityVO;
import com.mascj.lup.event.bill.vo.QueryBillDataVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/29 14:13
 * @describe
 */
@Service
@AllArgsConstructor
public class LupBillDataThirdPartyServiceImpl extends ServiceImpl<LupBillMapper, LupBill> implements ILupBillDataThirdPartyService {

    @Override
    public IPage<QueryBillDataVO> pagedBill(Page page, QueryBillSearchDTO billSearchDTO) {

        List<QueryBillDataEntityVO> records = baseMapper.pagedThirdParty(page,billSearchDTO);

        List<QueryBillDataVO> list = new ArrayList<>();

        records.forEach(item->{

            QueryBillDataVO billDataVO = BeanUtil.copyProperties(item,QueryBillDataVO.class);
            billDataVO.setPicUrlList(new ArrayList<>());

            if(ObjectUtil.isNotEmpty(item.getExtraData())){

                QueryBillDataExtraDataEntityVO dataExtraDataEntityVO = JSONUtil.toBean(item.getExtraData(), QueryBillDataExtraDataEntityVO.class);

                if(ObjectUtil.isAllNotEmpty(dataExtraDataEntityVO.getEventPictureUrlList())){
                    billDataVO.getPicUrlList().addAll(dataExtraDataEntityVO.getEventPictureUrlList());
                }

                if(ObjectUtil.isNotEmpty(dataExtraDataEntityVO.getEventPictureUrl())){
                    if(!billDataVO.getPicUrlList().contains(dataExtraDataEntityVO.getEventPictureUrl()))
                    billDataVO.getPicUrlList().add(dataExtraDataEntityVO.getEventPictureUrl());
                }
            }

            list.add(billDataVO);

        });

        return page.setRecords(list);
    }

}
