package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工单数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface LupBillMapper extends BaseMapper<LupBill> {

    List<BillCountVO> countBill(@Param(value = "billDTO") ListUnitDTO billDTO);

    List<BillCountVO> countBillForPending(@Param(value = "billDTO") ListUnitDTO billDTO);



    List<LupBillCountVo> countBillByGrid(@Param(value = "billDTO") BillDTO billDTO);

    List<LupBillCountByFlowStateVo> countBillByFlowState(@Param(value = "labelIdListPerm") List<Long> userIdList, @Param(value = "gridIdList") List<Long> gridIdList,@Param(value = "flowStateList") List<Integer> flowStateList);

    List<BillSearchVO> pagedBill(IPage page, @Param("billDTO") BillSearchDTO labelSearchDTO, @Param("projectId") Long projectId, @Param("tenantId")String tenantId);

    List<BillSearchExcelVO> listAllBill(@Param("billDTO") ExportFileDTO labelSearchDTO, @Param("projectId") Long projectId, @Param("tenantId")String tenantId);
    List<BillSearchExcelYXQVO> listYXQAllBill(@Param("billDTO") ExportFileDTO labelSearchDTO, @Param("projectId") Long projectId, @Param("tenantId")String tenantId);


    LupBillCountVo  countFlowStateBill(@Param("billDTO") BillSearchDTO labelSearchDTO, @Param("projectId") Long projectId);
    List<BillDataVO> listBill(@Param("billDTO") ListUnitDTO labelSearchDTO, @Param("projectId") Long projectId);
    List<BillDataVO> listBillOnlyLocation(@Param("billDTO") ListUnitDTO labelSearchDTO, @Param("projectId") Long projectId);


    BillDetailVO detailByDataId(@Param(value = "dataId")Long billId);
    List<BillDetailVO> listBillDetailVOByQueryBillPictureInfo(IPage page,@Param(value = "data")QueryBillPictureInfoDTO queryBillPictureInfoDTO);
    BillDetailVO detailById(@Param(value = "billId")Long billId);



    @InterceptorIgnore(tenantLine = "true")
    LupBill detailNoTenantIdById(@Param(value = "billId")Long billId);

    List<QueryBillDataEntityVO> pagedThirdParty(IPage page,@Param("data")QueryBillSearchDTO billSearchDTO);

    List<BillUserLabelVO> listBillUserLabelVO(@Param(value = "flowId")Long flowId);

    BillUserLabelVO listBillUserLabelOneVO(@Param(value = "flowId")Long flowId);

    @InterceptorIgnore(tenantLine = "true")
    List<BillUserLabelVO> listBillLabelAndDataIdOneVO(@Param(value = "flowId")Long flowId);
}
