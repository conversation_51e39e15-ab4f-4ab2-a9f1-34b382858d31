package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.entity.LupSource;
import com.mascj.lup.event.bill.vo.SourceTileVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 事件资源记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface LupSourceMapper extends BaseMapper<LupSource> {

    List<LupSource> listSourceTile(@Param("billDTO") BillDTO billDTO);
}
