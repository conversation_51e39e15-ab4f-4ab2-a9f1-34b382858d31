package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.lup.event.bill.dto.ExportFileDTO;
import com.mascj.lup.event.bill.dto.ReportSearchDTO;
import com.mascj.lup.event.bill.entity.DataReport;
import com.mascj.lup.event.bill.vo.DataReportDetailsVO;
import com.mascj.lup.event.bill.vo.DataReportSetVO;
import com.mascj.lup.event.bill.vo.DataReportVO;


import java.util.List;

public interface IDataReportRepository extends IService<DataReport> {

    IPage<DataReportVO> listPage(ReportSearchDTO search);

    Boolean saveReport(ExportFileDTO exportFileDto);
    Boolean setReport(DataReportSetVO aepReportSetVO);

    DataReportDetailsVO getDetails(String id);

    Boolean delReport(List<Long> toLongList);

    /**
     * 生成进度更新方法
     */

    Boolean updateProgress(DataReport aepReport);

    Boolean updateStatus(Long id,Integer status);
}
