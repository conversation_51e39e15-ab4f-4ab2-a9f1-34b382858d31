package com.mascj.lup.event.bill.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/3 16:22
 * @describe
 */
public class GeoLocation {
    public static ResponseEntity<String> locationToAddress (String key ,String x, String y) {
        String url = "http://api.tianditu.gov.cn/geocoder?type=geocode&tk="+ key +"&postStr={postStr}";

        RestTemplate restTemplate = new RestTemplate();
        Map<String, Object> hashMap = new HashMap<>();
        if (StrUtil.isNotBlank(x)) {
            hashMap.put("lon", x);
        }
        if (StrUtil.isNotBlank(y)) {
            hashMap.put("lat", y);
        }
        hashMap.put("ver", "1");

        HttpHeaders headers = new HttpHeaders();
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********");

        HttpEntity<String> requestEntity = new HttpEntity<>(headers);
        try{
            ResponseEntity<String> obj =  restTemplate.postForEntity(url, requestEntity, String.class, JSONObject.toJSONString(hashMap));
            return  obj;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }
}
