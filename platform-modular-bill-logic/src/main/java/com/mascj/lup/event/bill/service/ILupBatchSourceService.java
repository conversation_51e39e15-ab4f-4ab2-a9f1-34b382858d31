package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.lup.event.bill.entity.LupBatch;
import com.mascj.lup.event.bill.entity.LupBatchSource;
import com.mascj.lup.event.bill.entity.LupSource;

import java.util.List;

/**
 * <p>
 * 事件数据资源关联记录表 事件和瓦片图片的关联记录 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface ILupBatchSourceService extends IService<LupBatchSource> {

    List<LupSource> listByBatch(Long batchId);
}
