package com.mascj.lup.event.bill.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.lup.event.bill.entity.LupBatch;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.mapper.LupBatchMapper;
import com.mascj.lup.event.bill.mapper.LupDataMapper;
import com.mascj.lup.event.bill.service.ILupBatchService;
import com.mascj.lup.event.bill.service.ILupDataService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 事件数据 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
public class LupBatchServiceImpl extends ServiceImpl<LupBatchMapper, LupBatch> implements ILupBatchService {

}
