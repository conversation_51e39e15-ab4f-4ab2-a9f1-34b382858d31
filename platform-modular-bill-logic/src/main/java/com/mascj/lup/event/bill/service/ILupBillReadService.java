package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.LupBillRead;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.vo.*;
import com.mascj.support.workflow.entity.XlmTaskInfo;

import java.util.List;

/**
 * <p>
 * 工单数据表 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface ILupBillReadService extends IService<LupBillRead> {

    BillReadStateCounterVO countBillUnReadCount(BillReadStateCounterDTO readStateCounterDTO);
    Boolean saveBillReadState(BillReadStateDTO billReadStateDTO);

    Integer countByUserId(BillReadStateDTO billReadStateDTO);
}
