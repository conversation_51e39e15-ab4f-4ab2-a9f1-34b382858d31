package com.mascj.lup.event.bill.support;

/**
 * <AUTHOR>
 * @date 2024/11/1 08:46
 * @describe
 */
import cn.hutool.json.JSONUtil;
import com.mascj.lup.event.bill.dto.EventDataDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class AppRunner implements CommandLineRunner {

    @Autowired
    private MessageSender messageSender;

    @Override
    public void run(String... args) throws Exception {
        EventDataDTO dataDTO = new EventDataDTO();
        dataDTO.setEventName("调试队列");
        messageSender.sendMessage(JSONUtil.toJsonStr(dataDTO));
    }
}