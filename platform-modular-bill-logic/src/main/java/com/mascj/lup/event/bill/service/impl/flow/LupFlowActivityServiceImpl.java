/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.mascj.lup.event.bill.service.impl.flow;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.$;
import com.mascj.kernel.common.util.DateUtil;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.common.util.StringUtil;
import com.mascj.kernel.tools.SetFunction;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.dto.FlowEntityTimeOutSaveDTO;
import com.mascj.lup.event.bill.entity.LupFlowActivity;
import com.mascj.lup.event.bill.entity.LupFlowEntity;
import com.mascj.lup.event.bill.exceptions.OperationFailedException;
import com.mascj.lup.event.bill.mapper.LupFlowEntityMapper;
import com.mascj.lup.event.bill.service.*;
import com.mascj.lup.event.bill.support.LupExecutionContext;
import com.mascj.lup.event.bill.vo.FlowMessageVO;
import com.mascj.lup.event.bill.mapper.LupFlowActivityMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.support.config.dto.XlmActivityState;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mascj.kernel.common.api.Result;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.Date;

/**
 * 流程活动节点 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Service
public class LupFlowActivityServiceImpl extends ServiceImpl<LupFlowActivityMapper, LupFlowActivity> implements ILupFlowActivityService {

    @Autowired
    private LupExecutionContext lupExecutionContext;
    @Autowired
    private LupFlowEntityMapper lupFlowEntityMapper;
    @Autowired
    private ILupFlowActivityService lupFlowActivityService;
    @Autowired
    private ILupFlowEntityService lupFlowEntityService;
    @Autowired
    private LupFlowActivityMapper lupFlowActivityMapper;

    @Autowired
    private IFlowEntityTimeOutService flowEntityTimeOutService;

    @Autowired
    private ILupFlowSmsService flowSmsService;
    @Autowired
    private ILupEventSmsGradeService lupEventSmsGradeService;


    @Override
    public Result taskEnded(FlowMessageVO message) {
        String processInstanceId = message.getProcessInstanceId();
        String currentActivityId = message.getCurrentActivityId();
        String currentTaskId = message.getCurrentTaskId();
        String tenantId = message.getTenantId();
        LmContextHolder.setTenantId(tenantId);
        LupFlowEntity lupFlowEntity = lupFlowEntityMapper.selectOne(new LambdaQueryWrapper<>(new LupFlowEntity())
                .eq(LupFlowEntity::getProcessInstanceId, processInstanceId));

        Assert.notNull(lupFlowEntity, LocaleMessageUtil.getMessage(EventTipKey.NoneProcessDefinitionId, Arrays.asList(EventTipKey.NoneTip)) + processInstanceId);


        XlmActivityState activityState = lupExecutionContext.resolveActivityState(lupFlowEntity.getBizCode(), lupFlowEntity.getProcessDefinitionId(), currentActivityId);
        Assert.notNull(activityState, $.format(
                LocaleMessageUtil.getMessage(EventTipKey.FailActivityState), lupFlowEntity.getProcessDefinitionId(), currentActivityId));

        LupFlowActivity activity = lupFlowActivityMapper.selectOne(new LambdaQueryWrapper<>(new LupFlowActivity())
                .eq(LupFlowActivity::getFlowId, lupFlowEntity.getId())
                .eq(LupFlowActivity::getProcessTaskId, currentTaskId));
        Assert.notNull(activity, $.format(LocaleMessageUtil.getMessage(EventTipKey.FailBillState), lupFlowEntity.getId(), currentActivityId));

        activity.setEndTime(DateUtil.toDateTime(new Date()));
//        activity.setDurationInMillis(message.getDurationInMillis());
        activity.setAssignee(Long.parseLong(message.getAssignee()));
        activity.setFromStr(message.getFormInfo());
        saveToDb( null, activity);
        return Result.success("");
    }

    @Override
    public Result taskStarted(FlowMessageVO message) {

        Integer currentFlowState;
        Integer approvalFlowState;

        String processInstanceId = message.getProcessInstanceId();
        String currentActivityId = message.getCurrentActivityId();
        String currentTaskId = message.getCurrentTaskId();
        String flowId = message.getFlowId();
        String tenantId = message.getTenantId();
        LupFlowEntity lupFlowEntity = null;
        if (StringUtil.isNotBlank(flowId)){
            lupFlowEntity = lupFlowEntityMapper.selectById(flowId);
        }else {
            lupFlowEntity = lupFlowEntityMapper.selectOne(new LambdaQueryWrapper<>(new LupFlowEntity())
                    .eq(LupFlowEntity::getProcessInstanceId, processInstanceId));
        }


        XlmActivityState activityState = lupExecutionContext.resolveActivityState(lupFlowEntity.getBizCode(), lupFlowEntity.getProcessDefinitionId(),
                currentActivityId);
//        Assert.notNull(activityState, $.format("获取XlmActivityState失败，processDefinitionId={},activityId={}",
//                lupFlowEntity.getProcessDefinitionId(), currentActivityId));

        lupFlowEntity.setProcessTaskId(processInstanceId);

        currentFlowState = activityState.getState();
        approvalFlowState = lupFlowEntity.getFlowState();

        lupFlowEntity.setFlowState(activityState.getState());
        lupFlowEntity.setProcessTaskId(currentTaskId);
        lupFlowEntity.setProcessDefinitionId(message.getProcessDefinitionId());
        if (activityState.getState() ==1){
            lupFlowEntity.setHandleType(null);
        }

        LupFlowActivity activity = lupFlowActivityMapper.selectOne(new LambdaQueryWrapper<>(new LupFlowActivity())
                .eq(LupFlowActivity::getFlowId, lupFlowEntity.getId())
                .eq(LupFlowActivity::getProcessTaskId, currentTaskId));

        if ($.isNull(activity)) {
            activity = SetFunction
                    .lambdaSet(LupFlowActivity::getFlowId, lupFlowEntity.getId())
                    .set(LupFlowActivity::getProcessInstanceId, lupFlowEntity.getProcessInstanceId())
                    .set(LupFlowActivity::getActivityId, currentActivityId)
                    .set(LupFlowActivity::getProcessTaskId, currentTaskId)
                    .set(LupFlowActivity::getName, activityState.getName())
                    .set(LupFlowActivity::getState, activityState.getState())
                    .set(LupFlowActivity::getStartTime, DateUtil.toDateTime(message.getCreateTaskTime()))
                    .set(LupFlowActivity::getTenantId, Long.parseLong(tenantId))
                    .getInstance();
        } else {
            activity = SetFunction
                    .forInstance(activity)
                    .set(LupFlowActivity::getActivityId, currentActivityId)
                    .set(LupFlowActivity::getName, activityState.getName())
                    .set(LupFlowActivity::getState, activityState.getState())
                    .set(LupFlowActivity::getStartTime, DateUtil.toDateTime(message.getCreateTaskTime()))
                    .set(LupFlowActivity::getTenantId, Long.parseLong(tenantId))
                    .getInstance();
        }
        LmContextHolder.setTenantId(tenantId);
        saveToDb(lupFlowEntity, activity);

        try{

            FlowEntityTimeOutSaveDTO flowEntityTimeOutSaveDTO = new FlowEntityTimeOutSaveDTO();

            flowEntityTimeOutSaveDTO.setProcessTaskId(currentTaskId);
            flowEntityTimeOutSaveDTO.setFlowEntityId(lupFlowEntity.getId());
            flowEntityTimeOutSaveDTO.setFlowState(lupFlowEntity.getFlowState());

            flowEntityTimeOutService.saveFlowEntityTimeOut(flowEntityTimeOutSaveDTO);


            if (currentFlowState == 1 || currentFlowState == 2) {
                lupEventSmsGradeService.smsGrade(lupFlowEntity.getId(),currentFlowState, new Date());
            }
            flowSmsService.recordFlowRejectSms(lupFlowEntity.getId(),currentFlowState,approvalFlowState);


        }catch (Exception exception){
            exception.printStackTrace();
        }

        return Result.success("");
    }


    @Override
    public Result processEnd(FlowMessageVO message) {
        String processInstanceId = message.getProcessInstanceId();

        LupFlowEntity ee = new LupFlowEntity();
        ee.setProcessInstanceId(processInstanceId);
        LupFlowEntity lupFlowEntity = lupFlowEntityMapper.getByOne(ee);
        LmContextHolder.setTenantId(lupFlowEntity.getTenantId().toString());

//        XlmProcessInstance processInstance = instanceProvider.getInstance(processInstanceId).getData();
//        Assert.notNull(processInstance, "获取XlmProcessInstance失败, processInstanceId=" + processInstanceId);

        lupFlowEntity.setFlowState(lupExecutionContext.resolveArchivedState(lupFlowEntity.getBizCode(), lupFlowEntity.getProcessDefinitionId()).getState());
        lupFlowEntity.setArchivedTime(DateUtil.toDateTime(new Date()));
//        lupFlowEntity.setProcessingDuration(processInstance.getDurationInMillis());

//        if (!repositoryProvider.saveFlow(flowEntity)) {
//            throw OperationFailedException.format("更新数据库失败, 请稍后重试");
//        }
        saveToDb(lupFlowEntity, null);

        return Result.success("");
    }

    private void saveToDb(LupFlowEntity lupFlowEntity, LupFlowActivity activity) {

        if ($.isNotNull(lupFlowEntity) && !lupFlowEntityService.saveOrUpdate(lupFlowEntity)) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailUpdate));
        }
        if ($.isNotNull(activity) && !lupFlowActivityService.saveOrUpdate(activity)) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailUpdate));
        }
    }


}
