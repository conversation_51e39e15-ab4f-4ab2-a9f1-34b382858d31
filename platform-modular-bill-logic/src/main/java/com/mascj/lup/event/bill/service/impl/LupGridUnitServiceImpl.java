package com.mascj.lup.event.bill.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.esri.core.geometry.*;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.exception.ServiceException;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.common.util.StringPool;
import com.mascj.kernel.tools.RedisKey;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.dto.ListUnitDTO;
import com.mascj.lup.event.bill.dto.LupGridUnitDTO;
import com.mascj.lup.event.bill.entity.LupGridUnit;
import com.mascj.lup.event.bill.enums.UnitType;
import com.mascj.lup.event.bill.mapper.LupGridUnitMapper;
import com.mascj.lup.event.bill.service.ILupBillService;
import com.mascj.lup.event.bill.service.ILupGridUnitService;
import com.mascj.lup.event.bill.service.ILupGridUserService;
import com.mascj.lup.event.bill.service.LupEventLabelService;
import com.mascj.lup.event.bill.util.ProjectUtil;
import com.mascj.lup.event.bill.vo.GriUnitVO;
import com.mascj.lup.event.bill.vo.GridUnitShapeVO;
import com.mascj.lup.event.bill.vo.LupBillCountVo;
import com.mascj.lup.event.bill.vo.LupGridUnitVO;
import io.jsonwebtoken.lang.Assert;
import lombok.AllArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 网格数据表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
@AllArgsConstructor
public class LupGridUnitServiceImpl extends ServiceImpl<LupGridUnitMapper, LupGridUnit> implements ILupGridUnitService {

    private final ILupBillService iLupBillService;

    private final ILupGridUserService gridUserService;

    private final RedisTemplate redisTemplate;

    //构建center对象
    private String getCenter(String center) {
        JSONArray objects = JSON.parseArray(center);
        String lng = objects.get(0) + "";
        String lat = objects.get(1) + "";
        Point point = new Point(Double.parseDouble(lng), Double.parseDouble(lat));
        String centerPoint = OperatorExportToWkt.local().execute(WktExportFlags.wktExportPoint, point, null);
        return centerPoint;
    }

    //构建polygon对象
    private String getPolygon(MapGeometry mapGeom, String type) {
        Polygon polygon = (Polygon) mapGeom.getGeometry();
        String polygonData = "";
        if ("polygon".equalsIgnoreCase(type)) {
            polygonData = OperatorExportToWkt.local().execute(WktExportFlags.wktExportPolygon, polygon, null);
        } else {
            polygonData = OperatorExportToWkt.local().execute(WktExportFlags.wktExportMultiPolygon, polygon, null);
        }
        return polygonData;
    }

    //构建geometry对象
    private MapGeometry getMapGeometry(JSONObject jsonObject) {
        String geometryStr = jsonObject.getString("geometry");
        MapGeometry mapGeom = OperatorImportFromGeoJson.local().
                execute(GeoJsonImportFlags.geoJsonImportDefaults,
                        Geometry.Type.Polygon,
                        geometryStr, null);
        return mapGeom;
    }

    @Override
    public void importData(MultipartFile file) {

        Long projectId = ProjectUtil.getProjectId();

        InputStream in = null;
        String jsonStr = "";
        JSONArray arr = null;
        try {
            in = file.getInputStream();
            jsonStr = IOUtils.toString(in, "UTF-8");

            try {
                JSONObject obj = JSONObject.parseObject(jsonStr);

                if(obj.containsKey("features")){
                    arr = obj.getJSONArray("features");
                }
                if(arr == null)
                    arr = JSONArray.parseArray(jsonStr);

            }catch (Exception exp){
                throw exp;
            }
        } catch (IOException e) {
            e.printStackTrace();
            Assert.isTrue(false,e.getMessage());
        }

        List<LupGridUnit> list = new ArrayList<>();
        if (arr != null && arr.size() > 0) {
            for (int i = 0; i < arr.size(); i++) {

                JSONObject jsonObject = (JSONObject) arr.get(i);

                JSONObject properties = jsonObject.getObject("properties", JSONObject.class);
                JSONObject geometry = jsonObject.getObject("geometry", JSONObject.class);

                String geometryStr = jsonObject.getString("geometry");

                String code = properties.containsKey("Code")?properties.getString("Code"):properties.getString("code");
                String Name = properties.containsKey("Name")?properties.getString("Name"):properties.getString("name");
                String center = properties.containsKey("Center")?properties.getString("Center"):properties.getString("center");
                BigDecimal area = properties.containsKey("Area")?properties.getBigDecimal("Area"):properties.getBigDecimal("area");
                String remark = properties.containsKey("Remark")?properties.getString("Remark"):properties.getString("remark");
                String type = geometry.getString("type");
                MapGeometry mapGeom = getMapGeometry(jsonObject);

                String polygon = getPolygon(mapGeom, type);

                String centerStr = getCenter(center);

                LupGridUnit unit = new LupGridUnit();
                unit.setCode(code);
                unit.setName(Name);
                unit.setProjectId(projectId);
                unit.setCentriod(centerStr);
                unit.setPolygon(polygon);
                unit.setPid(0l);
                unit.setDescription(remark);
                unit.setGridArea(area);
                unit.setHierarchy("");
                unit.setGeometryStr(geometryStr);
                unit.setCentriodStr(center);
                list.add(unit);
            }
        }
        //新增的数据
        int newSize = list.size();
        if (newSize <= 0) {
            return;
        }

        List<LupGridUnit> addList = new ArrayList<>();
        for (LupGridUnit unit : list) {
            String code = unit.getCode();
            String name = unit.getName();
            LambdaQueryWrapper<LupGridUnit> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(LupGridUnit::getIsDeleted,0);
            queryWrapper.eq(LupGridUnit::getProjectId, projectId);
            queryWrapper.eq(LupGridUnit::getCode, code);
            queryWrapper.eq(LupGridUnit::getName, name);
            List<LupGridUnit> list1 = baseMapper.selectList(queryWrapper);
            if (list1.size() <= 0) {
                addList.add(unit);
            } else {
                LupGridUnit unit1 = list1.get(0);
                unit.setId(unit1.getId());
                baseMapper.updateUnitById(unit);
            }
        }
        if (addList.size() > 0) {
            baseMapper.batchImport(addList);
        }


        LambdaQueryWrapper<LupGridUnit> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LupGridUnit::getIsDeleted,0);
        queryWrapper.eq(LupGridUnit::getProjectId, ProjectUtil.getProjectId());
        List<LupGridUnit> list1 = baseMapper.selectList(queryWrapper);
        //添加父类pid
        addPid(list1);
        //添加Hierarchy
        addHierarchy(list1);
        list1.forEach(
                e->{
                    RedisKey redisKey = RedisKey.forService(VariableConstants.UNIT_LEVEL_CACHE, ILupGridUserService.class)
                            .forParameter(String.valueOf(LmContextHolder.getTenantId()),String.valueOf(e.getId()));
                    redisTemplate.delete(redisKey.toString());
                }
        );
    }


    @Override
    public Result addUnitData(LupGridUnitDTO lupGridUnit) {

        LmContextHolder.setTenantId(lupGridUnit.getTenantId().toString());

        List<LupGridUnit> list = new ArrayList<>();

        String code = lupGridUnit.getCode();
        String name = lupGridUnit.getName();

        LupGridUnit unit = new LupGridUnit();
        unit.setCode(code);
        unit.setName(name);
        unit.setTenantId(lupGridUnit.getTenantId());
        unit.setProjectId(lupGridUnit.getTenantId());

        unit.setPid(0l);
        unit.setGridArea(new BigDecimal(-1));
        unit.setHierarchy("[0]");
//        unit.setGeometryStr(geometryStr);
        list.add(unit);

        LambdaQueryWrapper<LupGridUnit> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LupGridUnit::getIsDeleted,0);
        queryWrapper.eq(LupGridUnit::getProjectId, lupGridUnit.getTenantId());
        queryWrapper.eq(LupGridUnit::getTenantId, lupGridUnit.getTenantId());
        queryWrapper.eq(LupGridUnit::getCode, code);
        queryWrapper.eq(LupGridUnit::getName, name);
        queryWrapper.select(LupGridUnit::getId);

        List<LupGridUnit> list1 = baseMapper.selectList(queryWrapper);
        if(list1.size()>0){
            unit.setGeometryStr(null);
            unit.setId(list1.get(0).getId());
            baseMapper.updateUnitById(unit) ;
        }
        else {
            if (list.size() > 0) {
                baseMapper.batchImportInit(list);
            }
        }
        return Result.success("");
    }

    @Override
    public List<LupGridUnit> allUnit(){
        Long userId = LmContextHolder.getUserId();
        return allUnit(userId);
    }


    @Override
    public List<LupGridUnit> allUnit(Long userId){

        List<LupGridUnitVO> gridUnitVOList = baseMapper.findDataScope(ProjectUtil.getProjectId(),userId);
        if(gridUnitVOList == null || gridUnitVOList.size() == 0) return null;

        List<LupGridUnit> unitAllList = gridUserService.getUserLupGridUnitInfo(userId);
        if(unitAllList!=null && unitAllList.size()>0) return unitAllList;
        //root node
        List<LupGridUnitVO> rootUnitList = findRootUnit();

        if(rootUnitList!=null && rootUnitList.size() > 0){

            //查询子节点 unit
            List <LupGridUnit> list = new ArrayList<>();

            for (int i = 0; i < rootUnitList.size(); i++) {
                LupGridUnitVO rootUnit = rootUnitList.get(i);

                List <LupGridUnit> tmpList = list(Wrappers.<LupGridUnit>lambdaQuery()
                        .eq(LupGridUnit::getIsDeleted,0)
                        .eq(LupGridUnit::getId,rootUnit.getId())
                        .eq(ProjectUtil.getProjectId()!=null,LupGridUnit::getProjectId,ProjectUtil.getProjectId())
                        .orderByAsc(LupGridUnit::getSort)
                        .select(LupGridUnit::getPid,LupGridUnit::getGridArea,
                                LupGridUnit::getSort,LupGridUnit::getCode,LupGridUnit::getId,LupGridUnit::getName,LupGridUnit::getGeometryStr,LupGridUnit::getCentriodStr));

                if(tmpList.size()>0){list.addAll(tmpList);}
                //可以递归查询数据
                findAllChild(gridUnitVOList,list,rootUnit.getId());
            }



            gridUserService.saveUserInfo(LmContextHolder.getUserId(),list);
            return list;
        }

        return null;
    }
    private void findAllChild(List<LupGridUnitVO> gridUnitVOList,List <LupGridUnit> list,Long id){

        List <LupGridUnit> tmpList = findChildByPid(gridUnitVOList,id);
        if(tmpList==null || tmpList.size() ==0){
            return;
        }else {
            list.addAll(tmpList);
            for (LupGridUnit gridUnit : tmpList) {
                findAllChild(gridUnitVOList,list,gridUnit.getId());
            }
        }
    }



    @Override
    public Result<List<GriUnitVO>> listUnit(ListUnitDTO unitDTO) {

        Map<String,LupBillCountVo> billUnitCountMap =  new HashMap<>();

        prepareUnitDTO(unitDTO);


        List <GriUnitVO> unitVOList = new ArrayList<>();
        List <LupGridUnit> list = null;

        BillDTO billDTO = new BillDTO();
        BeanUtil.copyProperties(unitDTO,billDTO);
        billDTO.setProjectId(ProjectUtil.getProjectId());

        if(unitDTO.getUnitCode() != null && unitDTO.getUnitId() != null){
            //只返回列表数据
            list = findChildByPid(null,unitDTO.getUnitId());
            if(list==null) {
            list = list(Wrappers.<LupGridUnit>lambdaQuery()
                    .eq(LupGridUnit::getIsDeleted,0)
                    .eq(LupGridUnit::getPid,unitDTO.getUnitId())
                    .eq(ProjectUtil.getProjectId()!=null,LupGridUnit::getProjectId,ProjectUtil.getProjectId())
                    .orderByAsc(LupGridUnit::getSort)
                    .select(LupGridUnit::getSort,LupGridUnit::getCode,LupGridUnit::getId,LupGridUnit::getName));
            }
            if(list.size()>0)
                billDTO.setGridCode(list.get(0).getCode());
            billDTO.setParentOrgCode(unitDTO.getUnitCode());
        }else {
            //查询根节点数据 同时返回shape
            List<LupGridUnitVO> rootUnitList = findRootUnit();

            if(rootUnitList!=null && rootUnitList.size()>0) {
                list = new ArrayList<>();
                for (int i = 0; i < rootUnitList.size(); i++) {

                    LupGridUnitVO rootUnit = rootUnitList.get(i);

                    List<LupGridUnit> tmpList =  list(Wrappers.<LupGridUnit>lambdaQuery()
                            .eq(LupGridUnit::getIsDeleted,0)
                            .eq(LupGridUnit::getId,rootUnit.getId())
                            .eq(ProjectUtil.getProjectId()!=null,LupGridUnit::getProjectId,ProjectUtil.getProjectId())
                            .orderByAsc(LupGridUnit::getSort)
                            .select(LupGridUnit::getSort,LupGridUnit::getCode,LupGridUnit::getId,LupGridUnit::getName,LupGridUnit::getGeometryStr,LupGridUnit::getCentriodStr));

                    if(tmpList.size()>0)
                        list.addAll(tmpList);
                }

//                list = list(Wrappers.<LupGridUnit>lambdaQuery()
//                        .eq(LupGridUnit::getIsDeleted,0)
//                        .eq(LupGridUnit::getId,rootUnit.getId())
//                        .eq(ProjectUtil.getProjectId()!=null,LupGridUnit::getProjectId,ProjectUtil.getProjectId())
//                        .orderByAsc(LupGridUnit::getSort)
//                        .select(LupGridUnit::getSort,LupGridUnit::getCode,LupGridUnit::getId,LupGridUnit::getName,LupGridUnit::getGeometryStr,LupGridUnit::getCentriodStr));

                if(list.size()>0)
                    billDTO.setGridCode(list.get(0).getCode());
            }else {
                //查询根节点数据 同时返回shape
                list = list(Wrappers.<LupGridUnit>lambdaQuery()
                        .eq(LupGridUnit::getIsDeleted,0)
                        .eq(LupGridUnit::getPid,0)
                        .eq(ProjectUtil.getProjectId()!=null,LupGridUnit::getProjectId,ProjectUtil.getProjectId())
                        .orderByAsc(LupGridUnit::getSort)
                        .select(LupGridUnit::getSort,LupGridUnit::getCode,LupGridUnit::getId,LupGridUnit::getName,LupGridUnit::getGeometryStr,LupGridUnit::getCentriodStr));

                if(list.size()>0) {



                    for (int i = 1; i < list.size(); i++) {
                        billDTO.setGridCode(list.get(i).getCode());
                        List<LupBillCountVo> billCountVoList = iLupBillService.countBillByGrid(billDTO);
                        //查询
                        Map<String,LupBillCountVo> billMap = billCountVoList.stream().collect(Collectors.toMap(LupBillCountVo::getCode,
                                bill -> bill
                        ));
                        billMap.forEach((k1,k2)->{
                            billUnitCountMap.put(k1,k2);
                        });

                    }

                    billDTO.setGridCode(list.get(0).getCode());

                }
            }
        }

        List<LupBillCountVo> billCountVoList = iLupBillService.countBillByGrid(billDTO);
        Map<String,LupBillCountVo> billMap = billCountVoList.stream().collect(Collectors.toMap(LupBillCountVo::getCode,
                bill -> bill
        ));

        billUnitCountMap.forEach((k1,v1)->{
            billMap.put(k1,v1);
        });
        if(list!=null) {
            list.forEach(item -> {

                GriUnitVO unitVO = new GriUnitVO();

                unitVO.setId(item.getId());
                unitVO.setGridName(item.getName());
                unitVO.setGridCode(item.getCode());
                unitVO.setShape(item.getGeometryStr());
                unitVO.setCenter(item.getCentriodStr());
                unitVO.setSort(item.getSort());

                int childCount = count(Wrappers.<LupGridUnit>lambdaQuery()
                        .eq(LupGridUnit::getIsDeleted, 0)
                        .eq(LupGridUnit::getPid, item.getId())
                        .eq(ProjectUtil.getProjectId() != null, LupGridUnit::getProjectId, ProjectUtil.getProjectId()));
                unitVO.setLastNode(childCount == 0);


                if (billMap.containsKey(item.getCode()))
                    unitVO.setEventAmount(
                            billMap.get(item.getCode()).getBillCount()
                    );

                if(unitVO.getGridName().equals(EventTipKey.UnknownAreaName)){
                    unitVO.setGridName(LocaleMessageUtil.getMessage(EventTipKey.UnknownAreaName));
                    if(unitVO.getEventAmount()>0)
                        unitVOList.add(unitVO);
                }else
                    unitVOList.add(unitVO);
            });
        }

        return Result.data(unitVOList);
    }


    private void prepareUnitDTO(ListUnitDTO unitDTO){

        if(unitDTO.getDateEnd() != null && !"".equals(unitDTO.getDateEnd())){

            unitDTO.setDateEnd(
                    unitDTO.getDateEnd() + " 23:59:59"
            );
        }
        unitDTO.setGridUnitIdList(gridUserService.dataScope(LmContextHolder.getUserId()));
    }



    /**
     * 查询根节点
     * @return
     */
    private List<LupGridUnitVO> findRootUnit(){

        Long userId = LmContextHolder.getUserId();

        List<LupGridUnitVO> gridUnitVOList = baseMapper.findDataScope(ProjectUtil.getProjectId(),userId);
        if(gridUnitVOList.size()==0)return null;

        List<Long>parentGridUnitIdList = gridUnitVOList.stream().map(LupGridUnitVO::getPid).collect(Collectors.toList());

        Boolean  bf = true;
        //判断 拥有的父节点数据 全部是跟几点的情况
        for (int i = 0; i < parentGridUnitIdList.size(); i++) {
            Long pid = parentGridUnitIdList.get(i);
            if(pid > 0){
                bf = false;
                break;
            }
        }

        if(bf) {
            return gridUnitVOList;
        }

        LupGridUnitVO finalRootNode = findRootUnitInfoList(userId,parentGridUnitIdList);

        List<LupGridUnitVO> list = new ArrayList<>();
        if(finalRootNode!=null) list.add(finalRootNode);
        return list;
    }

    /**
     * 递归查询根节点
     *
     * @param userId
     * @param parentGridUnitIdList
     * @return
     */
    private LupGridUnitVO findRootUnitInfoList(Long userId,List<Long>parentGridUnitIdList){

        List<LupGridUnitVO> gridUnitVOList = baseMapper.findGridUnitRootNode(ProjectUtil.getProjectId(),parentGridUnitIdList);

        if(gridUnitVOList.size()==1){
            LupGridUnitVO gridUnitVO = gridUnitVOList.get(0);

            List<LupGridUnit> lupGridUnitList = null;
            Long unitId = gridUnitVO.getId();
            LupGridUnit tmpUnitData = null;
            //查找本节点下属的节点 如果唯一 记为正确的根节点
            do {
                    lupGridUnitList = findChildByPid(null,unitId);
                    if(lupGridUnitList.size() > 1){
                        //子节点 多个  上一个节点数据为 root节点
                        gridUnitVO = baseMapper.getOneGridUnitVO(unitId);
                        break;
                    }else if(lupGridUnitList.size() == 1){
                        //子节点 1个
                        unitId = lupGridUnitList.get(0).getId();
//                        gridUnitVO = baseMapper.getOneGridUnitVO(unitId);
                    }else {
                        gridUnitVO = baseMapper.getOneGridUnitVO(unitId);
//                        gridUnitVO = baseMapper.getOneGridUnitVO(unitId);
//                        gridUnitVO = baseMapper.getOneGridUnitVO(gridUnitVO.getPid());
                        break;
                    }

            }while (true);

            return gridUnitVO;

        }else {
            parentGridUnitIdList = gridUnitVOList.stream().map(LupGridUnitVO::getPid).collect(Collectors.toList());
            return findRootUnitInfoList( userId, parentGridUnitIdList);
        }
    }

    private List<LupGridUnit> findChildByPid(List<LupGridUnitVO> tmpGridUnitVOList,Long unitId){

        List<LupGridUnit> finalList = new ArrayList<>();
        //根据id获取子节点unit  并且 和 权限关联起来；
        List<LupGridUnit> list = list(Wrappers.<LupGridUnit>lambdaQuery()
                .eq(LupGridUnit::getIsDeleted,0)
                .eq(LupGridUnit::getPid,unitId)
                .select(LupGridUnit::getPid,LupGridUnit::getGridArea,
                        LupGridUnit::getHierarchy,LupGridUnit::getSort,LupGridUnit::getCode,LupGridUnit::getId,LupGridUnit::getName)
        );

        Long userId = LmContextHolder.getUserId();
        List<LupGridUnitVO> gridUnitVOList = tmpGridUnitVOList ==null? baseMapper.findDataScope(ProjectUtil.getProjectId(),userId):tmpGridUnitVOList;
        if(gridUnitVOList.size()==0)return null;
        list.forEach(item->{
            for (LupGridUnitVO unit : gridUnitVOList) {
                String pIdListStr = unit.getHierarchy().replace(StringPool.LEFT_SQ_BRACKET,"").replace(StringPool.RIGHT_SQ_BRACKET,"");
                String[]pIdArr = pIdListStr.split(",");
                List pIdList = Arrays.asList(pIdArr);

                if(pIdList.contains(item.getId().toString())){
                    if(!finalList.contains(item))
                    finalList.add(item);
                    break;
                }else  if(
                        unit.getId().equals(item.getId())
//                        unitId.toString().equals(pIdList.get(pIdList.size()-1))
            ){
                    if(!finalList.contains(item))
                        finalList.add(item);
                    break;
                }
            }


        });

        return finalList;

    }


    /**
     * 多层机构结构的网格
     *
     * 提供前端选择区域下辖子级机构的网格数据
     *
     * 并且针对当前选中的节点是叶子节点的情况下
     * 返回的数据  current 为 当前节点的父节点   childList 为  当前节点的父节点  的 所有的一级子节点
     *
     * 如果当前节点不是叶子节点
     *
     * 返回的数据  current 为当前节点  childList 为当前节点的下的所有的一级子节点
     *
     * @param unitDTO
     * @return
     */
    @Override
    public Result<GridUnitShapeVO> listChildUnit(ListUnitDTO unitDTO) {

        prepareUnitDTO(unitDTO);
        if(unitDTO.getGridUnitIdList()== null || unitDTO.getGridUnitIdList().size()==0){
            int childCount = count(Wrappers.<LupGridUnit>lambdaQuery()
                    .eq(LupGridUnit::getIsDeleted,0)
                    .eq(LupGridUnit::getPid,unitDTO.getUnitId())
                    .eq(ProjectUtil.getProjectId()!=null,LupGridUnit::getProjectId,ProjectUtil.getProjectId()));
            boolean hasNoChild = childCount == 0;

            GridUnitShapeVO gridUnitShapeVO = new GridUnitShapeVO();

            List <GriUnitVO> unitVOList = new ArrayList<>();
            List <LupGridUnit> list = null;

            if(hasNoChild){

                list = list(Wrappers.<LupGridUnit>lambdaQuery()
                        .eq(LupGridUnit::getIsDeleted, 0)
                        .eq(LupGridUnit::getId, unitDTO.getUnitId())
                        .eq(ProjectUtil.getProjectId()!=null,LupGridUnit::getProjectId, ProjectUtil.getProjectId())
                        .orderByAsc(LupGridUnit::getSort)
                        .select(LupGridUnit::getGeometryStr,LupGridUnit::getPid, LupGridUnit::getSort, LupGridUnit::getCode, LupGridUnit::getId, LupGridUnit::getName, LupGridUnit::getCentriodStr));

                if(list.get(0).getPid()>0)
                    unitDTO.setUnitId(list.get(0).getPid());
            }
            if (unitDTO.getUnitCode() != null && unitDTO.getUnitId() != null) {
                //只返回列表数据
                list = list(Wrappers.<LupGridUnit>lambdaQuery()
                        .eq(LupGridUnit::getIsDeleted, 0)
                        .eq(LupGridUnit::getPid, unitDTO.getUnitId())
                        .eq(ProjectUtil.getProjectId()!=null,LupGridUnit::getProjectId, ProjectUtil.getProjectId())
                        .orderByAsc(LupGridUnit::getSort)
                        .select(LupGridUnit::getGeometryStr, LupGridUnit::getSort, LupGridUnit::getCode, LupGridUnit::getId, LupGridUnit::getName, LupGridUnit::getCentriodStr));


            } else {
                //查询根节点数据 同时返回shape
                list = list(Wrappers.<LupGridUnit>lambdaQuery().eq(LupGridUnit::getIsDeleted, 0).eq(LupGridUnit::getPid, 0)
                        .eq(ProjectUtil.getProjectId()!=null,LupGridUnit::getProjectId, ProjectUtil.getProjectId())
                        .orderByAsc(LupGridUnit::getSort)
                        .select(LupGridUnit::getSort, LupGridUnit::getCode, LupGridUnit::getId, LupGridUnit::getName, LupGridUnit::getGeometryStr, LupGridUnit::getCentriodStr));

            }

            list.forEach(item -> {

                GriUnitVO unitVO = new GriUnitVO();

                unitVO.setId(item.getId());
                unitVO.setGridName(item.getName());
                unitVO.setGridCode(item.getCode());
                unitVO.setShape(item.getGeometryStr());
                unitVO.setCenter(item.getCentriodStr());
                unitVO.setSort(item.getSort());

                unitVOList.add(unitVO);
            });
            gridUnitShapeVO.setChildUnitList(unitVOList);

            if (unitDTO.getUnitCode() != null && unitDTO.getUnitId() != null) {
                //只返回列表数据
                list = list(Wrappers.<LupGridUnit>lambdaQuery()
                        .eq(LupGridUnit::getIsDeleted, 0)
                        .eq(LupGridUnit::getId, unitDTO.getUnitId())
                        .eq(ProjectUtil.getProjectId()!=null,LupGridUnit::getProjectId, ProjectUtil.getProjectId())
                        .orderByDesc(LupGridUnit::getSort)
                        .select(LupGridUnit::getGeometryStr, LupGridUnit::getSort, LupGridUnit::getCode, LupGridUnit::getId, LupGridUnit::getName, LupGridUnit::getCentriodStr));

                list.forEach(item -> {
                    GriUnitVO unitVO = new GriUnitVO();

                    unitVO.setId(item.getId());
                    unitVO.setGridName(item.getName());
                    unitVO.setGridCode(item.getCode());
                    unitVO.setShape(item.getGeometryStr());
                    unitVO.setCenter(item.getCentriodStr());
                    unitVO.setSort(item.getSort());
                    gridUnitShapeVO.setCurrentUnit(unitVO);
                });
            }
            return Result.data(gridUnitShapeVO);
        }else {

            List<LupGridUnit> tmpList = findChildByPid(null,unitDTO.getUnitId());

            int childCount = tmpList.size();

            boolean hasNoChild = childCount == 0;

            GridUnitShapeVO gridUnitShapeVO = new GridUnitShapeVO();

            List<GriUnitVO> unitVOList = new ArrayList<>();
            List<LupGridUnit> list = null;
            List<LupGridUnit> finalList = new ArrayList<>();

            if (hasNoChild) {

                list = list(Wrappers.<LupGridUnit>lambdaQuery()
                        .eq(LupGridUnit::getIsDeleted, 0)
                        .eq(LupGridUnit::getId, unitDTO.getUnitId())
                        .eq(ProjectUtil.getProjectId() != null, LupGridUnit::getProjectId, ProjectUtil.getProjectId())
                        .orderByAsc(LupGridUnit::getSort)
                        .select(LupGridUnit::getGeometryStr, LupGridUnit::getPid, LupGridUnit::getSort, LupGridUnit::getCode, LupGridUnit::getId, LupGridUnit::getName, LupGridUnit::getCentriodStr));

                unitDTO.setUnitId(list.get(0).getPid());

                tmpList = findChildByPid(null,unitDTO.getUnitId());
            }

//            BillDTO billDTO = new BillDTO();
//            BeanUtil.copyProperties(unitDTO, billDTO);
//            billDTO.setProjectId(ProjectUtil.getProjectId());

            if (unitDTO.getUnitCode() != null && unitDTO.getUnitId() != null) {
                //只返回列表数据
                list = list(Wrappers.<LupGridUnit>lambdaQuery()
                        .eq(LupGridUnit::getIsDeleted, 0)
                        .eq(LupGridUnit::getPid, unitDTO.getUnitId())
                        .eq(ProjectUtil.getProjectId() != null, LupGridUnit::getProjectId, ProjectUtil.getProjectId())
                        .orderByAsc(LupGridUnit::getSort)
                        .select(LupGridUnit::getGeometryStr, LupGridUnit::getSort, LupGridUnit::getCode, LupGridUnit::getId, LupGridUnit::getName, LupGridUnit::getCentriodStr));


            } else {
                //查询根节点数据 同时返回shape
                list = list(Wrappers.<LupGridUnit>lambdaQuery().eq(LupGridUnit::getIsDeleted, 0).eq(LupGridUnit::getPid, 0)
                        .eq(ProjectUtil.getProjectId() != null, LupGridUnit::getProjectId, ProjectUtil.getProjectId())
                        .orderByAsc(LupGridUnit::getSort)
                        .select(LupGridUnit::getSort, LupGridUnit::getCode, LupGridUnit::getId, LupGridUnit::getName, LupGridUnit::getGeometryStr, LupGridUnit::getCentriodStr));

            }


            for (LupGridUnit item : list) {
                for (LupGridUnit e : tmpList) {
                    if (e.getId().toString().equals(item.getId().toString())) {
                        GriUnitVO unitVO = new GriUnitVO();

                        unitVO.setId(item.getId());
                        unitVO.setGridName(item.getName());
                        unitVO.setGridCode(item.getCode());
                        unitVO.setShape(item.getGeometryStr());
                        unitVO.setCenter(item.getCentriodStr());
                        unitVO.setSort(item.getSort());

                        if(unitVO.getGridName().equals(EventTipKey.UnknownAreaName)){
                            unitVO.setGridName(LocaleMessageUtil.getMessage(EventTipKey.UnknownAreaName));
                        }
                        unitVOList.add(unitVO);
                        break;
                    }
                }
            }

            gridUnitShapeVO.setChildUnitList(unitVOList);

            if (unitDTO.getUnitCode() != null && unitDTO.getUnitId() != null) {
                //只返回列表数据
                list = list(Wrappers.<LupGridUnit>lambdaQuery()
                        .eq(LupGridUnit::getIsDeleted, 0)
                        .eq(LupGridUnit::getId, unitDTO.getUnitId())
                        .eq(ProjectUtil.getProjectId() != null, LupGridUnit::getProjectId, ProjectUtil.getProjectId())
                        .orderByDesc(LupGridUnit::getSort)
                        .select(LupGridUnit::getGeometryStr, LupGridUnit::getSort, LupGridUnit::getCode, LupGridUnit::getId, LupGridUnit::getName, LupGridUnit::getCentriodStr));

                list.forEach(item -> {
                    GriUnitVO unitVO = new GriUnitVO();

                    unitVO.setId(item.getId());
                    unitVO.setGridName(item.getName());
                    unitVO.setGridCode(item.getCode());
                    unitVO.setShape(item.getGeometryStr());
                    unitVO.setCenter(item.getCentriodStr());
                    unitVO.setSort(item.getSort());

                    if(unitVO.getGridName().equals(EventTipKey.UnknownAreaName)){
                        unitVO.setGridName(LocaleMessageUtil.getMessage(EventTipKey.UnknownAreaName));
                    }

                    gridUnitShapeVO.setCurrentUnit(unitVO);
                });
            }


            return Result.data(gridUnitShapeVO);
        }
    }

    @Override
    public List<LupGridUnit> listByUnitId(Long unitId) {

        RedisKey redisKey = RedisKey.forService(VariableConstants.UNIT_LEVEL_CACHE, ILupGridUserService.class)
                .forParameter(String.valueOf(LmContextHolder.getTenantId()),String.valueOf(unitId));
        List<LupGridUnit> list = (List<LupGridUnit>)redisTemplate.opsForValue().get(redisKey.toString());
        if(list!=null && list.size()>0){
            return list;
        }

        List<LupGridUnit> resultList = new ArrayList<>();
        //查询子级数据
        //递归子集数据
        queryByPid(resultList,unitId);

        //查询本级数据
        LupGridUnit gridUnit = getOne(
                Wrappers.<LupGridUnit>lambdaQuery()
                        .eq(LupGridUnit::getIsDeleted,"0")
                        .eq(LupGridUnit::getId,unitId)
                        .select(LupGridUnit::getId,LupGridUnit::getPid,LupGridUnit::getCode,LupGridUnit::getGridArea,LupGridUnit::getHierarchy,LupGridUnit::getGeometryStr)
        );
        if(gridUnit!=null) resultList.add(gridUnit);

        redisTemplate.opsForValue().set(redisKey.toString(),resultList);

        return resultList;
    }

    private void queryByPid(List<LupGridUnit> resultList  ,Long rootUnitId){
        List<LupGridUnit> childList = list(Wrappers.<LupGridUnit>lambdaQuery()
                .eq(LupGridUnit::getIsDeleted,"0")
                .eq(LupGridUnit::getPid,rootUnitId)
                .select(LupGridUnit::getId,LupGridUnit::getPid,LupGridUnit::getCode,LupGridUnit::getGridArea,LupGridUnit::getHierarchy)
        );
        if(childList.size()>0){

            resultList.addAll(childList);

            childList.forEach(unit->{
                queryByPid(resultList,unit.getId());
            });
        }else {
            return;
        }
    }


    /**
     * 添加pid父id
     *
     * @param list
     */
    private void addPid(List<LupGridUnit> list) {
        for (LupGridUnit a : list) {
            String code = a.getCode();
            int alength = a.getCode().length();
            String level = getLevel(alength);
            if (level == null) {
                continue;
            }
            Integer value = UnitType.getValue(level);
            for (LupGridUnit b : list) {
                String code1 = b.getCode();
                int blength = code1.length();
                String blevel = getLevel(blength);
                if (blevel == null) {
                    continue;
                }
                Integer bvalue = UnitType.getValue(blevel);
                if (StrUtil.startWith(code, code1) && value + 1 == bvalue) {
                    System.err.println(code + "-----" + code1);
                    a.setPid(b.getId());
                    continue;
                }
            }
        }
        if (list.size() > 0) {
            baseMapper.batchUpdatePid(list);
        }
    }
    /**
     * 获取网格层级新
     *
     * @param length
     * @return
     */
    private String getLevel(int length) {
        switch (length) {
            case 2:
                return "省";
            case 4:
                return "市";
            case 6:
                return "区县";
            case 9:
                return "镇街";
            case 12:
                return "村社区";
            case 15:
                return "网格";
            case 19:
                return "地块";
            default:
                return null;
        }
    }

    /**
     * 添加pid父id
     *
     * @param list
     */
    private void addHierarchy(List<LupGridUnit> list) {
        for (LupGridUnit unit : list) {
            String hierStr = "";
            Long id = unit.getId();
            Long pid = unit.getPid();
            if (pid == 0) {
                String pidStr = StringPool.LEFT_SQ_BRACKET + pid + StringPool.RIGHT_SQ_BRACKET;
                hierStr = hierStr + pidStr;
                unit.setHierarchy(hierStr);
                continue;
            }
            //填充hierstr
            String s = this.makeHierarchy(hierStr, id);

            //字符串反转
            String[] cc = s.split(",");
            String hierarchyStr = "";
            for (int i = cc.length - 1; i >= 0; i--) {
                hierarchyStr += cc[i] + ",";
            }
            unit.setHierarchy(hierarchyStr);
        }
        if (list.size() > 0) {
            baseMapper.batchUpdateHhierarchy(list);
        }
    }

    private String makeHierarchy(String hierStr, Long id) {
        LupGridUnit unit = baseMapper.selectById(id);
        if (unit == null) {
            return hierStr;
        } else {
            Long pid1 = unit.getPid();
            String pidStr = StringPool.LEFT_SQ_BRACKET + pid1 + StringPool.RIGHT_SQ_BRACKET;
            hierStr += pidStr + ",";
            return makeHierarchy(hierStr, pid1);
        }


    }


}
