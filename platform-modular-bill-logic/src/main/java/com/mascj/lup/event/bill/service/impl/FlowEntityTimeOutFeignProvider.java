package com.mascj.lup.event.bill.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.FlowEntityTimeOutSaveDTO;
import com.mascj.lup.event.bill.entity.LupFlowEntityTimeOut;
import com.mascj.lup.event.bill.entity.LupLabelFlow;
import com.mascj.lup.event.bill.service.IFlowEntityTimeOutService;
import com.mascj.lup.event.bill.service.ILupFlowEntityTimeOutService;
import com.mascj.lup.event.bill.service.ILupLabelFlowService;
import com.mascj.lup.event.bill.util.ReadConfigUtil;
import com.mascj.lup.event.bill.vo.config.FlowEntityTimeOutVO;
import com.mascj.lup.event.bill.vo.config.HandleModuleConfigVO;
import com.mascj.support.config.feign.IConfigValueProvider;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/15 15:08
 * @describe
 */
@RestController
@AllArgsConstructor
public class FlowEntityTimeOutFeignProvider implements IFlowEntityTimeOutService {

    private final ILupFlowEntityTimeOutService flowEntityTimeOutService;

    private final ILupLabelFlowService labelFlowService;

    private final IConfigValueProvider configValueProvider;

    @Autowired
    private ReadConfigUtil readConfigUtil;


    @Override
    public Result saveFlowEntityTimeOut(@RequestBody FlowEntityTimeOutSaveDTO timeOutSaveDTO) {

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();
         {
            // 开关 默认 0 未开启  1 已开启
            Boolean timeSwitch = null;
            BigDecimal timeLimit = null;
            LupLabelFlow labelFlow = labelFlowService.labelFlowDetailByState(timeOutSaveDTO.getFlowEntityId(), timeOutSaveDTO.getFlowState());

            if (labelFlow != null) {
                timeSwitch = labelFlow.getTimeSwitch() > 0;
                if (timeSwitch) {
                    //单位小时
                    timeLimit = labelFlow.getTimeLimit();
                }
            } else {


                if (handleModuleConfigVO != null && handleModuleConfigVO.getFlowEntityTimeOutVOMap() != null) {

                    FlowEntityTimeOutVO flowEntityTimeOutVO = handleModuleConfigVO.getFlowEntityTimeOutVOMap().get(timeOutSaveDTO.getFlowState());
                    if (flowEntityTimeOutVO != null && handleModuleConfigVO.getFlowEntityTimeOutSwitchKey()) {
                        BigDecimal tmpTimeLimit = flowEntityTimeOutVO.getTimeLimit();
                        if (tmpTimeLimit != null) {
                            timeLimit = tmpTimeLimit;
                        }
                    }

                }
            }

            if ((labelFlow == null && timeLimit != null) || (labelFlow != null
                    //事件标签和当前配置的标签一直 流程阶段一致
                    && timeSwitch)) {
                LupFlowEntityTimeOut flowEntityTimeOut = BeanUtil.toBean(timeOutSaveDTO, LupFlowEntityTimeOut.class);

                flowEntityTimeOut.setProcessStartTime(DateUtil.now());
                flowEntityTimeOut.setDeadLine(
                        DateUtil.formatDateTime(
                                DateUtil.offsetMinute(new Date(), timeLimit.multiply(new BigDecimal(VariableConstants.FlowEntityTimeOutHourLength)).intValue())));

                flowEntityTimeOutService.save(flowEntityTimeOut);

            }

        }
        return Result.success(LocaleMessageUtil.getMessage(EventTipKey.CommonSuccess));
    }
}
