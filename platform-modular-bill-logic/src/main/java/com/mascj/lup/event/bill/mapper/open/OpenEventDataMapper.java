package com.mascj.lup.event.bill.mapper.open;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.lup.event.bill.dto.EventDataQueryDTO;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.open.vo.OpenEventDataDetailVO;
import com.mascj.lup.event.bill.open.vo.OpenEventDataVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OpenEventDataMapper extends BaseMapper<LupBill> {
    IPage<OpenEventDataVO> pageOpenEventData(IPage<OpenEventDataVO> lupBillPage, @Param("data") EventDataQueryDTO eventDataQueryDTO);

    OpenEventDataDetailVO detailEventData(@Param("billId") Long billId);
}
