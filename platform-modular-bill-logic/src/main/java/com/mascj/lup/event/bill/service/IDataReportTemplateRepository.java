package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import com.mascj.lup.event.bill.dto.DataReportTemplateSetDTO;
import com.mascj.lup.event.bill.dto.ReportTemplateSearchDTO;
import com.mascj.lup.event.bill.entity.DataReportTemplate;
import com.mascj.lup.event.bill.vo.DataReportTemplateVO;

import java.util.List;
import java.util.Map;

public interface IDataReportTemplateRepository extends IService<DataReportTemplate> {

    IPage<DataReportTemplateVO>  listPage(ReportTemplateSearchDTO search);
    Boolean setReportTemplate(DataReportTemplateSetDTO aepReportTemplateSetVO);

    Boolean delTemplate(List<Long> toLongList);

    List<Map<String,String>> getTemplateList(String orgId);


}
