package com.mascj.lup.event.bill.support;

import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.tools.RedisKey;
import com.mascj.kernel.tools.RedisPlane;
import com.mascj.kernel.tools.linq.Linqs;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.platform.system.entity.Dict;
import com.mascj.platform.system.feign.ISysDictProvider;
import com.mascj.support.config.dto.XlmActivityState;
import com.mascj.support.config.dto.XlmBizCategory;
import com.mascj.support.config.dto.XlmBizEntity;
import com.mascj.support.config.feign.IAepConfigProvider;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
@AllArgsConstructor
public class DataConfigContext {

    // TODO: 考虑存在多个流程定义的情况
    private final IAepConfigProvider aepConfigProvider;
    private final ISysDictProvider dictProvider;
    private final RedisPlane redisPlane;


    public List<XlmActivityState> getActivityList(String bizCode) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));

        RedisKey redisKey = RedisKey.forService("biz-aep-core", XlmActivityState.class)
                .forParameter(bizCode, String.valueOf(LmContextHolder.getTenantId()));

        return redisPlane.getOrCreateList(redisKey, () -> aepConfigProvider.getActivityList(bizCode).getData(), XlmActivityState.class);

    }

    public List<XlmActivityState> getActivityList(String bizCode, String processDefinitionId) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Assert.hasText(processDefinitionId, LocaleMessageUtil.getMessage(EventTipKey.NoneProcessDefinitionId, Arrays.asList(EventTipKey.NoneTip)));

        RedisKey redisKey = RedisKey.forService("biz-aep-core", XlmActivityState.class)
                .forParameter(bizCode, processDefinitionId, String.valueOf(LmContextHolder.getTenantId()));

        return redisPlane.getOrCreateList(redisKey,
                () -> aepConfigProvider.getEnvironmentOrDefault(bizCode, processDefinitionId).getData().getActivityStates(),
                XlmActivityState.class);
    }

    @Deprecated
    public Map<String, XlmActivityState> getActivityMap(String bizCode) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));

        return Linqs.of(getActivityList(bizCode)).toMap(XlmActivityState::getActivityId);
    }

    public Map<String, XlmActivityState> getActivityMap(String bizCode, String processDefinitionId) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Assert.hasText(processDefinitionId, LocaleMessageUtil.getMessage(EventTipKey.NoneProcessDefinitionId,Arrays.asList(EventTipKey.NoneTip)));

        return Linqs.of(getActivityList(bizCode, processDefinitionId)).toMap(XlmActivityState::getActivityId);
    }

    public List<XlmBizCategory> getBizList() {
        return aepConfigProvider.getBizList().getData();
    }

    public List<XlmBizEntity> getEventList(String bizCode) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        return aepConfigProvider.getEventList(bizCode).getData();
    }

    public List<XlmBizEntity> getBillStateList(String bizCode) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        return aepConfigProvider.getBillStateList(bizCode).getData();
    }

    public List<XlmBizEntity> getHandleList(String bizCode) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        return aepConfigProvider.getHandleList(bizCode).getData();
    }

    public Map<String, String> getHandleMap(String bizCode) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        return Linqs.of(getHandleList(bizCode)).toMap(XlmBizEntity::getCode, XlmBizEntity::getName);
    }

    public Map<String, String> getBillStateMap(String bizCode) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        return Linqs.of(getBillStateList(bizCode)).toMap(XlmBizEntity::getCode, XlmBizEntity::getName);
    }

    public Map<String, String> getEventMap(String bizCode) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        return Linqs.of(getEventList(bizCode)).toMap(XlmBizEntity::getCode, XlmBizEntity::getName);
    }

    public Map<String, String> getDictMap(String code) {
        Assert.hasText(code, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneCode, Arrays.asList(EventTipKey.RequiredTip)));

        RedisKey redisKey = RedisKey.forEntity("liangma-aep", Dict.class).forItem(code);

        List<Dict> list = redisPlane.getOrCreateList(redisKey,
                () -> dictProvider.getList(code).getData(),
                Dict.class
        );

        return Linqs.of(list).toMap(Dict::getDictKey, Dict::getDictValue);
    }
}
