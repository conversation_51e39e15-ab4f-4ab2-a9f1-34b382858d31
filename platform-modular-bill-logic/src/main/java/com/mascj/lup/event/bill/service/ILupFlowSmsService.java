package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.lup.event.bill.entity.LupFlowSms;
import com.mascj.lup.event.bill.entity.LupGridUser;

/**
 * <AUTHOR>
 * @date 2024/10/18 18:07
 * @describe
 */
public interface ILupFlowSmsService extends IService<LupFlowSms> {

    void recordFlowRejectSms(Long flowId, Integer currentFlowState, Integer approvalFlowState);
}
