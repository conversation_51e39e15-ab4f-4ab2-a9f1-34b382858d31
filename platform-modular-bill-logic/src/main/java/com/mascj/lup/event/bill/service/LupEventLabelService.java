package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.database.entity.Search;
import com.mascj.lup.event.bill.entity.LupEventLabel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.lup.event.bill.vo.LupCommonTreeVO;
import com.mascj.lup.event.bill.vo.LupEventLabelVo;
import com.mascj.platform.system.entity.Dict;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @since 2024-01-05 14:36:06
 */
public interface LupEventLabelService extends IService<LupEventLabel> {

    Result<IPage<LupEventLabelVo>> listPage(Integer codeValue, Search search);

    Result saveConfig(LupEventLabelVo lupEventLabelVo);

    List<Long> premissionList(Long labelId);

    List<Long> labelIdsByUserIds(List<Long> userIds);


    List<LupCommonTreeVO> listLabelTree();
    Map<String,String> listOrigin();
}