package com.mascj.lup.event.bill.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.database.entity.Search;
import com.mascj.kernel.database.util.PageUtil;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.LabelFlowBatchSaveDTO;
import com.mascj.lup.event.bill.dto.LabelFlowSaveDTO;
import com.mascj.lup.event.bill.entity.LupLabelFlow;
import com.mascj.lup.event.bill.mapper.LupLabelFlowMapper;
import com.mascj.lup.event.bill.service.ILupLabelFlowService;
import com.mascj.lup.event.bill.util.ReadConfigUtil;
import com.mascj.lup.event.bill.vo.LupLabelFlowTimeLimitVO;
import com.mascj.lup.event.bill.vo.LupLabelFlowVO;
import com.mascj.lup.event.bill.vo.config.FlowEntityTimeOutVO;
import com.mascj.lup.event.bill.vo.config.HandleModuleConfigVO;
import com.mascj.platform.system.entity.Dict;
import com.mascj.platform.system.feign.ISysDictProvider;
import com.mascj.platform.system.feign.ISysUserProvider;
import com.mascj.support.config.dto.XlmActivityState;
import com.mascj.support.config.feign.IAepConfigProvider;
import com.mascj.support.config.feign.IConfigValueProvider;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 工单数据表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
@AllArgsConstructor
public class LupLabelFlowServiceImpl extends ServiceImpl<LupLabelFlowMapper, LupLabelFlow> implements ILupLabelFlowService {

    private final IConfigValueProvider configValueProvider;

    private final IAepConfigProvider configProvider;

    private final ISysUserProvider iSysUserProvider;

    private final ReadConfigUtil readConfigUtil;

    @Override
    public Result<IPage<LupLabelFlowVO>> labelFlowPage(Integer codeValue, Search search) {

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();




        // 查询标签
        IPage<LupLabelFlowVO> page = baseMapper.labelFlowPage(codeValue, PageUtil.getPage(search));
        List<LupLabelFlowVO> records = page.getRecords();

        if (CollectionUtil.isNotEmpty(records)) {

            List<Long> labelIds = records.stream().map(LupLabelFlowVO::getLabelId).collect(Collectors.toList());

            List<LupLabelFlow> labelFlowList = baseMapper.selectList(new LambdaQueryWrapper<LupLabelFlow>()
                    .eq(LupLabelFlow::getDeleted, 0).in(LupLabelFlow::getLabelId, labelIds));



                Map<Long, List<LupLabelFlow>> labelFlowListMap = labelFlowList.stream().collect(Collectors.groupingBy(LupLabelFlow::getLabelId));

                records.forEach(item -> {

                    item.setTimeLimitData(new HashMap<>());

                    List<LupLabelFlow> lupLabelFlowList = labelFlowListMap.get(item.getLabelId());
                    if(lupLabelFlowList!=null && lupLabelFlowList.size()>0) {
                        Map<Integer, LupLabelFlow> labelFlowMap = lupLabelFlowList.stream().collect(Collectors.toMap(LupLabelFlow::getFlowState, x -> x, (exist, repeat) -> exist));


                        Map<Integer, LupLabelFlowTimeLimitVO> timeLimitData = item.getTimeLimitData();

                        labelFlowMap.keySet().forEach(flowState -> {
                            LupLabelFlow lupLabelFlow = labelFlowMap.get(flowState);
                            LupLabelFlowTimeLimitVO labelFlowTimeLimitVo = BeanUtil.toBean(lupLabelFlow, LupLabelFlowTimeLimitVO.class);
                            timeLimitData.put(flowState, labelFlowTimeLimitVo);
                        });



                    }

                    //补充没有的flowState 设定默认的数据
                    handleModuleConfigVO.getFlowEntityTimeOutVOMap().keySet().forEach(flowState->{
                        Map<Integer, LupLabelFlowTimeLimitVO> timeLimitData = item.getTimeLimitData();

                        if(!item.getTimeLimitData().keySet().contains(flowState)){
                            FlowEntityTimeOutVO flowEntityTimeOutVO = handleModuleConfigVO.getFlowEntityTimeOutVOMap().get(flowState);
                            if(flowEntityTimeOutVO != null && handleModuleConfigVO.getFlowEntityTimeOutSwitchKey() ) {
                                LupLabelFlowTimeLimitVO labelFlowTimeLimitVo = new LupLabelFlowTimeLimitVO();

                                labelFlowTimeLimitVo.setTimeSwitch(1);
                                labelFlowTimeLimitVo.setTimeLimit(flowEntityTimeOutVO.getTimeLimit());
                                labelFlowTimeLimitVo.setFlowState(flowState);

                                timeLimitData.put(flowState, labelFlowTimeLimitVo);
                            }
                        }
                    });




                });

            }


        return Result.data(page);
    }


    @Override
    public LupLabelFlowTimeLimitVO labelFlowDetail(Long id) {
        LupLabelFlow lupLabelFlow =  getById(id);
        LupLabelFlowTimeLimitVO labelFlowTimeLimitVO = BeanUtil.toBean(lupLabelFlow,LupLabelFlowTimeLimitVO.class);
        return labelFlowTimeLimitVO;
    }


    @Override
    public LupLabelFlow labelFlowDetailByState(Long flowId, Integer flowState) {
        List<LupLabelFlow> lupLabelFlow =  baseMapper.labelFlowDetailByState(flowId,flowState);

        if(lupLabelFlow.size()>0) return lupLabelFlow.get(0);

        return null;
    }

    @Override
    @Transactional
    public Boolean batchSaveLabelFlow(LabelFlowBatchSaveDTO labelFlowBatchSaveDTO) {

        List<XlmActivityState> activityStates = configProvider.getActivityList(VariableConstants.handleCode).getData();

        Map<Integer,XlmActivityState> activityStateMap=
                activityStates.stream().
                        collect(Collectors.toMap(XlmActivityState::getState,x->x,(e,x)->e));


        List<LupLabelFlow> labelFlowList = list(Wrappers.<LupLabelFlow>lambdaQuery().eq(LupLabelFlow::getDeleted,0));

        List<LupLabelFlow> delList = new ArrayList<>();
        labelFlowList.forEach(e->{
            LupLabelFlow del = new LupLabelFlow();
            del.setId(e.getId());
            del.setDeleted(1);
            delList.add(del);
        });

        if(delList.size() > 0)
            updateBatchById(delList);

        List<LupLabelFlow> saveList = new ArrayList<>();
        labelFlowBatchSaveDTO.getLabelIdList().forEach(labelId->{
            labelFlowBatchSaveDTO.getFlowStateList().forEach(flowState->{

                LupLabelFlow save = new LupLabelFlow();
                save.setLabelId(labelId);
                save.setFlowState(flowState);
                XlmActivityState xlmActivityState = activityStateMap.get(flowState);
                save.setFlowActivityId(xlmActivityState.getActivityId());
                save.setTimeLimit(labelFlowBatchSaveDTO.getTimeLimit());
                save.setTimeSwitch(labelFlowBatchSaveDTO.getTimeSwitch());

                saveList.add(save);
            });
        });

        if(saveList.size()>0){
            saveBatch(saveList);
        }

        return true;
    }

    @Override
    public Boolean saveLabelFlow(LabelFlowSaveDTO labelFlowSaveDTO) {
        List<XlmActivityState> activityStates = configProvider.getActivityList(VariableConstants.handleCode).getData();

        Map<Integer,XlmActivityState> activityStateMap=
                activityStates.stream().
                        collect(Collectors.toMap(XlmActivityState::getState,x->x,(e,x)->e));

        LupLabelFlow save = new LupLabelFlow();
        save.setId(labelFlowSaveDTO.getLabelFlowId());
        save.setLabelId(labelFlowSaveDTO.getLabelId());
        save.setFlowState(labelFlowSaveDTO.getFlowState());
        XlmActivityState xlmActivityState = activityStateMap.get(labelFlowSaveDTO.getFlowState());
        save.setFlowActivityId(xlmActivityState.getActivityId());
        save.setTimeLimit(labelFlowSaveDTO.getTimeLimit());
        save.setTimeSwitch(labelFlowSaveDTO.getTimeSwitch());

        saveOrUpdate(save);
        return true;
    }
}
