package com.mascj.lup.event.bill.support;

/**
 * <AUTHOR>
 * @date 2024/11/1 08:47
 * @describe
 */
import com.mascj.lup.event.bill.constant.VariableConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MessageSender {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void sendMessage(String message) {
        rabbitTemplate.convertAndSend(VariableConstants.RabbitMQQueueNameForReceiveEventDataFromDimension, message);
        log.info(" [x] Sent '" + message + "'");
    }
}
