package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mascj.lup.event.bill.entity.LupGridUnit;
import com.mascj.lup.event.bill.vo.LupGridUnitVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 网格数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface LupGridUnitMapper extends BaseMapper<LupGridUnit> {

    void updateUnitById(@Param("unit") LupGridUnit unit);
    void batchImport(@Param("list") List<LupGridUnit> list);
    void batchImportInit(@Param("list") List<LupGridUnit> list);
    void batchUpdatePid(@Param("list") List<LupGridUnit> list);
    void batchUpdateHhierarchy(@Param("list") List<LupGridUnit> list);

    List<LupGridUnitVO> findGridByPoint(@Param("projectId")Long projectId, @Param("centerPoint")String centerPoint);

    List<LupGridUnitVO> findDataScope(@Param("projectId")Long projectId,@Param("userId")Long userId);
    List<LupGridUnitVO> findGridUnitRootNode(@Param("projectId")Long projectId,@Param("list") List<Long> list);

    LupGridUnitVO getOneGridUnitVO(@Param("id")Long id);
}
