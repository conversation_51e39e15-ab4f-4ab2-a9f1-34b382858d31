package com.mascj.lup.event.bill.service.sync;

import com.alibaba.fastjson.JSON;

import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.$;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.tools.SetFunction;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.dto.ExportFileDTO;
import com.mascj.lup.event.bill.entity.DataReport;
import com.mascj.lup.event.bill.entity.DataSyncLog;
import com.mascj.lup.event.bill.service.IDataReportRepository;
import com.mascj.lup.event.bill.service.IExportFileStrategyService;
import com.mascj.lup.event.bill.sync.ExportFileSyncState;
import com.mascj.support.config.feign.IAepConfigProvider;
import lombok.Data;
import org.springframework.context.ApplicationContext;
import org.springframework.util.Assert;

import java.util.Arrays;

@Data
public class ExportFileSyncRunner implements Runnable {

        private final ExportFileSyncState syncState;

        private final ExportFileSyncContext dataSyncContext;

        private final IDataSyncLogRepository syncLogRepository;

        private final IDataReportRepository reportRepository;

        private final int pageSize = 10;

        private final IExportFileStrategyService exportFileStrategyService;

        private ExportFileDTO reportDto;

    public ExportFileSyncRunner(ExportFileSyncState syncState, ApplicationContext applicationContext, IExportFileStrategyService exportFileStrategyService , ExportFileDTO dto) {
            Assert.notNull(syncState, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneSyncState, Arrays.asList(EventTipKey.RequiredTip)));
            Assert.notNull(applicationContext, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneSyncContext, Arrays.asList(EventTipKey.RequiredTip)));
            this.syncState = syncState;
            this.dataSyncContext = applicationContext.getBean(ExportFileSyncContext.class);
            this.reportRepository = applicationContext.getBean(

                    IDataReportRepository.class);
            this.syncLogRepository = applicationContext.getBean(IDataSyncLogRepository.class);
            this.reportDto = dto;
            this.exportFileStrategyService = exportFileStrategyService;

        }

        @Override
        public void run() {
            LmContextHolder.setLang(syncState.getLang());
            LmContextHolder.setUserId(syncState.getUserId());
            LmContextHolder.setTenantId(syncState.getTenantId());

            try {
                //执行生成报告
                this.exportFileStrategyService.startExportFile(this.reportDto,this);

            } catch (Exception e) {
                DataReport report = new DataReport();
                report.setId(reportDto.getReportId());

                report.setExecuteRemark("报告生成失败[" +
                        e.toString() +
                        "]");
                report.setState(2);

                reportRepository.updateProgress(report);

                e.printStackTrace();
                syncState.setFailed(true);
                syncState.setErrMessage(e.toString());
                dataSyncContext.updateState(syncState);
            }

            if (!syncState.isFailed()) {
                syncState.setFinished(true);
                dataSyncContext.updateState(syncState);
            }

            dataSyncContext.finishTask(syncState.getUuid());

            saveSyncState(syncState);

        }

        /**
         * 更新进度
         * @param size
         */
    public void updateSyncState(int size){
//        size++;
        syncState.setCurrent(syncState.getCurrent() + size);
        dataSyncContext.updateState(syncState);

        DataReport report = new DataReport();
        report.setId(reportDto.getReportId());
        report.setCurrentLine(syncState.getCurrent());

        reportRepository.updateProgress(report);
    }

    /**
     * 更新进度
     * @param size
     */
    public void updateSyncState(int size,int total,String downloadFileName,String downloadUrl,Long fileId,int state){
//        size++;

        syncState.setFileId(fileId);
        syncState.setDownloadUrl(downloadUrl);
        syncState.setDownloadFileName(downloadFileName);

        syncState.setCurrent(syncState.getCurrent() + size);
        dataSyncContext.updateState(syncState);


        DataReport report = new DataReport();
        report.setDownloadUrl(downloadUrl);
        report.setId(reportDto.getReportId());
        report.setCurrentLine(syncState.getCurrent());
        if(syncState.getCurrent() == total){
            report.setExecuteRemark("报告已生成");
            report.setFileId(fileId);
            report.setState(state);
        }

        reportRepository.updateProgress(report);


    }

    /**
     * 更新进度
     * @param size
     */
    public void updateSyncState(int size,int total,String url,int state){
//        size++;
        syncState.setCurrent(syncState.getCurrent() + size);
        dataSyncContext.updateState(syncState);

        DataReport report = new DataReport();
        report.setId(reportDto.getReportId());
        report.setCurrentLine(syncState.getCurrent());
        if(syncState.getCurrent() == total){
            report.setExecuteRemark("报告已生成");
            report.setDownloadUrl(url);
            report.setState(state);
        }

        reportRepository.updateProgress(report);
    }

    private void saveSyncState(ExportFileSyncState syncState) {
        Assert.notNull(syncState, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneSyncState, Arrays.asList(EventTipKey.RequiredTip)));

        DataSyncLog syncLog = SetFunction
                .lambdaSet(DataSyncLog::getUuid, syncState.getUuid())
                .set(DataSyncLog::getUserId, syncState.getUserId())
                .set(DataSyncLog::getAppId, syncState.getAppId())
                .set(DataSyncLog::getBatchCode, syncState.getBatchCode())
                .set(DataSyncLog::getTotal, syncState.getTotal())
                .set(DataSyncLog::getErrCount, syncState.getErrCount())
                .set(DataSyncLog::isSuccess, syncState.isFinished())
                .set(DataSyncLog::getTenantId, Long.parseLong(syncState.getTenantId()))
                .set($.isNotBlank(syncState.getErrMessage()), DataSyncLog::getFailedMessage, syncState.getErrMessage())
                .set($.isNotEmpty(syncState.getErrDetails()), DataSyncLog::getDetails, JSON.toJSONString(syncState.getErrDetails()))
                .getInstance();

        syncLogRepository.save(syncLog);
    }
}
