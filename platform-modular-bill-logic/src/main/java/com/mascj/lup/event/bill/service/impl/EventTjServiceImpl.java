package com.mascj.lup.event.bill.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.DateUtil;
import com.mascj.kernel.common.util.StringUtil;
import com.mascj.kernel.common.util.beans.BeanUtil;
import com.mascj.lup.datacenter.constant.Constants;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.mapper.EventTJMapper;
import com.mascj.lup.event.bill.service.EventTjService;
import com.mascj.lup.event.bill.vo.*;
import com.mascj.platform.micro.entity.SysTenant;
import com.mascj.platform.micro.feign.ISysTenantProvider;
import com.mascj.platform.system.entity.Tenant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EventTjServiceImpl implements EventTjService {
    @Resource
    private EventTJMapper eventTJMapper;
    @Resource
    private ISysTenantProvider iSysTenantProvider;

    @Override
    public Result<FlyEventTjResVO> eventTj(FlyEventTjQueryVO flyEventTjQueryVO) {
        flyEventTjQueryVO.dealQuery();
        FlyEventTjResVO flyEventTjResVO =  eventTJMapper.eventTj(flyEventTjQueryVO);
        return Result.data(flyEventTjResVO);
    }

    @Override
    public FlyEventRankTwoVO eventRank(FlyEventTjQueryVO flyEventTjQueryVO) {
        FlyEventRankTwoVO flyEventRankTwoVO = new FlyEventRankTwoVO();
        //对缺少的月处理
        String monthStart = flyEventTjQueryVO.getMonthStart();
        String dateType = flyEventTjQueryVO.getDateType();
        flyEventTjQueryVO.dealQuery();

        List<FlyEventRankVO> flyEventRankVOS = eventTJMapper.eventRank(flyEventTjQueryVO);
        flyEventRankTwoVO.setFlyEventRankVOList(flyEventRankVOS);
        if (CollectionUtil.isNotEmpty(flyEventRankVOS)){
            List<Long> labelIdList = flyEventRankVOS.stream().map(FlyEventRankVO::getLabelId).collect(Collectors.toList());
            flyEventTjQueryVO.setLabelIdList(labelIdList);
            List<FlyEventMonthTjVO> flyEventMonthTjVOS = eventTJMapper.eventRankLine(flyEventTjQueryVO);
            Map<String, List<FlyEventMonthTjVO>> collect1 = flyEventMonthTjVOS.stream().collect(Collectors.groupingBy(FlyEventMonthTjVO::getMonth ));

            List<FlyEventEachMonthTjVO> list = new ArrayList<>();
            collect1.forEach((k,v)->{
                FlyEventEachMonthTjVO flyEventEachMonthTjVO = new FlyEventEachMonthTjVO();
                flyEventEachMonthTjVO.setFlyEventMonthTjVOList(v);
                flyEventEachMonthTjVO.setMonth(k);
                list.add(flyEventEachMonthTjVO);
            });


            if (StringUtil.isBlank(monthStart)){
                Result<SysTenant> byId = iSysTenantProvider.getById(Long.parseLong(LmContextHolder.getTenantId()));
                SysTenant data = byId.getData();
                if (dateType.equals(VariableConstants.DATATYPE.MONTH)){
                    monthStart =     DateUtil.format(data.getCreateTime(), "yyyy-MM");
                }else {
                    monthStart =     DateUtil.format(data.getCreateTime(), "yyyy-MM-dd");
                    flyEventTjQueryVO.setDateStart(monthStart);
                }

            }
            List<String> getAllDatesBetween = null;
            if (dateType.equals(VariableConstants.DATATYPE.MONTH)){
                getAllDatesBetween =  getMonthsList(monthStart, flyEventTjQueryVO.getMonthEnd());
                
            } else if (dateType.equals(VariableConstants.DATATYPE.DATE) ){
                getAllDatesBetween =  getAllDatesBetween(flyEventTjQueryVO.getDateStart(), flyEventTjQueryVO.getDateEnd());
            }

            List<String> collectMonthEd = list.stream().map(FlyEventEachMonthTjVO::getMonth).collect(Collectors.toList());
            for (String s : getAllDatesBetween) {
                if (!collectMonthEd.contains(s)){
                    list.add(new FlyEventEachMonthTjVO(s));
                }
            }
            Collections.sort(list, Comparator.comparing(FlyEventEachMonthTjVO::getMonth));

            List<FlyEventMonthTjVO> copy = BeanUtil.copy(flyEventRankVOS, FlyEventMonthTjVO.class);
            copy.forEach(e->e.setNum(0));
            Map<Long, FlyEventMonthTjVO> collect2 = copy.stream().collect(Collectors.toMap(FlyEventMonthTjVO::getLabelId, e -> e));
            for (FlyEventEachMonthTjVO flyEventEachMonthTjVO : list) {
                //处理里面缺少的东西
                List<FlyEventMonthTjVO> flyEventMonthTjVOList = flyEventEachMonthTjVO.getFlyEventMonthTjVOList();
                if (CollectionUtil.isEmpty(flyEventMonthTjVOList)){
                    flyEventEachMonthTjVO.setFlyEventMonthTjVOList(copy);
                }else {
                    List<Long> collect = flyEventMonthTjVOList.stream().map(FlyEventMonthTjVO::getLabelId).collect(Collectors.toList());
                    for (Long l : labelIdList) {
                        if (!collect.contains(l)){
                            flyEventMonthTjVOList.add(collect2.get(l));
                        }
                    }
                    flyEventEachMonthTjVO.setFlyEventMonthTjVOList(flyEventMonthTjVOList);
                }
                List<FlyEventMonthTjVO> listNew = flyEventEachMonthTjVO.getFlyEventMonthTjVOList();
                Collections.sort(listNew, Comparator.comparing(FlyEventMonthTjVO::getLabelId));
                flyEventEachMonthTjVO.setFlyEventMonthTjVOList(listNew);
            }
            flyEventRankTwoVO.setFlyEventEachMonthTjVOList(list);

        }
        return flyEventRankTwoVO;
    }
}
