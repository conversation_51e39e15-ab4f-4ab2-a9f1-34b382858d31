package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mascj.lup.event.bill.entity.LupFlowEntity;

import java.util.List;

/**
 * <p>
 * 流程记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface LupFlowEntityMapper extends BaseMapper<LupFlowEntity> {

    @InterceptorIgnore(tenantLine = "true")
    LupFlowEntity getByOne(LupFlowEntity entity);
    List<LupFlowEntity> list_deal_four( );
}
