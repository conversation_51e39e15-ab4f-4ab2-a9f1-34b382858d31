package com.mascj.lup.event.bill.service;

import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.PushKQDTO;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.vo.SendFourResutVo;

/**
 * <AUTHOR>
 * @date 2024/1/3 18:35
 * @describe
 */
public interface IPushDataService {

    Result<SendFourResutVo> translateData(PushKQDTO pushKQDTO, LupData lupData, LupBill lupBill, String labelName) throws Exception;

    Result translateData(Long dataId,Long pushLogId);
}
