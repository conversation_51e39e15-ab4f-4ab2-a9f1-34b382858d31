package com.mascj.lup.event.bill.service.strategy;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.redis.core.RedisService;
import com.mascj.kernel.tools.RedisKey;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.dto.ExportFileDTO;
import com.mascj.lup.event.bill.service.IExportFileStrategyService;
import com.mascj.lup.event.bill.service.ILupBillService;
import com.mascj.lup.event.bill.service.sync.ExportFileSyncRunner;
import com.mascj.lup.event.bill.sync.ReportSyncState;
import com.mascj.lup.event.bill.util.FileUtils;
import com.mascj.lup.event.bill.vo.BillSearchExcelVO;
import com.mascj.lup.event.bill.vo.BillSearchExcelYXQVO;
import com.mascj.lup.event.bill.vo.CustomerTitleHandler;
import com.mascj.support.file.api.feign.IFileProvider;
import com.mascj.support.file.api.model.XlmFile;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.List;

@AllArgsConstructor
@Service("BillExcelFileYXQ")
public class ExportWorkBillExcelYXQServiceImpl implements IExportFileStrategyService {

    private final RedisService redisService;
    private final RedisKey redisKeyTemplate = RedisKey.forEntity("biz-lup-report", ReportSyncState.class);
    private final IFileProvider fileProvider;
    private final ILupBillService billService;

    @Override
    public int initDataTotal(ExportFileDTO exportFileDto) {

        int count =  1;

        int totalStep = count>0?count+2:0;

        return totalStep;
    }

    @Override
    public void initReportInfo(ExportFileDTO exportFileDto) {
        //表头名+工单+导出时间（具体到秒），例如待巡查工单20230408092230
//        exportFileDto.getReport().setFileName(exportFileDto.getTagName()+"工单"+ DateUtil.format(new Date(),"yyyyMMddHHmmss"));
        exportFileDto.setFileName(
                LocaleMessageUtil.getMessage(EventTipKey.CommonReportFileName)
                + DateUtil.format(new Date(),"yyyyMMddHHmmss")+".xlsx");
    }

    /**
     * 开始导出文件
     * 按照类型执行指定的策略 在各自策略内部完成数据搜索和任务执行
     *
     * @param exportFileDto 查询数据的参数
     */
    @Override
    public void startExportFile(ExportFileDTO exportFileDto) {
        System.out.println("导出工单excel");
    }

    private String buildRedisKey(String bizCode,String orgMd5){
        return redisKeyTemplate.forItem(LmContextHolder.getTenantId(),LmContextHolder.getUserId()+"-org",bizCode,orgMd5).toString();
    }
    /**
     * 开始导出文件
     * 按照类型执行指定的策略 在各自策略内部完成数据搜索和任务执行
     *
     * @param exportFileDto        查询数据的参数
     * @param exportFileSyncRunner
     */
    @Override
    public void startExportFile(ExportFileDTO exportFileDto, ExportFileSyncRunner exportFileSyncRunner) throws Exception {

        String redisKey = buildRedisKey(exportFileDto.getBizCode(), DigestUtils.md5DigestAsHex(JSONUtil.toJsonStr(exportFileDto).getBytes()));

        Object obj = redisService.get(redisKey);

        if(obj != null){
            //直接返回地址
            XlmFile xmlFile = (XlmFile)obj;
            exportFileSyncRunner.updateSyncState(exportFileDto.getDataTotal(),exportFileDto.getDataTotal(),exportFileDto.getFileName(),xmlFile.getUrl(),xmlFile.getId(),1);
        }


//查询并生成文件数据

        exportFileSyncRunner.updateSyncState(1,exportFileDto.getDataTotal(),"",0);

        //生成文件
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        List<BillSearchExcelYXQVO> billSearchExcelVOList = billService.exportYXQDataToExcel(exportFileDto);

        EasyExcel.write(byteArrayOutputStream, BillSearchExcelYXQVO.class)
                .sheet(LocaleMessageUtil.getMessage(EventTipKey.CommonReportFileName))

                .registerWriteHandler(new CustomerTitleHandler())
//                .registerConverter(new WriteCellDataConverter())
                .doWrite(billSearchExcelVOList);

        exportFileSyncRunner.updateSyncState(1,exportFileDto.getDataTotal(),"",0);

        //上传文件得到结果

        InputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        MultipartFile file = FileUtils.getMultipartFile(inputStream, exportFileDto.getFileName());

        //hasOverTime 普通文件
        Result<XlmFile> uploadResult = fileProvider.uploadFile(file, "hasOverTime");

        Assert.isTrue(200 == uploadResult.getCode(), LocaleMessageUtil.getMessage(EventTipKey.FailFileUpload));


        exportFileSyncRunner.updateSyncState(1,exportFileDto.getDataTotal(),exportFileDto.getFileName(),uploadResult.getData().getUrl(),uploadResult.getData().getId(),1);

        redisService.set(
                redisKey
                ,
                JSONUtil.toJsonStr(uploadResult.getData())
        );

    }
}
