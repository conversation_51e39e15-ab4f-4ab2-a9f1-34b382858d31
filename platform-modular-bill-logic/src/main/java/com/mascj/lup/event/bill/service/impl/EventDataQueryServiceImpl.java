package com.mascj.lup.event.bill.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.$;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.tools.RedisKey;
import com.mascj.kernel.tools.RedisPlane;
import com.mascj.kernel.tools.SetFunction;
import com.mascj.kernel.tools.linq.Enumerable;
import com.mascj.kernel.tools.linq.Linqs;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.LupFlowActivity;
import com.mascj.lup.event.bill.entity.LupFlowEntity;
import com.mascj.lup.event.bill.exceptions.OperationFailedException;
import com.mascj.lup.event.bill.mapper.LupDataMapper;
import com.mascj.lup.event.bill.service.*;
import com.mascj.lup.event.bill.support.LupExecutionContext;
import com.mascj.lup.event.bill.vo.*;
import com.mascj.platform.system.feign.ISysUserProvider;

import com.mascj.support.config.dto.XlmActivityState;
import com.mascj.support.config.feign.IAepConfigProvider;
import com.mascj.support.workflow.dto.XlmQueryBaseTaskDto;
import com.mascj.support.workflow.dto.XlmQueryHisTaskDto;
import com.mascj.support.workflow.entity.XlmTaskInfo;
import com.mascj.support.workflow.feign.IXlmQueryProvider;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.mascj.kernel.common.util.LocaleMessageUtil.getMessage;

/**
 * <AUTHOR>
 * @date 2023/11/14 09:35
 * @describe
 */
@Service
@AllArgsConstructor
public class EventDataQueryServiceImpl implements IEventDataQueryService {

    private final ILupDataService dataService;
    private final ILupBillService billService;
    private final ILupFlowEntityService iLupFlowEntityService;
    private final ISysUserProvider sysUserProvider;
    private final IXlmQueryProvider xlmQueryProvider;
    private final LupExecutionContext configContext;
    private final ILupLabelService iLupLabelService;
    private final ILupFlowActivityService iLupFlowActivityService;
    private final RedisPlane redisPlane;
    private final IAepConfigProvider aepConfigProvider;

    @Override
    public List<EventDataLabelVO> labelList() {
        return iLupLabelService.labelList();
    }

    @Override
    public List<EventDataBaseInfoVO> listAllEventData(){
        List<EventDataBaseInfoVO> list = dataService.listAllEventData();


        return list;
    }

    public Page<EventDataTaskVO> pageByTask(Page<EventDataTaskVO> page, EventDataTaskQueryDTO eventDataTaskQueryDTO){
        dataService.pageByTask(page,eventDataTaskQueryDTO);
        page.getRecords().forEach(item->{

            try {
                item.setEventPictureUrlList(new ArrayList<>());

                if (item.getExtraData() != null && !"".equals(item.getExtraData())) {
                    JSONObject jsonObject = JSONUtil.parseObj(item.getExtraData());
                    if(jsonObject != null && jsonObject.containsKey("eventPictureUrl")){
                        item.setEventPictureUrl(jsonObject.get("eventPictureUrl").toString());
                    }

                    System.out.println("billDetailVO.getExtraData:"+item.getExtraData());
                    item.setExtraDataVO( JSONUtil.toBean(item.getExtraData(), ExtraDataVO.class));
//                    item.setExtraData(null);
                    if(item.getExtraDataVO().getEventPictureUrlList()!=null && item.getExtraDataVO().getEventPictureUrlList().size()>0){

//                        if(!item.getEventPictureUrlList().contains(item.getEventPictureUrl()))
//                            item.getEventPictureUrlList().add(item.getEventPictureUrl());

                        item.getEventPictureUrlList().addAll(item.getExtraDataVO().getEventPictureUrlList());
                    }

                }

            }catch (Exception exp)
            {
                exp.printStackTrace();
            }

            if(!item.getEventPictureUrlList().contains(item.getEventPictureUrl()))
                item.getEventPictureUrlList().add(item.getEventPictureUrl());

        });
        return page;
    }

    @Override
    public IPage<EventDataVO> page(IPage<EventDataVO> page, EventDataQueryDTO eventDataQueryDTO) {


        List<XlmActivityState> activityStates = aepConfigProvider.getActivityList("handle").getData();

        Map<Integer,String> activityStateMap= activityStates.stream().collect(Collectors.toMap(XlmActivityState::getState,XlmActivityState::getName));


        dataService.pageEventData(page,eventDataQueryDTO);
        page.getRecords().forEach(item->{

            if( item.getEventLabelList().size()>0 ) item.setEventLabel(item.getEventLabelList().get(0).getName());

            try {
                if (item.getExtraData() != null && !"".equals(item.getExtraData())) {
                    System.out.println("dataVO.getExtraData:"+item.getExtraData());
                    item.setExtraDataVO( JSONUtil.toBean(item.getExtraData(), ExtraDataVO.class));
                }
            }catch (Exception exp)
            {
                exp.printStackTrace();
            }

            if(item.getExtraDataVO() == null){
                item.setExtraDataVO(new ExtraDataVO());
            }
            if(!item.getExtraDataVO().getEventPictureUrlList().contains(item.getExtraDataVO().getEventPictureUrl()))
                item.getExtraDataVO().getEventPictureUrlList().add(item.getExtraDataVO().getEventPictureUrl());

            item.setName(activityStateMap.get(item.getFlowState()));
        });
        return page;
    }

    @Override
    public Page<EventDataVO> pageForLand(Page<EventDataVO> page, EventDataForLandQueryDTO eventDataQueryDTO) {


        List<XlmActivityState> activityStates = aepConfigProvider.getActivityList("handle").getData();

        Map<Integer,String> activityStateMap= activityStates.stream().collect(Collectors.toMap(XlmActivityState::getState,XlmActivityState::getName));


        dataService.pageEventDataForLand(page,eventDataQueryDTO);
        page.getRecords().forEach(item->{

            if( item.getEventLabelList().size()>0 ) item.setEventLabel(item.getEventLabelList().get(0).getName());

            try {
                if (item.getExtraData() != null && !"".equals(item.getExtraData())) {

                    item.setExtraDataVO( JSONUtil.toBean(item.getExtraData(), ExtraDataVO.class));
                }
            }catch (Exception exp)
            {
                exp.printStackTrace();
            }

            if(item.getExtraDataVO() == null){
                item.setExtraDataVO(new ExtraDataVO());
            }
            if(!item.getExtraDataVO().getEventPictureUrlList().contains(item.getExtraDataVO().getEventPictureUrl()))
                item.getExtraDataVO().getEventPictureUrlList().add(item.getExtraDataVO().getEventPictureUrl());

            item.setName(activityStateMap.get(item.getFlowState()));
        });
        return page;
    }


    @Override
    public EventDataVO detail(Long billId) {
        RedisKey redisKey = RedisKey.forService(VariableConstants.lupHandleSystemTag, LupBillServiceImpl.class)
                .forParameter(VariableConstants.HandleModuleConfigCode,VariableConstants.HandleModuleBillDataQueryCode, String.valueOf(LmContextHolder.getTenantId()),String.valueOf(billId));
        EventDataVO finalDataVO = redisPlane.getOrCreate(redisKey,()-> {
            EventDataVO dataVO = dataService.detail(billId);
            if (dataVO.getGridName().equals(EventTipKey.UnknownAreaName)) {
                dataVO.setGridName(getMessage(EventTipKey.UnknownAreaName));
                dataVO.setAreaName(getMessage(EventTipKey.UnknownAreaName));
            }
            List<XlmActivityState> activityStates = aepConfigProvider.getActivityList("handle").getData();

            Map<Integer, String> activityStateMap = activityStates.stream().collect(Collectors.toMap(XlmActivityState::getState, XlmActivityState::getName));

            dataVO.setName(activityStateMap.get(dataVO.getFlowState()));

            //查询flowentity数据
            LupFlowEntity byId = iLupFlowEntityService.getById(dataVO.getFlowEntityId());
            if (byId != null) {

                Integer flowState = byId.getFlowState();
                if (flowState == 3) {
                    Result<List<String>> permissionByRequest = sysUserProvider.getPermissionByRequest(LmContextHolder.getUserId());
                    List<String> data = permissionByRequest.getData();
                    if (CollectionUtils.isNotEmpty(data) && data.contains("event:bill:view")) {
                        dataVO.setCheck(true);
                    } else {
                        dataVO.setCheck(false);
                    }
                }

                XlmQueryHisTaskDto queryHisTaskDto = SetFunction
                        .lambdaSet(XlmQueryHisTaskDto::getProcessInstanceId, byId.getProcessInstanceId())
                        // 倒序展示，最新的节点排在最前面
                        .set(XlmQueryBaseTaskDto::isOrderByTaskCreateTime, true)
                        .set(XlmQueryBaseTaskDto::isAsc, false)
                        .set(XlmQueryHisTaskDto::isReturnFormData, true)
                        .getInstance();

                List<XlmTaskInfo> taskInfoList = xlmQueryProvider.queryHisTask(queryHisTaskDto).getData();
                Map<String, Map<String, Object>> formData = new HashMap<>();

                for (XlmTaskInfo info : taskInfoList) {
                    info.setName(getMessage("event.flow.".concat(info.getTaskDefinitionKey())));
                    if ($.isEmpty(info.getFormData())) {
                        continue;
                    }
                    // 展示最新一次的数据
                    if (!formData.containsKey(info.getTaskDefinitionKey())) {
                        formData.put(info.getTaskDefinitionKey(), info.getFormData());
                    }
                }

                Enumerable<TaskVo> select = Linqs.of(taskInfoList).select(x -> billService.convertTask(x, byId.getBizCode()));
                List<TaskVo> taskVoList = new ArrayList<>();
                Integer flowState1 = byId.getFlowState();
                if (flowState1 == 4) {
                    TaskVo end = new TaskVo();
                    end.setName(getMessage("event.flow.archived"));
                    TaskVo taskVo = select.get(0);
                    end.setCreateTime(taskVo.getEndTime());
                    taskVoList.add(end);
                }

                billService.rebuildTaskVoCollection(taskVoList, select, billId);

                dataVO.setEventFlowInfo(JSONUtil.toJsonStr(taskVoList));

            }

            try {
                if (dataVO.getExtraData() != null && !"".equals(dataVO.getExtraData())) {
                    System.out.println("dataVO.getExtraData:" + dataVO.getExtraData());
                    dataVO.setExtraDataVO(JSONUtil.toBean(dataVO.getExtraData(), ExtraDataVO.class));
//                dataVO.setExtraData(null);
                }

            } catch (Exception exp) {
                exp.printStackTrace();
            }


            if (dataVO.getExtraDataVO() == null) {
                dataVO.setExtraDataVO(new ExtraDataVO());
            }
            if (!dataVO.getExtraDataVO().getEventPictureUrlList().contains(dataVO.getExtraDataVO().getEventPictureUrl()))
                dataVO.getExtraDataVO().getEventPictureUrlList().add(dataVO.getExtraDataVO().getEventPictureUrl());

            return dataVO;

        },EventDataVO.class);
        return finalDataVO;
    }


    private TaskVo convertTask(XlmTaskInfo info, String bizCode) {
        TaskVo taskVo = $.copy(info, TaskVo.class);

        assert taskVo != null;

        if ($.isNull(info.getFormData())) {
            return taskVo;
        }
        taskVo.setContent(formatFormData(info.getTaskDefinitionKey(), info.getFormData(), bizCode));


        Map<String, Object> formData = info.getFormData();
        if (formData.containsKey(VariableConstants.handle_time) && $.isNotNull(formData.get(VariableConstants.handle_time))) {
            try {
                String time = String.valueOf(formData.get(VariableConstants.handle_time));
                if (time.contains("-")) {
                    taskVo.setEndTime(DateUtil.parse(time));
                } else {
                    long lt = new Long(time);
                    Date date = new Date(lt);

                    DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                    taskVo.setEndTime(format.parse(format.format(date)));
                }

            } catch (Exception e) {


                throw OperationFailedException.format(getMessage(EventTipKey.FailOptAnalysis) + formData.get(VariableConstants.handle_time));
            }
        }

        if (formData.containsKey(VariableConstants.originalHandleName) && $.isNotNull(formData.get(VariableConstants.originalHandleName))) {
            try {
                String originalHandleName = String.valueOf(formData.get(VariableConstants.originalHandleName));
                taskVo.getFormData().put(VariableConstants.handlerName, originalHandleName);
            } catch (Exception e) {


                throw OperationFailedException.format(getMessage(EventTipKey.FailOptAnalysis) + formData.get(VariableConstants.originalHandleName));
            }
        }
        if (formData.containsKey(VariableConstants.originalHandlePhone) && $.isNotNull(formData.get(VariableConstants.originalHandlePhone))) {
            try {
                String originalHandlePhone = String.valueOf(formData.get(VariableConstants.originalHandlePhone));
                taskVo.getFormData().put(VariableConstants.handlerPhone, originalHandlePhone);

            } catch (Exception e) {


                throw OperationFailedException.format(getMessage(EventTipKey.FailOptAnalysis) + formData.get(VariableConstants.originalHandlePhone));
            }
        }

        return taskVo;
    }

    private List<String> formatFormData(String taskDefinitionKey, Map<String, Object> formData, String bizCode) {
        // TODO: 优化Dict的取值
        // TODO: 严格校验formData的值
        if ($.isEmpty(formData)) return Collections.emptyList();


        if (formData.containsKey(VariableConstants.originalHandleName) && $.isNotNull(formData.get(VariableConstants.originalHandleName))) {
            try {
                String originalHandleName = String.valueOf(formData.get(VariableConstants.originalHandleName));
                formData.put(VariableConstants.handlerName, originalHandleName);
            } catch (Exception e) {


                throw OperationFailedException.format(getMessage(EventTipKey.FailOptAnalysis) + formData.get(VariableConstants.originalHandleName));
            }
        }
        if (formData.containsKey(VariableConstants.originalHandlePhone) && $.isNotNull(formData.get(VariableConstants.originalHandlePhone))) {
            try {
                String originalHandlePhone = String.valueOf(formData.get(VariableConstants.originalHandlePhone));
                formData.put(VariableConstants.handlerPhone, originalHandlePhone);

            } catch (Exception e) {
                throw OperationFailedException.format(getMessage(EventTipKey.FailOptAnalysis) + formData.get(VariableConstants.originalHandlePhone));
            }
        }


        List<String> content = new ArrayList<>();
        if (formData.containsKey(VariableConstants.handler_user_name) &&
                formData.containsKey(VariableConstants.handler_user_phone)) {

            String userName = (String) formData.get(VariableConstants.handler_user_name);
            String phone = (String) formData.get(VariableConstants.handler_user_phone);
            content.add($.format("{}:{}({})",  getMessage( EventTipKey.FlowViewPerson), userName, phone));
        }

        Map<String, String> handleMap = configContext.getDictMap(VariableConstants.handle_type);
        Map<String, String> judgeMap = configContext.getDictMap(VariableConstants.judge_result);
        Map<String, String> clientMap = configContext.getDictMap(VariableConstants.operatorClient);

        // ====================================================
//
//        if (formData.containsKey(VariableConstants.event_code)) {
//            String eventCode = (String) formData.get(VariableConstants.event_code);
//            content.add($.format("{}:{}", "事件类型", eventMap.get(eventCode)));
//        }

        if (formData.containsKey(VariableConstants.handle_type)) {
            String handleType = (String) formData.get(VariableConstants.handle_type);
            content.add($.format("{}:{}", getMessage("event.flow.handleType"), handleMap.get(handleType)));
        }


        if (formData.containsKey(VariableConstants.judge_result)) {

            System.out.println("VariableConstants.delay::->" +
                    ((Boolean) formData.get(VariableConstants.delay))
                    + "---->"
                    + formData.get(VariableConstants.delay)
                    + "====formData.containsKey(VariableConstants.delay)" + formData.containsKey(VariableConstants.delay)
            );

            if (formData.containsKey(VariableConstants.delay)
                    &&
                    ((Boolean) formData.get(VariableConstants.delay))
            ) {
                //delayDate
                String delayDate = (String) formData.get(VariableConstants.delay_date);
                content.add($.format("{}:{}", getMessage("event.flow.extend_date"), delayDate));
            } else {
                String judgeResult = (String) formData.get(VariableConstants.judge_result);
                content.add($.format("{}:{}", getMessage("event.flow.check"), getMessage(  "event.flow."+judgeResult)));
            }
        }

        if (formContainKeys(formData, VariableConstants.operatorClient)) {
            String operatorClient = String.valueOf(formData.get(VariableConstants.operatorClient));
            content.add($.format("{}:{}", "操作端", clientMap.get(operatorClient)));
        }


        if (formData.containsKey(VariableConstants.reject_reason)) {
            String rejectReason = (String) formData.get(VariableConstants.reject_reason);

            content.add($.format("{}:{}", getMessage(EventTipKey.FlowViewRejectReason), rejectReason));
        }
        if (formData.containsKey(VariableConstants.protal_content)) {
            String protal_content = (String) formData.get(VariableConstants.protal_content);

            content.add($.format("{}:{}", getMessage("event.flow.inspection_situation"), protal_content));
        }
        if (formData.containsKey(VariableConstants.handleReason)) {
            String handleReason = (String) formData.get(VariableConstants.handleReason);

            content.add($.format("{}:{}", getMessage("event.flow.handleqk"), handleReason));
        }
        if (formData.containsKey(VariableConstants.hasEvent)) {
            String hasEvent = (String) formData.get(VariableConstants.hasEvent);

            content.add($.format("{}:{}", getMessage("event.flow.event_has"), hasEvent.equals("1")?
                    getMessage("event.flow.yes"): getMessage("event.flow.no")));
        }


        if (formData.containsKey(VariableConstants.remark) && $.isNotNull(formData.get(VariableConstants.remark))) {

            //当前阶段是 市级初审的备注、市级复审的备注
            String remark = String.valueOf(formData.get(VariableConstants.remark));
            content.add($.format("{}:{}", "备注", remark));
        }

        if (formContainKeys(formData, VariableConstants.photoDescription)) {
            String photoDescription = String.valueOf(formData.get(VariableConstants.photoDescription));
            content.add($.format("{}:{}", "材料描述", photoDescription));
        }

        return content;
    }

    private boolean formContainKeys(Map<String, Object> formData, String key) {
        org.springframework.util.Assert.notEmpty(formData, getMessage(EventTipKey.NoneFormData));
        org.springframework.util.Assert.hasText(key, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneKey, Arrays.asList(EventTipKey.RequiredTip)));

        if (!formData.containsKey(key)) {
            return false;
        }

        if ($.isNull(formData.get(key))) {
            return false;
        }

        Object objValue = formData.get(key);

        if (objValue.getClass().equals(String.class) && $.isBlank((String) objValue)) {
            return false;
        }

        return true;
    }


    @Override
    public List<CountEventDataVO> countByArea() {

        List<CountEventDataVO> list =dataService.countByArea();
        list.forEach(item->{
            if(item.getName().equals(EventTipKey.UnknownAreaName)){
                item.setName(getMessage(EventTipKey.UnknownAreaName));
            }
        });

        return list;
    }

    @Override
    public List<CountEventDataVO> countByLabel() {
        return dataService.countByLabel();
    }
    @Override
    public List<CountEventDataVO> countByLabelOnDay() {
        return dataService.countByLabelOnDay();
    }

    @Override
    public CountEventDataOriginGroupVO countByOrigin(EventDataCountQueryDTO eventDataCountQueryDTO) {
        return dataService.countByOriginDataFetchWay(eventDataCountQueryDTO);
    }

    @Override
    public CountEventDataDealResultVO countByDealResult(EventDataCountQueryDTO eventDataCountQueryDTO) {
        return dataService.countByDealResult(eventDataCountQueryDTO);
    }

    @Override
    public FlyTaskReportVO queryFlyTaskReportVO(FlyTaskReportDTO flyTaskReportDTO) {

        FlyTaskReportVO flyTaskReportVO = new FlyTaskReportVO();

        List<FlyTaskReportBillCountVO> flyTaskReportBillCountVOList = dataService.queryFlyTaskReportVO(flyTaskReportDTO);

        if(ObjectUtil.isNotEmpty(flyTaskReportBillCountVOList)){

            flyTaskReportBillCountVOList.forEach(e->{
                flyTaskReportVO.getEventCountMap().put(e.getFlowState(),e.getBillCount());
            });

        }

        return flyTaskReportVO;
    }

    @Override
    public List<BillQueryCountVO> billQueryCount(BillQueryCountDTO billQueryCountDTO) {

        LmContextHolder.setTenantId(billQueryCountDTO.getTenantId().toString());

        List<BillQueryCountVO> list = new ArrayList<>();

        if(ObjectUtil.isNotEmpty(billQueryCountDTO.getOriginalDataLogoList())) {
            list = dataService.billQueryCount(billQueryCountDTO);
            list.forEach(item->{
                if (item.getGridName().equals(EventTipKey.UnknownAreaName)) {
                    item.setGridName(getMessage(EventTipKey.UnknownAreaName));
                }
            });
        }

        return list;
    }

    @Override
    public List<BillQueryListItemVO> billQueryList(BillQueryCountDTO billQueryCountDTO) {
        LmContextHolder.setTenantId(billQueryCountDTO.getTenantId().toString());

        List<BillQueryListItemVO> list = new ArrayList<>();

        if(ObjectUtil.isNotEmpty(billQueryCountDTO.getOriginalDataLogoList())) {
            list = dataService.billQueryList(billQueryCountDTO);
            list.forEach(item->{
                if (item.getGridName().equals(EventTipKey.UnknownAreaName)) {
                    item.setGridName(getMessage(EventTipKey.UnknownAreaName));
                }
            });
        }

        return list;
    }
}
