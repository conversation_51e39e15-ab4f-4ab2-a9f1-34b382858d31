package com.mascj.lup.event.bill.support;

/**
 * <AUTHOR>
 * @date 2024/10/31 18:18
 * @describe
 */
import cn.hutool.json.JSONUtil;
import com.mascj.lup.event.bill.constant.Constants;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.EventDataDTO;
import com.mascj.lup.event.bill.service.IDataReceiveService;
import com.mascj.lup.event.bill.util.RedisOpsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MqServiceListener {

    @Autowired
    private IDataReceiveService dataReceiveService;

    @RabbitListener(queues = VariableConstants.RabbitMQQueueNameForReceiveEventDataFromDimension,concurrency = "3")
    public void receiveMessage(String message) {
        log.info("Received message: " + message);
        // 在这里处理接收到的消息
        processMessage(message);
    }

    private void processMessage(String message) {
        // 在这里实现消息处理逻辑
        log.info("Processing message: " + message);


        try {
            EventDataDTO dataDTO = JSONUtil.toBean(message, EventDataDTO.class);

            if ( dataDTO != null && dataDTO.getOriginalDataLogo() != null) {
                dataReceiveService.dealReceiveData(dataDTO);
            }


        }catch (Exception exp){

        }
        // 例如：解析消息、更新数据库、发送通知等
    }


}

