package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.dto.LupBillPendingApplyDTO;
import com.mascj.lup.event.bill.dto.LupBillPendingApprovalDTO;
import com.mascj.lup.event.bill.dto.LupBillPendingOldDataDTO;
import com.mascj.lup.event.bill.entity.LupBillPending;
import com.mascj.lup.event.bill.entity.LupSource;
import com.mascj.lup.event.bill.vo.LupBillPendingDataItemVO;
import com.mascj.lup.event.bill.vo.SourceTileVO;
import com.mascj.lup.event.vo.AchievementPreviewVO;

import java.util.List;
import java.util.Map;


public interface ILupBillPendingService extends IService<LupBillPending> {

    Boolean applyPending(LupBillPendingApplyDTO applyDTO);

    Boolean approval(LupBillPendingApprovalDTO approvalDTO);

    Boolean resetBillPending(Long billId);

    Boolean dealHistoryPendingBill(LupBillPendingOldDataDTO billPendingOldDataDTO);


    Map<String,List<LupBillPendingDataItemVO>> mapLupBillPendingDataItemVOByFlowTaskId(Long billId);

    void clearBillCache(Long billId);

}
