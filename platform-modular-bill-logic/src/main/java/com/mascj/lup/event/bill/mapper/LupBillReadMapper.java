package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.LupBillRead;
import com.mascj.lup.event.bill.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工单数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface LupBillReadMapper extends BaseMapper<LupBillRead> {

    BillReadStateCounterVO countBillUnReadCount(@Param("userId")Long userId
            ,@Param("dataDTO") BillReadStateCounterDTO readStateCounterDTO);

    Integer  countByUserId(@Param("userId")Long userId,@Param("dataDTO")BillReadStateDTO billReadStateDTO);
}
