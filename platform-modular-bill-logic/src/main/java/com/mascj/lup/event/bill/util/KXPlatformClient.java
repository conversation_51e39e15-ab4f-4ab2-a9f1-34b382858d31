package com.mascj.lup.event.bill.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
public class KXPlatformClient {

    /**
     * Get方法
     */
    private static final String GET_METHOD = "GET";
    /**
     * Post方法
     */
    private static final String POST_METHOD = "POST";
    /**
     * Put方法
     */
    private static final String PUT_METHOD = "PUT";
    /**
     * Delete方法
     */
    private static final String DELETE_METHOD = "DELETE";

    private String host;
    private String appKey;
    private String appSecret;


    public KXPlatformClient(String host, String appKey, String appSecret) {
        this.host = host;
        this.appKey = appKey;
        this.appSecret = appSecret;
    }

//    public Communicate communicate() {
//        return new Communicate(this);
//    }

    /**
     * 统一请求方法
     *
     * @param requestUrl    请求的地址
     * @param requestMethod 请求方法的类型
     * @param headerMap     请求头map
     * @param requestParams 请求参数
     * @return
     */
    public static String doRequest(String requestUrl, String requestMethod, MultiValueMap<String, String> headerMap, Object requestParams, String contentType) {
        RestTemplate restTemplate = new RestTemplate();

        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(300000); // 设置连接超时时间为300秒
        requestFactory.setReadTimeout(500000); // 设置读取超时时间为500秒
        restTemplate.setRequestFactory(requestFactory);

        String result = null;
        HttpHeaders headers = new HttpHeaders();
        try {
            switch (requestMethod) {
                case GET_METHOD:
                    // GET方法
                    Map<String, String> paramMap = JSONObject.parseObject(JSON.toJSONString(requestParams), Map.class);
                    requestUrl = handleGetParam(requestUrl, paramMap);
                    //设置请求头
                    headers.addAll(headerMap);
                    ResponseEntity<String> getResponse = restTemplate.getForEntity(requestUrl, String.class, headers);
                    result = getResponse.getBody();
                    break;
                case POST_METHOD:
                    // POST方法（JSON形式）
                    //设置请求头
                    headers.setContentType(MediaType.valueOf(contentType));
                    headers.addAll(headerMap);
                    //body参数
                    String postJsonRequestBody = JSON.toJSONString(requestParams);
                    HttpEntity<String> postJsonRequestEntity = new HttpEntity<>(postJsonRequestBody, headers);
                    ResponseEntity<String> postJsonResponse = restTemplate.postForEntity(requestUrl, postJsonRequestEntity, String.class);
                    HttpHeaders responseHeaders = postJsonResponse.getHeaders();
                    log.info(JSON.toJSONString(responseHeaders));
                    result = postJsonResponse.getBody();
                    break;
                case PUT_METHOD:
                    // PUT方法（JSON形式）
                    //设置请求头
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    headers.addAll(headerMap);
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    String putJsonRequestBody = JSON.toJSONString(requestParams);
                    HttpEntity<String> putJsonRequestEntity = new HttpEntity<>(putJsonRequestBody, headers);
                    // 发送PUT请求并设置请求头
                    ResponseEntity<String> putRes = restTemplate.exchange(requestUrl, HttpMethod.PUT, putJsonRequestEntity, String.class);
                    // 获取响应结果
                    result = putRes.getBody();
                    break;
                case DELETE_METHOD:
                    // DELETE方法
                    //设置请求头
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    headers.addAll(headerMap);
                    // 创建HttpEntity对象并设置请求头
                    HttpEntity<String> entity = new HttpEntity<>(headers);
                    // 发送DELETE请求并设置请求头
                    ResponseEntity<String> deleteRes = restTemplate.exchange(requestUrl, HttpMethod.DELETE, entity, String.class);
                    // 获取响应结果
                    result = deleteRes.getBody();
                    break;
                default:
                    throw new RuntimeException("error");
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof HttpClientErrorException){
                HttpClientErrorException exception = (HttpClientErrorException) e ;
                String responseBodyAsString = exception.getResponseBodyAsString();
                return  responseBodyAsString;
            }else {
                throw new RuntimeException(e.getMessage());
            }

        }
    }

    /**
     * 根据url 和 map参数，生成最终的get地址
     *
     * @param baseUrl
     * @param params
     * @return
     */
    public static String handleGetParam(String baseUrl, Map<String, String> params) {
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        if (!params.isEmpty()) {
            urlBuilder.append("?");
            for (Map.Entry<String, String> entry : params.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                urlBuilder.append(key).append("=").append(value).append("&");
            }
            urlBuilder.deleteCharAt(urlBuilder.length() - 1);
        }
        String result = urlBuilder.toString();
        return result;
    }

    /**
     * 获取accessToken
     *
     * @return
     */
    private String getAccessToken() {
        MultiValueMap<String, String> headerMap = new HttpHeaders();
        Map<String, Object> gatewayAccessTokenParam = new HashMap<>();

        gatewayAccessTokenParam.put("appKey", appKey);
        long timestamp = System.currentTimeMillis();
        gatewayAccessTokenParam.put("timestamp", timestamp);
        String nonce = UUID.randomUUID().toString();
        gatewayAccessTokenParam.put("nonce", nonce);
        String signature = null;
        //获取签名
        try {
            signature = SignatureUtils.signature(appKey, appSecret, timestamp, nonce);
        } catch (Exception e) {
            throw new RuntimeException(LocaleMessageUtil.getMessage(EventTipKey.FailGenerateSignature));
        }
        gatewayAccessTokenParam.put("signature", signature);
        String requestUrl = host + "/auth/getToken";
        String response = doRequest(requestUrl, POST_METHOD, headerMap, gatewayAccessTokenParam, "application/json");
        JSONObject resJson = JSON.parseObject(response);
        Integer status = resJson.getInteger("status");
        if (status == 200) {
            String token = resJson.getJSONObject("data").getString("token");
            return token;
        } else {
            String message = resJson.getString("message");
            throw new RuntimeException(message);
        }
    }

    /**
     * 请求网关的方法
     * 第三方只需要调用这个方法就行了
     *
     * @param uri
     * @param requestApi
     * @param params
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public String gatewayRequest(String uri, String requestApi, Object params) throws NoSuchAlgorithmException, InvalidKeyException {
        String requestUrl = host + uri;
        String accessToken = this.getAccessToken();
        //设置请求头
        MultiValueMap<String, String> headerMap = new HttpHeaders();
        long timestamp = System.currentTimeMillis();
        String nonce = UUID.randomUUID().toString().replaceAll("-", "");
        headerMap.set("Request-Origin-AppKey", appKey);
        headerMap.set("Request-Api", requestApi);
        headerMap.set("Authorization", accessToken);

        headerMap.set("appId", appKey);

        headerMap.set("appSecret", appSecret);

        headerMap.set("Timestamp", String.valueOf(timestamp));
        headerMap.set("Nonce", nonce);
        String signature = SignatureUtils.methodSignature(accessToken, appKey, appSecret, timestamp, nonce, requestApi, JSONObject.toJSONString(params));
        headerMap.set("Signature", signature);

        String result = doRequest(requestUrl, POST_METHOD, headerMap, params, "application/json");
        return result;
    }


    public String gatewayRequestDefault(String uri,String requestApi, Object params) throws NoSuchAlgorithmException, InvalidKeyException {
        String requestUrl = host + uri;

        //设置请求头
        MultiValueMap<String, String> headerMap = new HttpHeaders();
        long timestamp = System.currentTimeMillis();
        String nonce = UUID.randomUUID().toString().replaceAll("-", "");
        headerMap.set("Request-Origin-AppKey", appKey);
        headerMap.set("Request-Api", requestApi);

        headerMap.set("appId", appKey);

        headerMap.set("Timestamp", String.valueOf(timestamp));
        headerMap.set("Nonce", nonce);
        String signature = SignatureUtils.methodSignature(appKey, appKey, appSecret, timestamp, nonce, requestApi, JSONObject.toJSONString(params));
        headerMap.set("Signature", signature);

        String result = doRequest(requestUrl, POST_METHOD, headerMap, params, "application/json");
        return result;
    }
    public String gatewayRequestGH(String uri, String requestApi, Object params) throws NoSuchAlgorithmException, InvalidKeyException {
        String requestUrl = host + uri;

        //设置请求头
        MultiValueMap<String, String> headerMap = new HttpHeaders();
        long timestamp = System.currentTimeMillis();
        String nonce = UUID.randomUUID().toString().replaceAll("-", "");
        headerMap.set("Request-Origin-AppKey", appKey);
        headerMap.set("Request-Api", requestApi);


        headerMap.set("appId", appKey);

        headerMap.set("appSecret", appSecret);

        headerMap.set("Timestamp", String.valueOf(timestamp));
        headerMap.set("Nonce", nonce);
        String signature = SignatureUtils.methodSignature(appKey, appKey, appSecret, timestamp, nonce, requestApi, JSONObject.toJSONString(params));
        headerMap.set("Signature", signature);

        String result = doRequest(requestUrl, POST_METHOD, headerMap, params, "application/json");
        return result;
    }

}

