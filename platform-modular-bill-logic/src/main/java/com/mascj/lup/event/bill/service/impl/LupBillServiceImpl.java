package com.mascj.lup.event.bill.service.impl;

import cc.lyiot.framework.common.pojo.CommonResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.esri.core.geometry.OperatorExportToWkt;
import com.esri.core.geometry.Point;
import com.esri.core.geometry.WktExportFlags;
import com.mascj.event.dimension.enumeration.ModuleDataSourceTypeEnum;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.constant.Oauth2Constant;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.exception.ServiceException;
import com.mascj.kernel.common.util.$;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.common.util.StringPool;
import com.mascj.kernel.redis.core.RedisService;
import com.mascj.kernel.tools.RedisKey;
import com.mascj.kernel.tools.RedisPlane;
import com.mascj.kernel.tools.SetFunction;
import com.mascj.kernel.tools.linq.Enumerable;
import com.mascj.kernel.tools.linq.Linqs;
import com.mascj.lup.cds.djicloud.feign.WaylineJobFeign;
import com.mascj.lup.cds.djicloud.wayline.dto.WaylineJobInfoDTO;
import com.mascj.lup.cds.djicloud.wayline.vo.WaylineJobInfoVO;
import com.mascj.lup.cds.survey.feign.SurveyAchievementFeign;
import com.mascj.lup.datacenter.client.feign.DataCenterClientTaskFeign;
import com.mascj.lup.datacenter.client.vo.res.AchievementClientPreviewVO;
import com.mascj.lup.datacenter.dto.MarkDTO;
import com.mascj.lup.datacenter.feign.LupMarkCenterFeign;
import com.mascj.lup.event.bill.constant.Constants;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.*;
import com.mascj.lup.event.bill.enums.*;
import com.mascj.lup.event.bill.exceptions.OperationFailedException;
//import com.mascj.lup.event.bill.feign.IFileProvider;
import com.mascj.lup.event.bill.geo.GeoTile;
import com.mascj.lup.event.bill.handler.MetadataHandler;
import com.mascj.lup.event.bill.mapper.LupBillMapper;
import com.mascj.lup.event.bill.mapper.LupDataMapper;
import com.mascj.lup.event.bill.mapper.LupGridUnitMapper;
import com.mascj.lup.event.bill.service.*;
import com.mascj.lup.event.bill.support.LupExecutionContext;
import com.mascj.lup.event.bill.util.*;
import com.mascj.lup.event.bill.util.clip.ClipTailsUtil;
import com.mascj.lup.event.bill.util.clip.ShapeJson;
import com.mascj.lup.event.bill.vo.*;
import com.mascj.lup.event.bill.vo.config.HandleModuleConfigVO;
import com.mascj.lup.event.feign.EventStreamFeign;
import com.mascj.lup.event.vo.AchievementPreviewVO;
import com.mascj.platform.system.entity.Dict;
import com.mascj.platform.system.feign.ISysDictProvider;
import com.mascj.platform.system.feign.ISysUserProvider;
import com.mascj.support.config.dto.XlmActivityState;
import com.mascj.support.config.feign.IAepConfigProvider;
import com.mascj.support.config.feign.IConfigValueProvider;
import com.mascj.support.config.vo.ConfigInVo;
import com.mascj.support.config.vo.meta.MetaTag;

import com.mascj.support.file.api.feign.IFileProvider;
import com.mascj.support.file.api.model.XlmFile;
import com.mascj.support.workflow.dto.XlmQueryBaseTaskDto;
import com.mascj.support.workflow.dto.XlmQueryHisTaskDto;
import com.mascj.support.workflow.entity.XlmTaskInfo;
import com.mascj.support.workflow.feign.IXlmQueryProvider;
import io.jsonwebtoken.lang.Assert;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.mascj.kernel.common.util.LocaleMessageUtil.getMessage;

/**
 * <p>
 * 工单数据表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
@AllArgsConstructor
@Slf4j
public class LupBillServiceImpl extends ServiceImpl<LupBillMapper, LupBill> implements ILupBillService {

    private final ILupGridUserService gridUserService;
    private final ILupBatchSourceService iLupBatchSourceService;
    private final IAepConfigProvider configProvider;
    private final DataCenterClientTaskFeign streamProvider;
    private final ILupFlowEntityService iLupFlowEntityService;
    private final LupGridUnitMapper gridUnitMapper;
    private final ILupFlowService lupFlowService;
    private final MetadataHandler metadataHandler;
    private final  ISysUserProvider sysUserProvider;
    private final IXlmQueryProvider xlmQueryProvider;
    private final LupExecutionContext configContext;

    private final IFileProvider fileProvider;

    private final ILupBillSwitchGridService switchGridService;

    private final HttpServletResponse response;

    private final ISysDictProvider dictProvider;

    private final LupMarkCenterFeign lupMarkCenterFeign;

    private final WaylineJobFeign waylineJobFeign;

    private final LupEventMarkService lupEventMarkService;

    private final ILupDataService dataService;

    private final LupDataMapper lupDataMapper;

    private final LupEventLabelService lupEventLabelService;

    private static final ReentrantLock reentrantLock = new ReentrantLock();

    private final ReadConfigUtil readConfigUtil;
    @Autowired
    private IConfigValueProvider configValueProvider;

    private final SurveyAchievementFeign surveyAchievementFeign;

    private volatile static int numberIndex = 0;


    private final ILupBillUsableService billUsableService;

    private final ILupBillUsableFileService billUsableFileService;

    private final ILupBillPendingService pendingService;

    private final ILupLabelService labelService;
    private final ILupDataLabelService dataLabelService;

    private final RedisPlane redisPlane;

    @Override
    public Result<List<BillCountVO>> countBill(ListUnitDTO billDTO) {

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();

        //查询流程数据
        Map<Integer, XlmActivityState> activityStateMap = Linqs.of(configProvider.getActivityList(billDTO.getBizCode()).getData()).toMap(XlmActivityState::getState);

        prepareBillDTO(billDTO);
        List<Long> userId = new ArrayList();
        userId.add(LmContextHolder.getUserId());
        billDTO.setLabelIdListPerm(lupEventLabelService.labelIdsByUserIds(userId));

        List<BillCountVO> list = baseMapper.countBill(billDTO);
        list.forEach(item -> {
            XlmActivityState activityState = activityStateMap.get(item.getFlowState());

            if (activityState != null)
                item.setFlowStateName(activityState.getName());

        });


        if (handleModuleConfigVO.getEventHangingSwitch()) {

            billDTO.setPendingState(PendingStateEnum.PendingStateEnumViewingAccess.getPendingState());

            List<BillCountVO> listForPending = baseMapper.countBillForPending(billDTO);
            for (BillCountVO item : listForPending) {

                item.setFlowStateName(LocaleMessageUtil.getMessage(EventTipKey.EventPending));
                list.add(item);

            }
        }



        return Result.data(list);
    }

    private void prepareBillDTO(BillDTO dto){
        dto.setProjectId(ProjectUtil.getProjectId());
        if(dto.getDateEnd() != null && !"".equals(dto.getDateEnd())){

            dto.setDateEnd(
                    dto.getDateEnd() + " 23:59:59"
            );
        }

        dto.setGridUnitIdList(gridUserService.dataScope(LmContextHolder.getUserId()));
    }

    @Override
    public List<LupBillCountVo> countBillByGrid(BillDTO billDTO) {

        prepareBillDTO(billDTO);

        List<Long> userId = new ArrayList();
        userId.add(LmContextHolder.getUserId());
        billDTO.setLabelIdListPerm(lupEventLabelService.labelIdsByUserIds(userId));


        return baseMapper.countBillByGrid(billDTO);
    }

    @Override
    public List<LupBillCountByFlowStateVo> countBillByFlowState(Long userId,List<Long> gridIdList, List<Integer> flowStateList) {
        List<Long> userIdList = new ArrayList();
        userIdList.add(userId);
        List<Long> labelIdListPerm = lupEventLabelService.labelIdsByUserIds(userIdList);

        return baseMapper.countBillByFlowState(labelIdListPerm,gridIdList,flowStateList);
    }

    private final ILupBillReadService billReadService;

    @Override
    public IPage<BillSearchVO> pagedBill(IPage page, BillSearchDTO taskSearchDTO) {

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();

        //查询流程数据
        Map<Integer,XlmActivityState> activityStateMap = Linqs.of(configProvider.getActivityList(taskSearchDTO.getBizCode()).getData()).toMap(XlmActivityState::getState);

        prepareBillSearchDTO(taskSearchDTO);

        List<Long> userId = new ArrayList<>();
        userId.add(LmContextHolder.getUserId());
        taskSearchDTO.setLabelIdListPerm(lupEventLabelService.labelIdsByUserIds(userId));

        if ($.isNotBlank(taskSearchDTO.getLngLat())) {
            if (!Pattern.matches("^\\[[0-9.]+,[0-9.]+\\]$", taskSearchDTO.getLngLat())) {
                Assert.isTrue(false, LocaleMessageUtil.getMessage(EventTipKey.ErrorGisFormat));
            }

            JSONArray objects = JSON.parseArray(taskSearchDTO.getLngLat());
            Double lat = Double.parseDouble(objects.get(0) + "");
            Double lng = Double.parseDouble(objects.get(1) + "");

            Point queryPoint = new Point(lng,lat);

            String geoPoint = OperatorExportToWkt.local().execute(WktExportFlags.wktExportPoint, queryPoint, null);

            taskSearchDTO.setLngLatGeometry(geoPoint);
        }


        Long projectId = ProjectUtil.getProjectId();

        List<BillSearchVO> records = baseMapper.pagedBill(page, taskSearchDTO,projectId, LmContextHolder.getTenantId());
        Map<String,String> eventOriginalMap = fetchEventOriginal();
        records.forEach(item->{

            item.setEventOrigin(
                    eventOriginalMap.get(item.getDataOrigin()+"")
//                    DataOrigin.parseToName(item.getDataOrigin())
            );
            item.setDistanceString(item.getDistance() +
                    LocaleMessageUtil.getMessage(EventTipKey.UnitMeter)
                    );//LocaleMessageUtil.getMessage("event.bill.distance.unit.meter"));//
            if (item.getDistance() >= 1000) {
                item.setDistanceString(item.getDistance() / 1000 + LocaleMessageUtil.getMessage(EventTipKey.UnitKilometer));//LocaleMessageUtil.getMessage("event.bill.distance.unit.kilometer"));//
            }

            if(item.getGridName().equals(EventTipKey.UnknownAreaName)) {
                item.setAreaName(LocaleMessageUtil.getMessage(item.getAreaName()));
                item.setGridName(LocaleMessageUtil.getMessage(item.getGridName()));
            }


            XlmActivityState activityState =  activityStateMap.get(item.getFlowState());

            if(activityState!=null)
                item.setFlowStateName(activityState.getName());

            BillReadStateDTO billReadStateDTO = new BillReadStateDTO();
            billReadStateDTO.setFlowId(item.getFlowEntityId());
            billReadStateDTO.setProcessTaskId(item.getProcessTaskId());
            item.setRead(billReadService.countByUserId(billReadStateDTO) > 0);

            if(handleModuleConfigVO.getEventHangingSwitch()
                    && item.getPendingState() == PendingStateEnum.PendingStateEnumViewingAccess.getPendingState()){
                item.setDeadLine(null);
            }

        });

        return page.setRecords(records);
    }

    @Override
    public List<BillDataVO> listBill(ListUnitDTO unitDTO) {

        prepareBillDTO(unitDTO);

        List<Long> userId = new ArrayList();
        userId.add(LmContextHolder.getUserId());
        unitDTO.setLabelIdListPerm(lupEventLabelService.labelIdsByUserIds(userId));


        if( unitDTO.getUnitId() == null)
            return baseMapper.listBillOnlyLocation(unitDTO,ProjectUtil.getProjectId());
        return baseMapper.listBill(unitDTO,ProjectUtil.getProjectId());
    }

    /**
     * 根据数据插入工单
     *
     * @param data
     * @return
     */
    @Override
    public Result<LupBillVO> addBill(LupData data) {
        LupBillVO bill = new LupBillVO();
        reentrantLock.lock();
        try {

            bill.setHappenedTime(data.getHappenedTime());
            bill.setDataId(data.getId());
            bill.setProjectId(data.getProjectId());
            bill.setDataOrigin(data.getDataOrigin());

            bill.setBillNumber(GenerateBillNumber(data.getProjectId()));
            //定位事件网格
            Point queryPoint = new Point(Double.parseDouble(data.getLocationLng().toString()), Double.parseDouble(data.getLocationLat().toString()));
            String centerPoint = OperatorExportToWkt.local().execute(WktExportFlags.wktExportPoint, queryPoint, null);
            List<LupGridUnitVO> unitList = gridUnitMapper.findGridByPoint(data.getProjectId(), centerPoint);

//            Assert.isTrue(unitList.size() > 0, LocaleMessageUtil.getMessage(EventTipKey.ErrorGridLocate));

            if(unitList.size() > 0){
            for (LupGridUnitVO a : unitList) {
                a.setCodeLength(a.getCode().length());
            }

            int maxCodeLength = unitList.stream()
                    .mapToInt(LupGridUnitVO::getCodeLength)
                    .max()
                    .orElseThrow(() -> new IllegalStateException("List is empty"));
            List<LupGridUnitVO> maxUnits = unitList.stream()
                    .filter(unit -> unit.getCodeLength() == maxCodeLength)
                    .collect(Collectors.toList());

            if(maxUnits != null && maxUnits.size()>0) {
                LupGridUnitVO unit = maxUnits.get(0);
//                    unitList.stream().max(Comparator.comparing(LupGridUnitVO::getCodeLength)).get();

                if (unit != null) {
                    bill.setGridCode(unit.getCode());
                    bill.setGridName(unit.getName());

                    bill.setGridUnitId(unit.getId());

                    if(unit.getPid() ==0){
                        bill.setAreaCode(unit.getCode());
                        bill.setAreaName(unit.getName());
                    }else{
                        LupGridUnit lupGridUnit = gridUnitMapper.selectById(unit.getPid());
                        bill.setAreaCode(lupGridUnit.getCode());
                        bill.setAreaName(lupGridUnit.getName());
                    }

                }
            }
            }else {
                List<LupGridUnit> lupGridUnitList = gridUnitMapper.selectList(Wrappers.<LupGridUnit>lambdaQuery()
                        .eq(LupGridUnit::getIsDeleted,0)

                                .eq(LupGridUnit::getCode,VariableConstants.UNKNOWN_AREA_CODE)
                        .orderByAsc(LupGridUnit::getId)
                        .last(" limit 1")
                        );

                if(lupGridUnitList.size()>0){
                    LupGridUnit lupGridUnit = lupGridUnitList.get(0);
                    bill.setGridCode(lupGridUnit.getCode());
                    bill.setGridName(EventTipKey.UnknownAreaName);
                    bill.setGridUnitId(lupGridUnit.getId());
                    bill.setAreaCode(lupGridUnit.getCode());
                    bill.setAreaName(EventTipKey.UnknownAreaName);
                }else
                {
                    bill.setGridCode(VariableConstants.UNKNOWN_AREA_CODE);
                    bill.setGridName(EventTipKey.UnknownAreaName);

                    bill.setGridUnitId(0L);
                    bill.setAreaCode(VariableConstants.UNKNOWN_AREA_CODE);
                    bill.setAreaName(EventTipKey.UnknownAreaName);

                }
            }



            save(bill);
        }
        catch (Exception exp){
            exp.printStackTrace();
        }
        finally {
            reentrantLock.unlock();
            Result<String> stringResult = lupFlowService.startupAepFlow(bill.getId(), null);
            bill.setProcessInstanceId(stringResult.getData());
        }
        return Result.data(bill);
    }

    @Override
    public TaskVo convertTask(XlmTaskInfo info, String bizCode) {
        TaskVo taskVo = $.copy(info, TaskVo.class);

        assert taskVo != null;

        List<LupBillSwitchGrid> lupBillSwitchGridList = switchGridService.list(Wrappers.<LupBillSwitchGrid>lambdaQuery()
                .eq(LupBillSwitchGrid::getProcessTaskId,taskVo.getId()).orderByDesc(LupBillSwitchGrid::getCreateTime));

        if ($.isNull(info.getFormData())) {
            if(ObjectUtil.isNotEmpty(lupBillSwitchGridList)) {
                List<String> content = new ArrayList();
                formatFormDataForOperate(content, lupBillSwitchGridList);
                taskVo.setContent(content);
            }
            return taskVo;
        }


        taskVo.setContent(formatFormData(info.getTaskDefinitionKey(), info.getFormData(), bizCode, info.getName(), lupBillSwitchGridList));


        Map<String, Object> formData = info.getFormData();
        if (formData.containsKey(VariableConstants.handle_time) && $.isNotNull(formData.get(VariableConstants.handle_time))) {
            try {
                String time = String.valueOf(formData.get(VariableConstants.handle_time));
                if (time.contains("-")) {
                    taskVo.setEndTime(DateUtil.parse(time));
                } else {
                    long lt = new Long(time);
                    Date date = new Date(lt);

                    DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                    taskVo.setEndTime(format.parse(format.format(date)));
                }

            } catch (Exception e) {


                throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailOptAnalysis) + formData.get(VariableConstants.handle_time));
            }
        }

        if (formData.containsKey(VariableConstants.originalHandleName) && $.isNotNull(formData.get(VariableConstants.originalHandleName))) {
            try {
                String originalHandleName = String.valueOf(formData.get(VariableConstants.originalHandleName));
                taskVo.getFormData().put(VariableConstants.handlerName, originalHandleName);
            } catch (Exception e) {


                throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailOptAnalysis) + formData.get(VariableConstants.originalHandleName));
            }
        }
        if (formData.containsKey(VariableConstants.originalHandlePhone) && $.isNotNull(formData.get(VariableConstants.originalHandlePhone))) {
            try {
                String originalHandlePhone = String.valueOf(formData.get(VariableConstants.originalHandlePhone));
                taskVo.getFormData().put(VariableConstants.handlerPhone, originalHandlePhone);

            } catch (Exception e) {


                throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailOptAnalysis) + formData.get(VariableConstants.originalHandlePhone));
            }
        }

        return taskVo;
    }

    private List<String> formatFormData(String taskDefinitionKey, Map<String, Object> formData, String bizCode,String name,List<LupBillSwitchGrid> lupBillSwitchGridList) {
        // TODO: 优化Dict的取值
        // TODO: 严格校验formData的值
        if ($.isEmpty(formData)) return Collections.emptyList();


        if (formData.containsKey(VariableConstants.originalHandleName) && $.isNotNull(formData.get(VariableConstants.originalHandleName))) {
            try {
                String originalHandleName = String.valueOf(formData.get(VariableConstants.originalHandleName));
                formData.put(VariableConstants.handlerName, originalHandleName);
            } catch (Exception e) {


                throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailOptAnalysis) + formData.get(VariableConstants.originalHandleName));
            }
        }
        if (formData.containsKey(VariableConstants.originalHandlePhone) && $.isNotNull(formData.get(VariableConstants.originalHandlePhone))) {
            try {
                String originalHandlePhone = String.valueOf(formData.get(VariableConstants.originalHandlePhone));
                formData.put(VariableConstants.handlerPhone, originalHandlePhone);

            } catch (Exception e) {
                throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailOptAnalysis) + formData.get(VariableConstants.originalHandlePhone));
            }
        }


        List<String> content = new ArrayList<>();
        if (formData.containsKey(VariableConstants.handler_user_name) &&
                formData.containsKey(VariableConstants.handler_user_phone)) {

            String userName = (String) formData.get(VariableConstants.handler_user_name);
            String phone = (String) formData.get(VariableConstants.handler_user_phone);
            content.add($.format("{}:{}({})",name+ LocaleMessageUtil.getMessage(EventTipKey.FlowViewPerson), userName, phone));
        }

        Map<String, String> handleMap = configContext.getDictMap(VariableConstants.handle_type);
        Map<String, String> judgeMap = configContext.getDictMap(VariableConstants.judge_result);
        Map<String, String> clientMap = configContext.getDictMap(VariableConstants.operatorClient);

        // ====================================================
//
//        if (formData.containsKey(VariableConstants.event_code)) {
//            String eventCode = (String) formData.get(VariableConstants.event_code);
//            content.add($.format("{}:{}", "事件类型", eventMap.get(eventCode)));
//        }

        if (formData.containsKey(VariableConstants.handle_type)) {
            String handleType = (String) formData.get(VariableConstants.handle_type);
            content.add($.format("{}:{}", LocaleMessageUtil.getMessage("event.flow.handleType"), handleMap.get(handleType)));
        }


        if (formData.containsKey(VariableConstants.judge_result)) {

            System.out.println("VariableConstants.delay::->" +
                    ((Boolean) formData.get(VariableConstants.delay))
                    + "---->"
                    + formData.get(VariableConstants.delay)
                    + "====formData.containsKey(VariableConstants.delay)" + formData.containsKey(VariableConstants.delay)
            );

            if (formData.containsKey(VariableConstants.delay)
                    &&
                    ((Boolean) formData.get(VariableConstants.delay))
            ) {
                //delayDate
                String delayDate = (String) formData.get(VariableConstants.delay_date);
                content.add($.format("{}:{}", LocaleMessageUtil.getMessage("event.flow.extend_date"), delayDate));
            } else {
                String judgeResult = (String) formData.get(VariableConstants.judge_result);
                content.add($.format("{}:{}", LocaleMessageUtil.getMessage(EventTipKey.FlowViewResult),
                        LocaleMessageUtil.getMessage(  "event.flow."+judgeResult)));
            }
        }

        if (formContainKeys(formData, VariableConstants.operatorClient)) {
            String operatorClient = String.valueOf(formData.get(VariableConstants.operatorClient));
            content.add($.format("{}:{}", LocaleMessageUtil.getMessage(EventTipKey.CommentOperationClient), clientMap.get(operatorClient)));
        }


        if (formData.containsKey(VariableConstants.reject_reason)) {
            String rejectReason = (String) formData.get(VariableConstants.reject_reason);

            content.add($.format("{}:{}", LocaleMessageUtil.getMessage(EventTipKey.FlowViewRejectReason), rejectReason));
        }
            if (formData.containsKey(VariableConstants.protal_content)) {
            String protal_content = (String) formData.get(VariableConstants.protal_content);

            content.add($.format("{}:{}", LocaleMessageUtil.getMessage("event.flow.inspection_situation"), protal_content));
        }
            if (formData.containsKey(VariableConstants.handleReason)) {
            String handleReason = (String) formData.get(VariableConstants.handleReason);

            content.add($.format("{}:{}", LocaleMessageUtil.getMessage("event.flow.handleqk"), handleReason));
        }
            if (formData.containsKey(VariableConstants.hasEvent)) {
            String hasEvent = (String) formData.get(VariableConstants.hasEvent);

            content.add($.format("{}:{}", LocaleMessageUtil.getMessage("event.flow.event_has"), hasEvent.equals("1")?
                    LocaleMessageUtil.getMessage("event.flow.yes"): LocaleMessageUtil.getMessage("event.flow.no")));
        }


            //处理四平台数据
        formData.forEach((k,v)->{
            if (k.contains("four_")){
                content.add(v.toString());
            }

        });


        if (formData.containsKey(VariableConstants.remark) && $.isNotNull(formData.get(VariableConstants.remark))) {

            //当前阶段是 市级初审的备注、市级复审的备注
                String remark = String.valueOf(formData.get(VariableConstants.remark));
                content.add($.format("{}:{}", LocaleMessageUtil.getMessage(EventTipKey.CommentRemark), remark));
        }

        if (formContainKeys(formData, VariableConstants.photoDescription)) {
            String photoDescription = String.valueOf(formData.get(VariableConstants.photoDescription));
            content.add($.format("{}:{}", LocaleMessageUtil.getMessage(EventTipKey.CommentSourceDescribe), photoDescription));
        }

        formatFormDataForOperate(content,lupBillSwitchGridList);


        return content;
    }

    private List<String> formatFormDataForOperate(
            List<String> content,
            List<LupBillSwitchGrid> lupBillSwitchGridList) {


        if(ObjectUtil.isNotEmpty(lupBillSwitchGridList)){
            lupBillSwitchGridList.forEach(item->{
                switch (item.getOperateType())
                {
                    case 1:



                        content.add(
                                $.format("{}【{}】:{}",
                                        DateUtil.format(item.getCreateTime(),"yyyy-MM-dd HH:mm:ss"),"网格" ,
                                        item.getOriginalGridName()+"→"+item.getTargetGridName()
                                ));
                        break;
                    case 2:
                        content.add(
                                $.format("{}【{}】:{}",
                                        DateUtil.format(item.getCreateTime(),"yyyy-MM-dd HH:mm:ss"),"标签" ,
                                        item.getOperateOriginalName()+"→"+item.getOperateTargetName()
                                ));
                        break;
                    default:
                        break;
                }
            });
        }


        return content;
    }

    private boolean formContainKeys(Map<String, Object> formData, String key) {
        org.springframework.util.Assert.notEmpty(formData, LocaleMessageUtil.getMessage(EventTipKey.NoneFormData));
        org.springframework.util.Assert.hasText(key, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneKey, Arrays.asList(EventTipKey.RequiredTip)));

        if (!formData.containsKey(key)) {
            return false;
        }

        if ($.isNull(formData.get(key))) {
            return false;
        }

        Object objValue = formData.get(key);

        if (objValue.getClass().equals(String.class) && $.isBlank((String) objValue)) {
            return false;
        }

        return true;
    }

//    private XlmFile createFile(LupBill bill){
//
//        try{
//            ShapeJson shapeJson = new ShapeJson();
//            List tileList = new ArrayList<>();
//            InputStream inputStream = new ByteArrayInputStream(ClipTailsUtil.getImgByte(JSONUtil.toJsonStr(
//                    shapeJson.getGeometry()), tileList,20, 50));
//
//            String imgName = "vary-next-" + com.mascj.kernel.common.util.DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".jpg";
//
//            MultipartFile file2 = FileUtils.getMultipartFile(inputStream, imgName);
//
//            Result<XlmFile> fileResult = fileProvider.upload(file2);
//
//            if(fileResult.isSuccess())return fileResult.getData();
//
//        }catch (Exception exp){
//            exp.printStackTrace();
//        }
//
//        return null;
//    }

    @Override
    public Result<BillDetailVO> detailByDataId(Integer originType,Long dataId) {

        ProjectUtil.getProjectId();

        BillDetailVO billDetailVO = baseMapper.detailByDataId(dataId);

        if(!"".equals(billDetailVO.getExtraData())
                && billDetailVO.getExtraData()!=null){
            try{

                System.out.println("billDetailVO.getExtraData:"+billDetailVO.getExtraData());
                billDetailVO.setExtraDataVO( JSONUtil.toBean(billDetailVO.getExtraData(), ExtraDataVO.class));

            }catch (Exception exp){
                exp.printStackTrace();
            }
        }

        //查询flowentity数据
        LupFlowEntity byId = iLupFlowEntityService.getById(billDetailVO.getFlowEntityId());
        if (byId != null){

            billDetailVO.setProcessTaskId(byId.getProcessTaskId());


            BillReadStateDTO billReadStateDTO = new BillReadStateDTO();
            billReadStateDTO.setFlowId(byId.getId());
            billReadStateDTO.setProcessTaskId(Long.parseLong(byId.getProcessTaskId()));

            Integer flowState = byId.getFlowState();

            billDetailVO.setFourSuccess(byId.getFourSuccess());
            if(originType == 1) {
            XlmQueryHisTaskDto queryHisTaskDto = SetFunction
                    .lambdaSet(XlmQueryHisTaskDto::getProcessInstanceId, byId.getProcessInstanceId())
                    // 倒序展示，最新的节点排在最前面
                    .set(XlmQueryBaseTaskDto::isOrderByTaskCreateTime, true)
                    .set(XlmQueryBaseTaskDto::isAsc, false)
                    .set(XlmQueryHisTaskDto::isReturnFormData, true)
                    .getInstance();

            List<XlmTaskInfo> taskInfoList = xlmQueryProvider.queryHisTask(queryHisTaskDto).getData();
            Map<String, Map<String, Object>> formData = new HashMap<>();

            for (XlmTaskInfo info : taskInfoList) {
                info.setName(LocaleMessageUtil.getMessage("event.flow.".concat(info.getTaskDefinitionKey())));
                if ($.isEmpty(info.getFormData())) {
                    continue;
                }
                // 展示最新一次的数据
                if (!formData.containsKey(info.getTaskDefinitionKey())) {
                    formData.put(info.getTaskDefinitionKey(), info.getFormData());
                }
            }


                billDetailVO.setFormData(formData);
            }


        }




        EventCompareType compareType = EventCompareType.parse(billDetailVO.getCompareType());
        EventDataType dataType = EventDataType.parse(billDetailVO.getDataType());
        if(dataType == EventDataType.PictureDataType){

            billDetailVO.setCurrentPictureList(new ArrayList<>());
            if(compareType == EventCompareType.DoubleCompareType){
                billDetailVO.setComparePictureList(new ArrayList<>());
            }
        }else if(dataType == EventDataType.TileDataType){

            billDetailVO.setCurrentTileList(new ArrayList<>());
            if(compareType == EventCompareType.DoubleCompareType){
                billDetailVO.setCompareTileList(new ArrayList<>());
            }
        }else if(dataType == EventDataType.HumanTileDataType){

            billDetailVO.setCurrentTileList(new ArrayList<>());
            if(compareType == EventCompareType.DoubleCompareType){
                billDetailVO.setCompareTileList(new ArrayList<>());
            }
        }

        List<LupSource> sourceList = iLupBatchSourceService.listByBatch(billDetailVO.getCurrentBatchId());

        List<GeoTile> tileList = billDetailVO.getCurrentTileList();

        List<String> picList = billDetailVO.getCurrentPictureList();

        for (LupSource item : sourceList) {

            GeoTile geoTile = new GeoTile();

            EventSourceType eventSourceType = EventSourceType.parse(item.getType());

            if(eventSourceType == EventSourceType.TileSourceType){

//                AchievementPreviewVO achievementPreviewVO = fetchTileOptions(item.getOriginalSourceId());
//                if(achievementPreviewVO == null) continue;
//
//                geoTile.setTileUrl(achievementPreviewVO.getPreviewUrl());
//                geoTile.setFlyDate(item.getFlyDate());
//                geoTile.setMinZoom(achievementPreviewVO.getMinzoom());
//                geoTile.setMaxZoom(achievementPreviewVO.getMaxzoom());
//                geoTile.setTileBounds(JSONUtil.toJsonStr(achievementPreviewVO.getBounds()));
//                geoTile.setTileName(item.getSourceName());
//
//                tileList.add(geoTile);
            }else if(eventSourceType == EventSourceType.AutoDefineSourceType){

//                geoTile.setTileUrl(item.getUrl());
//                geoTile.setFlyDate(item.getFlyDate());
//                geoTile.setMinZoom(item.getMinZoom());
//                geoTile.setMaxZoom(item.getMaxZoom());
//                geoTile.setTileBounds(item.getBounds());
//                geoTile.setTileName(item.getSourceName());
//
//                tileList.add(geoTile);
            }else  if(eventSourceType == EventSourceType.PictureSourceType){
                //追加 图片数据 特警事项

                if(picList != null && !picList.contains(item.getUrl()))
                    picList.add(item.getUrl());

            }



        }

        if(billDetailVO.getExtraDataVO() != null
                && billDetailVO.getExtraDataVO().getEventPictureUrlList() != null
                && billDetailVO.getExtraDataVO().getEventPictureUrlList().size() > 0){
            billDetailVO.getExtraDataVO().getEventPictureUrlList().addAll(picList);
        }

        return Result.data(billDetailVO);
    }


    @Override
    public List<BillDetailVO> listBillDetailVOByQueryBillPictureInfo(IPage page,QueryBillPictureInfoDTO queryBillPictureInfoDTO) {


        List<BillDetailVO> list = baseMapper.listBillDetailVOByQueryBillPictureInfo(page,queryBillPictureInfoDTO);

        list.forEach(item->{
            BeanUtil.copyProperties(detailByDataId(queryBillPictureInfoDTO.getOriginType(),item.getDataId()).getData(),item);
        });


        return list;
    }

    @Override
    public Result<BillDetailVO> detailByBillId(Long billId) {

        RedisKey redisKey = RedisKey.forService(VariableConstants.lupHandleSystemTag, LupBillServiceImpl.class)
                .forParameter(VariableConstants.HandleModuleConfigCode,VariableConstants.HandleModuleBillDataCode, String.valueOf(LmContextHolder.getTenantId()),String.valueOf(billId));
        BillDetailVO finalBillDetailVO = redisPlane.getOrCreate(redisKey,()-> {

            ProjectUtil.getProjectId();

            BillDetailVO billDetailVO = baseMapper.detailById(billId);

            PendingStateEnum currentPendingState = PendingStateEnum.parseFromState(billDetailVO.getPendingState());

            if (!"".equals(billDetailVO.getExtraData())
                    && billDetailVO.getExtraData() != null) {
                try {

                    System.out.println("billDetailVO.getExtraData:" + billDetailVO.getExtraData());
                    billDetailVO.setExtraDataVO(JSONUtil.toBean(billDetailVO.getExtraData(), ExtraDataVO.class));
//                billDetailVO.setExtraData(null);

                } catch (Exception exp) {
                    exp.printStackTrace();
                }
            }
            Long dataId = billDetailVO.getDataId();
            LupData lupData = lupDataMapper.selectById(dataId);
            if (lupData != null) {
                billDetailVO.setPushState(lupData.getPushState());
                billDetailVO.setPushMsg(lupData.getPushMsg());

                billDetailVO.setFlyTaskId(lupData.getFlyTaskId());
                //计算飞行任务视频和参数
                billDetailVO.setFlyTaskVideoUrl(computeFlyTaskVideo(lupData.getFlyTaskId(), lupData.getHappenedTime(), lupData.getDataOrigin()));
            }

            Map<String, String> eventOriginalMap = fetchEventOriginal();
            billDetailVO.setEventOrigin(billDetailVO.getDataOrigin() != null ?
                    eventOriginalMap.get(billDetailVO.getDataOrigin().toString())
//                DataOrigin.parseToName(billDetailVO.getDataOrigin())

                    : "");

            if (billDetailVO.getGridName().equals(EventTipKey.UnknownAreaName)) {
                billDetailVO.setAreaName("");
                billDetailVO.setGridName(LocaleMessageUtil.getMessage(billDetailVO.getGridName()));
                billDetailVO.setAreaGrid(billDetailVO.getGridName());
            } else
                billDetailVO.setAreaGrid(billDetailVO.getAreaName() + "-" + billDetailVO.getGridName());

            //查询flowentity数据
            LupFlowEntity byId = iLupFlowEntityService.getById(billDetailVO.getFlowEntityId());
            if (byId != null) {

                billDetailVO.setProcessTaskId(byId.getProcessTaskId());


                BillReadStateDTO billReadStateDTO = new BillReadStateDTO();
                billReadStateDTO.setFlowId(byId.getId());
                billReadStateDTO.setProcessTaskId(Long.parseLong(byId.getProcessTaskId()));
                billDetailVO.setRead(billReadService.countByUserId(billReadStateDTO) > 0);


                Integer flowState = byId.getFlowState();
                if (flowState == 3) {
                    Result<List<String>> permissionByRequest = sysUserProvider.getPermissionByRequest(LmContextHolder.getUserId());
                    List<String> data = permissionByRequest.getData();
                    if (CollectionUtils.isNotEmpty(data) && data.contains("event:bill:view")) {
                        billDetailVO.setCheck(true);
                    } else {
                        billDetailVO.setCheck(false);
                    }
                }
                billDetailVO.setFourSuccess(byId.getFourSuccess());
                XlmQueryHisTaskDto queryHisTaskDto = SetFunction
                        .lambdaSet(XlmQueryHisTaskDto::getProcessInstanceId, byId.getProcessInstanceId())
                        // 倒序展示，最新的节点排在最前面
                        .set(XlmQueryBaseTaskDto::isOrderByTaskCreateTime, true)
                        .set(XlmQueryBaseTaskDto::isAsc, false)
                        .set(XlmQueryHisTaskDto::isReturnFormData, true)
                        .getInstance();

                List<XlmTaskInfo> taskInfoList = xlmQueryProvider.queryHisTask(queryHisTaskDto).getData();
                Map<String, Map<String, Object>> formData = new HashMap<>();

                for (XlmTaskInfo info : taskInfoList) {
                    info.setName(LocaleMessageUtil.getMessage("event.flow.".concat(info.getTaskDefinitionKey())));
                    if ($.isEmpty(info.getFormData())) {
                        continue;
                    }
                    // 展示最新一次的数据
                    if (!formData.containsKey(info.getTaskDefinitionKey())) {
                        formData.put(info.getTaskDefinitionKey(), info.getFormData());
                    }
                }




                List<TaskVo> taskVoList = new ArrayList<>();
                Enumerable<TaskVo> select = Linqs.of(taskInfoList).select(x -> convertTask(x, byId.getBizCode()));


                Integer flowState1 = byId.getFlowState();
                Integer fourSuccess = byId.getFourSuccess();

                if (flowState1 != null && flowState1 == 4) {
                    TaskVo end = new TaskVo();
                    if (fourSuccess != null && fourSuccess == 2) {
                        end.setName(getMessage("event.flow.deal_complete"));
                    } else {
                        end.setName(getMessage("event.flow.archived"));
                    }
                    TaskVo taskVo = select.get(0);
                    end.setCreateTime(taskVo.getEndTime());
                    taskVoList.add(end);
                }

                rebuildTaskVoCollection(taskVoList, select, billId);


                if (fourSuccess != null && fourSuccess == 2) {
                    for (TaskVo taskVo : taskVoList) {
                        String taskDefinitionKey = taskVo.getTaskDefinitionKey();
                        if ("patrol".equals(taskDefinitionKey)) {
                            String a = getMessage("event.flow.deal_result") + ":" +
                                    getMessage("event.flow.no_push");
                            taskVo.setContent(Arrays.asList(a));
                        }
                    }
                }
                billDetailVO.setFlowTask(taskVoList);


                billDetailVO.setFormData(formData);


            }


            EventCompareType compareType = EventCompareType.parse(billDetailVO.getCompareType());
            EventDataType dataType = EventDataType.parse(billDetailVO.getDataType());
            if (dataType == EventDataType.PictureDataType) {

                billDetailVO.setCurrentPictureList(new ArrayList<>());
                if (compareType == EventCompareType.DoubleCompareType) {
                    billDetailVO.setComparePictureList(new ArrayList<>());
                }
            } else if (dataType == EventDataType.TileDataType) {

                billDetailVO.setCurrentTileList(new ArrayList<>());
                if (compareType == EventCompareType.DoubleCompareType) {
                    billDetailVO.setCompareTileList(new ArrayList<>());
                }
            } else if (dataType == EventDataType.HumanTileDataType) {

                billDetailVO.setCurrentTileList(new ArrayList<>());
                if (compareType == EventCompareType.DoubleCompareType) {
                    billDetailVO.setCompareTileList(new ArrayList<>());
                }
            }

            List<LupSource> sourceList = iLupBatchSourceService.listByBatch(billDetailVO.getCurrentBatchId());

            List<GeoTile> tileList = billDetailVO.getCurrentTileList();

            List<String> picList = billDetailVO.getCurrentPictureList();

            for (LupSource item : sourceList) {

                GeoTile geoTile = new GeoTile();

                EventSourceType eventSourceType = EventSourceType.parse(item.getType());

                if (eventSourceType == EventSourceType.TileSourceType) {
                    AchievementClientPreviewVO achievementPreviewVO = fetchTileOptions(item.getOriginalSourceId());
                    if (achievementPreviewVO == null) continue;

                    geoTile.setFlipFlag(achievementPreviewVO.getFlipFlag());
                    geoTile.setTileUrl(achievementPreviewVO.getPreviewUrl());
                    geoTile.setFlyDate(item.getFlyDate());
                    geoTile.setMinZoom(achievementPreviewVO.getMinZoom());
                    geoTile.setMaxZoom(achievementPreviewVO.getMaxZoom());
                    geoTile.setTileBounds(JSONUtil.toJsonStr(achievementPreviewVO.getBounds()));
                    geoTile.setTileName(item.getSourceName());

                    tileList.add(geoTile);
                } else if (eventSourceType == EventSourceType.AutoDefineSourceType) {

                    if (item.getSourceType() == ModuleDataSourceTypeEnum.CompareModuleSource.getValue()) {
                        geoTile.setTileUrl(item.getUrl());
                    } else {
                        CommonResult commonResult = surveyAchievementFeign.preview(item.getOriginalTileSourceId());
                        log.info("commonResult:" + JSONUtil.toJsonStr(commonResult));
                        if (!"".equals(commonResult.getData())) {
                            AchievementClientPreviewVO achievementPreviewVO = BeanUtil.toBean(commonResult.getData(), AchievementClientPreviewVO.class);
                            geoTile.setFlipFlag(achievementPreviewVO.getFlipFlag());
                            geoTile.setTileUrl(achievementPreviewVO.getPreviewUrl());
                        }
                    }

                    geoTile.setFlyDate(item.getFlyDate());
                    geoTile.setMinZoom(item.getMinZoom());
                    geoTile.setMaxZoom(item.getMaxZoom());
                    geoTile.setTileBounds(item.getBounds());
                    geoTile.setTileName(item.getSourceName());

                    tileList.add(geoTile);
                } else if (eventSourceType == EventSourceType.PictureSourceType) {
                    //追加 图片数据 特警事项
                    if (billDetailVO.getExtraDataVO() != null
                            && billDetailVO.getExtraDataVO().getEventPictureUrlList() != null
                            && billDetailVO.getExtraDataVO().getEventPictureUrlList().size() > 0) {
                        picList.addAll(billDetailVO.getExtraDataVO().getEventPictureUrlList());
                    }

                    if (picList != null && !picList.contains(item.getUrl()))
                        picList.add(item.getUrl());

                }
            }


            if (compareType == EventCompareType.DoubleCompareType) {

                tileList = billDetailVO.getCompareTileList();

                picList = billDetailVO.getComparePictureList();

                List<LupSource> compareSourceList = iLupBatchSourceService.listByBatch(billDetailVO.getCompareBatchId());

                for (LupSource item : compareSourceList) {

                    GeoTile geoTile = new GeoTile();

                    EventSourceType eventSourceType = EventSourceType.parse(item.getType());

                    if (eventSourceType == EventSourceType.TileSourceType) {
                        AchievementClientPreviewVO achievementPreviewVO = fetchTileOptions(item.getOriginalSourceId());
                        if (achievementPreviewVO == null) continue;
                        geoTile.setFlipFlag(achievementPreviewVO.getFlipFlag());
                        geoTile.setTileUrl(achievementPreviewVO.getPreviewUrl());
                        geoTile.setFlyDate(item.getFlyDate());
                        geoTile.setMinZoom(achievementPreviewVO.getMinZoom());
                        geoTile.setMaxZoom(achievementPreviewVO.getMaxZoom());
                        geoTile.setTileBounds(JSONUtil.toJsonStr(achievementPreviewVO.getBounds()));
                        geoTile.setTileName(item.getSourceName());

                        tileList.add(geoTile);
                    } else if (eventSourceType == EventSourceType.AutoDefineSourceType) {

                        if (item.getSourceType() == ModuleDataSourceTypeEnum.CompareModuleSource.getValue()) {
                            geoTile.setTileUrl(item.getUrl());
                        } else {
                            CommonResult commonResult = surveyAchievementFeign.preview(item.getOriginalTileSourceId());
                            log.info("commonResult:" + JSONUtil.toJsonStr(commonResult));
                            if (!"".equals(commonResult.getData())) {
                                AchievementPreviewVO achievementPreviewVO = BeanUtil.toBean(commonResult.getData(), AchievementPreviewVO.class);
                                geoTile.setTileUrl(achievementPreviewVO.getPreviewUrl());
                            }
                        }

                        geoTile.setFlyDate(item.getFlyDate());
                        geoTile.setMinZoom(item.getMinZoom());
                        geoTile.setMaxZoom(item.getMaxZoom());
                        geoTile.setTileBounds(item.getBounds());
                        geoTile.setTileName(item.getSourceName());

                        tileList.add(geoTile);
                    } else if (eventSourceType == EventSourceType.PictureSourceType) {
                        picList.add(item.getUrl());
                    }
                }

            }

            // 判断是否存在标记点
            // 判断是否已经存在
            LupEventMark one = lupEventMarkService.getOne(new LambdaQueryWrapper<LupEventMark>()
                    .eq(LupEventMark::getDeleted, 0).eq(LupEventMark::getEventId, billId));
            if (Objects.nonNull(one)) {
                billDetailVO.setHasDimension(true);
            }

            return billDetailVO;
        },BillDetailVO.class);

        return Result.data(finalBillDetailVO);
    }

    public void rebuildTaskVoCollection(List<TaskVo> taskVoList,Enumerable<TaskVo> select,Long billId){

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();

        if(handleModuleConfigVO.getEventHangingSwitch()) {

            Map<String, List<LupBillPendingDataItemVO>> billPendingDataMap = pendingService.mapLupBillPendingDataItemVOByFlowTaskId(billId);
            //处理流程节点补充后期处理流程节点
            for (TaskVo taskVo : select) {

//                if(taskVo.getEndTime() != null)
                taskVoList.add(taskVo);

                List<TaskVo> tmpTaskVoList = new ArrayList<>();

                boolean bf = false;

                //查询是否有多个申请后期处理的过程节点
                if (billPendingDataMap.containsKey(taskVo.getId())) {
                    List<LupBillPendingDataItemVO> pendingDataItemVOList = billPendingDataMap.get(taskVo.getId());
                    if (pendingDataItemVOList.size() > 0) {

                        for (int i = pendingDataItemVOList.size() - 1; i >= 0; i--) {
                            LupBillPendingDataItemVO item = pendingDataItemVOList.get(i);
//

                            //已审核
                            TaskVo approvalPendingTaskVo = new TaskVo();
                            approvalPendingTaskVo.setName($.format("{}{}", getMessage(EventTipKey.EventPending), getMessage(EventTipKey.EventPendingApproval)));
                            approvalPendingTaskVo.setTaskDefinitionKey(taskVo.getTaskDefinitionKey());
                            if (item.getApprovalResultState() != null) {
                                approvalPendingTaskVo.setEndTime(DateUtil.parse(item.getApprovalTime()));

                                List<String> approvalContentList = new ArrayList<>();
                                approvalContentList.add(
                                        $.format("{}{}", getMessage(EventTipKey.EventPendingApproval), getMessage(EventTipKey.FlowViewPerson)) +
                                                "：" + item.getApprovalUserName());
                                approvalContentList.add(
                                        LocaleMessageUtil.getMessage(EventTipKey.FlowViewResult) +
                                                "：" + item.getApprovalResult());
                                if (item.getApprovalResultState() == PendingStateEnum.PendingStateEnumViewingReject && ObjectUtil.isNotEmpty(item.getRejectReason()))
                                    approvalContentList.add(
                                            LocaleMessageUtil.getMessage(EventTipKey.FlowViewRejectReason) +
                                                    ":" + item.getRejectReason());


                                approvalPendingTaskVo.setContent(approvalContentList);
                                tmpTaskVoList.add(approvalPendingTaskVo);
                            } else {
                                bf = true;
                                String tmp = $.format("{}{}", getMessage(EventTipKey.EventPending), getMessage(EventTipKey.EventPendingApproving));
                                if (ObjectUtil.isEmpty(taskVo.getEndTime())&& !taskVo.getName().contains(tmp)) {
                                    taskVo.setName(taskVo.getName() + "(" +
                                            tmp +
                                            ")");
                                }
                            }


                            TaskVo applyPendingTaskVo = new TaskVo();

                            Map<String, Object> pendingFormData = new HashMap<>();
                            applyPendingTaskVo.setFormData(pendingFormData);
                            pendingFormData.put("photos", item.getFileList());

                            applyPendingTaskVo.setName($.format("{}{}", getMessage(EventTipKey.EventPending), getMessage(EventTipKey.EventPendingApply)));

                            if (bf && ObjectUtil.isNotEmpty(taskVo.getEndTime())) {
                                applyPendingTaskVo.setName(

                                        applyPendingTaskVo.getName() + "(" +
                                                getMessage(EventTipKey.EventPendingAutoDel) +
                                                ")"
                                );
                            }

                            List<String> contentList = new ArrayList<>();
                            applyPendingTaskVo.setContent(contentList);
                            contentList.add(
                                    $.format("{}{}", getMessage(EventTipKey.EventPendingApply), getMessage(EventTipKey.FlowViewPerson)) +
                                            "：" + item.getApplyUserName());
                            contentList.add(
                                    $.format("{}{}", getMessage(EventTipKey.EventPending), getMessage(EventTipKey.EventBillCommonDesc)) +
                                            "：" + item.getApplyReason());
                            Date date = DateUtil.date(item.getCreateTime());
                            applyPendingTaskVo.setEndTime(date);

                            applyPendingTaskVo.setTaskDefinitionKey(taskVo.getTaskDefinitionKey());
                            tmpTaskVoList.add(applyPendingTaskVo);

                        }

                    }

                }


                taskVoList.addAll(tmpTaskVoList);


            }
        }else {
            taskVoList.addAll(select);
        }
    }

    @Override
    public List<TagStateCountVo> tagMetadata(BillSearchDTO billSearchDTO) {

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();

        prepareBillSearchDTO(billSearchDTO);

        List<Long> userId = new ArrayList();
        userId.add(LmContextHolder.getUserId());
        billSearchDTO.setLabelIdListPerm(lupEventLabelService.labelIdsByUserIds(userId));

        Long projectId = ProjectUtil.getProjectId();

        List<MetaTag> tableMetadataList = metadataHandler.getTableMetadata(billSearchDTO.getBizCode());

        List<TagStateCountVo> result = tableMetadataList.stream().map(TagStateCountVo::new).collect(Collectors.toList());
        result.forEach(x -> {

            if(handleModuleConfigVO.getEventHangingSwitch() && x.getTagType() == 2) {
                //pendJudging pendingToHandle
                PendingStateEnum pendingStateEnum = PendingStateEnum.parse(x.getTagCode());
                if(pendingStateEnum!=null)
                    billSearchDTO.setPendingState(pendingStateEnum.getPendingState());
            }else{
                billSearchDTO.setPendingState(null);
            }
            billSearchDTO.setFlowStateList(x.getFlowStates());
            LupBillCountVo total = baseMapper.countFlowStateBill(billSearchDTO, projectId);//workBillRepository.pageCount(search);
            x.setCount(total.getBillCount());

        });

        return result;
    }
    private void prepareBillSearchDTO(BillSearchDTO dto){

        if(dto.getDateEnd() != null && !"".equals(dto.getDateEnd())){
            dto.setDateEnd(
                    dto.getDateEnd() + " 23:59:59"
            );
        }
        dto.setGridUnitIdList(gridUserService.dataScope(LmContextHolder.getUserId()));

    }
    @Override
    public List<BillStateCountVo>  billStateCount(BillSearchDTO billSearchDTO) {

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();

        prepareBillSearchDTO(billSearchDTO);

        List<Long> userId = new ArrayList();
        userId.add(LmContextHolder.getUserId());
        billSearchDTO.setLabelIdListPerm(lupEventLabelService.labelIdsByUserIds(userId));


        Result<List<XlmActivityState>> activityListResult = configProvider.getActivityList(billSearchDTO.getBizCode());
        List<XlmActivityState> activityList = activityListResult.getData();

        List<BillStateCountVo> result = Linqs.of(activityList)
                .select(x -> new BillStateCountVo(x.getState()))
                .toList();

        Long projectId = ProjectUtil.getProjectId();

        billSearchDTO.setPendingState(null);

        //查询流程数据
        Map<Integer,XlmActivityState> activityStateMap = Linqs.of(configProvider.getActivityList(billSearchDTO.getBizCode()).getData()).toMap(XlmActivityState::getState);

        result.forEach(x -> {

            XlmActivityState activityState = activityStateMap.get(x.getFlowState());
            if(activityState!=null) {
                billSearchDTO.setFlowState(x.getFlowState());
                LupBillCountVo total = baseMapper.countFlowStateBill(billSearchDTO, projectId);//workBillRepository.pageCount(search);
                x.setCount(total.getBillCount());
                x.setTagCode(activityState.getActivityId());
            }

        });
        if(handleModuleConfigVO.getEventHangingSwitch()) {

            billSearchDTO.setFlowState(null);
            billSearchDTO.setPendingState(PendingStateEnum.PendingStateEnumWaitViewing.getPendingState());
            LupBillCountVo total = baseMapper.countFlowStateBill(billSearchDTO, projectId);//workBillRepository.pageCount(search);

            BillStateCountVo pendingBillData = new BillStateCountVo();
            pendingBillData.setCount(total.getBillCount());
            pendingBillData.setTagCode(PendingStateEnum.PendingStateEnumWaitViewing.getCode());

            result.add(pendingBillData);


            billSearchDTO.setFlowState(null);
            billSearchDTO.setPendingState(PendingStateEnum.PendingStateEnumViewingAccess.getPendingState());
            total = baseMapper.countFlowStateBill(billSearchDTO, projectId);//workBillRepository.pageCount(search);

            pendingBillData = new BillStateCountVo();
            pendingBillData.setCount(total.getBillCount());
            pendingBillData.setTagCode(PendingStateEnum.PendingStateEnumViewingAccess.getCode());

            result.add(pendingBillData);
        }

        return result;


    }

    private AchievementClientPreviewVO fetchTileOptions(Long airlineTaskId){
        Result<AchievementClientPreviewVO> previewVOResult = streamProvider.preview(airlineTaskId);
        return previewVOResult.getData();
    }


    private String GenerateBillNumber(Long projectId){

        String billNumber = null;

        String key = Constants.LOCAL_PROJECT_NAME + ":GenerateBillNumber:" + projectId;

        Boolean lockLua = RedisOpsUtils.getLockLua(key, 360L);
        try {
            if (lockLua) {
                RedisKey redisKey = RedisKey.forService("GenerateBillNumber", LupBillServiceImpl.class)
                        .forParameter(String.valueOf(LmContextHolder.getTenantId()));

                String billNumberPrefix = DateUtil.format(new Date(), "yyMMdd");
//        numberIndex = redisPlane.getOrCreate(redisKey,()->{

                //创建的年月日（如230805 + 0000依次向上累加，例如230805200001

                List<LupBill> list = list(Wrappers.<LupBill>lambdaQuery()
                        .eq(LupBill::getDeleted, 0)
                        .eq(projectId != null, LupBill::getProjectId, projectId)
                        .likeRight(LupBill::getBillNumber, billNumberPrefix)
                        .orderByDesc(LupBill::getBillNumber, LupBill::getId)
                        .select(LupBill::getBillNumber)
                        .last(" limit 1 ")
                );


                if (list.size() > 0) {
                    numberIndex = Integer.parseInt(list.get(0).getBillNumber().substring(billNumberPrefix.length()));
                    numberIndex = numberIndex + 1;
                } else {
                    numberIndex = 1;
                }

                billNumber = billNumberPrefix + String.format("%04d", numberIndex);

            }
        }finally {
            if (lockLua) {
                RedisOpsUtils.unLockLua(key);
            }
        }

        return billNumber;
    }


    @Override
    public Boolean switchGridUnit(BillSwitchGridDTO switchGridDTO){

        pendingService.clearBillCache(switchGridDTO.getBillId());
        //查询工单信息
        LupBill bill =  getById(switchGridDTO.getBillId());

        Assert.isTrue(!bill.getGridUnitId().equals(switchGridDTO.getTargetGridUnitId()),LocaleMessageUtil.getMessage(EventTipKey.NoneChangeGrid));

        LupFlowEntity flowEntity = iLupFlowEntityService.getById(bill.getFlowEntityId());

        LupGridUnit gridUnit = gridUnitMapper.selectById(switchGridDTO.getTargetGridUnitId());

        LupGridUnit parentGridUnit = gridUnitMapper.selectById(gridUnit.getPid());

        //记录切换记录
        LupBillSwitchGrid switchGrid = new LupBillSwitchGrid();
        switchGrid.setBillId(switchGridDTO.getBillId());
        switchGrid.setFlowId(bill.getFlowEntityId());
        switchGrid.setProcessTaskId(flowEntity.getProcessTaskId());
        switchGrid.setOperateType(1);

        switchGrid.setOperateUserName(LmContextHolder.getUserName());

        switchGrid.setOriginalGridCode(bill.getGridCode());
        switchGrid.setOriginalGridName(bill.getGridName());
        switchGrid.setOriginalAreaCode(bill.getAreaCode());
        switchGrid.setOriginalAreaName(bill.getAreaName());
        if(switchGrid.getOriginalGridName().equals(EventTipKey.UnknownAreaName)) {
            switchGrid.setOriginalAreaName(LocaleMessageUtil.getMessage(switchGrid.getOriginalAreaName()));
            switchGrid.setOriginalGridName(LocaleMessageUtil.getMessage(switchGrid.getOriginalGridName()));
        }


        bill.setGridUnitId(gridUnit.getId());
        bill.setGridCode(gridUnit.getCode());
        bill.setGridName(gridUnit.getName());
        if(parentGridUnit != null) {
            bill.setAreaCode(parentGridUnit.getCode());
            bill.setAreaName(parentGridUnit.getName());
        }else{
            bill.setAreaCode(gridUnit.getCode());
            bill.setAreaName(gridUnit.getName());
        }

        switchGrid.setTargetGridCode(bill.getGridCode());
        switchGrid.setTargetGridName(bill.getGridName());
        switchGrid.setTargetAreaCode(bill.getAreaCode());
        switchGrid.setTargetAreaName(bill.getAreaName());

        if(switchGrid.getTargetGridName().equals(EventTipKey.UnknownAreaName)) {
            switchGrid.setTargetAreaName(LocaleMessageUtil.getMessage(switchGrid.getTargetAreaName()));
            switchGrid.setTargetGridName(LocaleMessageUtil.getMessage(switchGrid.getTargetGridName()));
        }

        switchGridService.save(switchGrid);

        updateById(bill);

        return true;
    }

    @Override
    public List<BillSearchExcelVO> exportDataToExcel(ExportFileDTO billSearchDTO){

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();

        Map<String, String> handleMap = configContext.getDictMap(VariableConstants.handle_type);

//        List<XlmActivityState> activityStates = configProvider.getActivityList("handle").getData();

//        Map<Integer,String> activityStateMap= activityStates.stream().collect(Collectors.toMap(XlmActivityState::getState,XlmActivityState::getName));

        if(billSearchDTO.getDateEnd() != null && !"".equals(billSearchDTO.getDateEnd())){
            billSearchDTO.setDateEnd(
                    billSearchDTO.getDateEnd() + " 23:59:59"
            );
        }
        billSearchDTO.setGridUnitIdList(gridUserService.dataScope(LmContextHolder.getUserId()));
        //查询全部
        billSearchDTO.setFlowStateList(null);

        List<Long> userId = new ArrayList();
        userId.add(LmContextHolder.getUserId());
        billSearchDTO.setLabelIdListPerm(lupEventLabelService.labelIdsByUserIds(userId));



        Long projectId = ProjectUtil.getProjectId();
        List<BillSearchExcelVO> billSearchExcelVOList = baseMapper.listAllBill(billSearchDTO,projectId, LmContextHolder.getTenantId());

        //查询流程数据
        Map<Integer,XlmActivityState> activityStateMap = Linqs.of(configProvider.getActivityList(billSearchDTO.getBizCode()).getData()).toMap(XlmActivityState::getState);

        System.out.println("LmContextHolder.getLang():"+LmContextHolder.getLang());
        Map<String,String> eventOriginalMap = fetchEventOriginal();
        billSearchExcelVOList.forEach(e->{


            XlmQueryHisTaskDto queryHisTaskDto = SetFunction
                    .lambdaSet(XlmQueryHisTaskDto::getProcessInstanceId, e.getProcessInstanceId())
                    // 倒序展示，最新的节点排在最前面
                    .set(XlmQueryBaseTaskDto::isOrderByTaskCreateTime, true)
                    .set(XlmQueryBaseTaskDto::isAsc, false)
                    .set(XlmQueryHisTaskDto::isReturnFormData, true)
                    .getInstance();

            List<XlmTaskInfo> taskInfoList = xlmQueryProvider.queryHisTask(queryHisTaskDto).getData();
            Map<String, Map<String, Object>> formData = new HashMap<>();

            for (XlmTaskInfo info : taskInfoList) {
                info.setName(LocaleMessageUtil.getMessage("event.flow.".concat(info.getTaskDefinitionKey())));
                if ($.isEmpty(info.getFormData())) {
                    continue;
                }
                // 展示最新一次的数据
                if (!formData.containsKey(info.getTaskDefinitionKey())) {
                    formData.put(info.getTaskDefinitionKey(), info.getFormData());
                }
            }

            Map<String,Object>patrolData =formData.get("patrol");

            e.setEventOriginName(eventOriginalMap.get(e.getDataOrigin().toString()));

            //originType  1 是 单个事件  2 是多个事件
            e.setEventUrlAddress(billSearchDTO.getDomainUrl()+"/lup-handle/preview?originType=1&eventId="+e.getDataId());

            if(ObjectUtil.isNotEmpty(patrolData)) {
                if (patrolData.containsKey("list")) {
                    List<FormDataItem> formDataItemList =  JSONUtil.toList(JSONUtil.toJsonStr(patrolData.get("list")),FormDataItem.class);

                    formDataItemList.forEach(formDataItem->{
                        if (ObjectUtil.isNotEmpty(formDataItem.getOptions())
                                && ObjectUtil.isNotEmpty(formDataItem.getOptions().getValue())
                        &&"protal_content".equals(formDataItem.getModel())) {
                            e.setPatrolContent(formDataItem.getOptions().getValue());
                        }
                    });

                }


                //是否存在 0不存在 1存在
                if (patrolData.containsKey("hasEvent")) {
                    String hasEvent = patrolData.get("hasEvent").toString();
                    if (ObjectUtil.isNotEmpty(hasEvent)) {
                        e.setHasEvent(hasEvent.equals("0") ? "不存在" : "存在");
                    }
                }
            }

            XlmActivityState activityState =  activityStateMap.get(e.getFlowState());

            if(activityState!=null) {
                e.setFlowStateName(activityState.getName());
            }

            if( handleModuleConfigVO.getEventHangingSwitch()
                    && e.getPendingState() != null
                    && e.getPendingState() == PendingStateEnum.PendingStateEnumViewingAccess.getPendingState()){
                e.setFlowStateName(LocaleMessageUtil.getMessage(EventTipKey.EventPending));
            }

            if(e.getGridName().equals(EventTipKey.UnknownAreaName)) {
                e.setAreaName(LocaleMessageUtil.getMessage(e.getAreaName()));
                e.setGridName(LocaleMessageUtil.getMessage(e.getGridName()));
            }
                e.setHandleTyeName(handleMap.get(e.getHandleType()));
            try{
                //计算地理位置
                String res = GeoLocation.locationToAddress(readConfigUtil.readPushConfig().getGeoLocationKey(),e.getLocationLng().toString(),e.getLocationLat().toString()).getBody();
                JSONObject resJsonObj = JSONUtil.parseObj(res);
                //{"result":{"formatted_address":"浙江省绍兴市柯桥区杨汛桥街道延寿寺","location":{"lon":120.28543,"lat":30.10462},"addressComponent":{"address":"延寿寺","city":"绍兴市","county_code":"156330603","nation":"中国","poi_position":"西北","county":"柯桥区","city_code":"156330600","address_position":"西北","poi":"延寿寺","province_code":"156330000","province":"浙江省","road":"杨渔线","road_distance":70,"poi_distance":40,"address_distance":40}},"msg":"ok","status":"0"}
                e.setLocationAddress(
                        resJsonObj.getJSONObject("result").getStr("formatted_address")
                );
            }catch (Exception exp){
                exp.printStackTrace();
            }

        });

        return billSearchExcelVOList;
    }

    @Override
    public List<BillSearchExcelYXQVO> exportYXQDataToExcel(ExportFileDTO billSearchDTO) {

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();
        Map<String, String> handleMap = configContext.getDictMap(VariableConstants.handle_type);

        if(billSearchDTO.getDateEnd() != null && !"".equals(billSearchDTO.getDateEnd())){
            billSearchDTO.setDateEnd(
                    billSearchDTO.getDateEnd() + " 23:59:59"
            );
        }
        billSearchDTO.setGridUnitIdList(gridUserService.dataScope(LmContextHolder.getUserId()));
        //查询全部
        billSearchDTO.setFlowStateList(null);

        List<Long> userId = new ArrayList();
        userId.add(LmContextHolder.getUserId());
        billSearchDTO.setLabelIdListPerm(lupEventLabelService.labelIdsByUserIds(userId));



        Long projectId = ProjectUtil.getProjectId();
        List<BillSearchExcelYXQVO> billSearchExcelVOList = baseMapper.listYXQAllBill(billSearchDTO,projectId, LmContextHolder.getTenantId());

        //查询流程数据
        Map<Integer,XlmActivityState> activityStateMap = Linqs.of(configProvider.getActivityList(billSearchDTO.getBizCode()).getData()).toMap(XlmActivityState::getState);

        System.out.println("LmContextHolder.getLang():"+LmContextHolder.getLang());
        Map<String,String> eventOriginalMap = fetchEventOriginal();
        for (BillSearchExcelYXQVO e : billSearchExcelVOList) {



            e.setEventOriginName(eventOriginalMap.get(e.getDataOrigin().toString()));


            XlmActivityState activityState =  activityStateMap.get(e.getFlowState());

            if(activityState!=null)
                e.setFlowStateName(activityState.getName());

            if(handleModuleConfigVO.getEventHangingSwitch()
                    && e.getPendingState()!=null
                    && e.getPendingState() == PendingStateEnum.PendingStateEnumViewingAccess.getPendingState()){
                e.setFlowStateName(LocaleMessageUtil.getMessage(EventTipKey.EventPending));
            }

            if(e.getGridName().equals(EventTipKey.UnknownAreaName)) {
                e.setAreaName(LocaleMessageUtil.getMessage(e.getAreaName()));
                e.setGridName(LocaleMessageUtil.getMessage(e.getGridName()));
            }
            e.setHandleTyeName(handleMap.get(e.getHandleType()));
            try{
                //计算地理位置
                String res = GeoLocation.locationToAddress(readConfigUtil.readPushConfig().getGeoLocationKey(),e.getLocationLng().toString(),e.getLocationLat().toString()).getBody();
                JSONObject resJsonObj = JSONUtil.parseObj(res);
                //{"result":{"formatted_address":"浙江省绍兴市柯桥区杨汛桥街道延寿寺","location":{"lon":120.28543,"lat":30.10462},"addressComponent":{"address":"延寿寺","city":"绍兴市","county_code":"156330603","nation":"中国","poi_position":"西北","county":"柯桥区","city_code":"156330600","address_position":"西北","poi":"延寿寺","province_code":"156330000","province":"浙江省","road":"杨渔线","road_distance":70,"poi_distance":40,"address_distance":40}},"msg":"ok","status":"0"}
                e.setLocationAddress(
                        resJsonObj.getJSONObject("result").getStr("formatted_address")
                );
            }catch (Exception exp){
                exp.printStackTrace();
            }
        }

        return billSearchExcelVOList;
    }

    private Map<String,String> fetchEventOriginal(){
        Map<String,String> eventOriginalMap = new HashMap<>();
        //查询字典
        Result<List<Dict>> result = dictProvider.getList(VariableConstants.EventOriginCode);
        if(result.isSuccess()) {
            Map<String, Dict>  dictMap= result.getData().stream()
                    .filter(s -> s.getDictKey() != null && !"".equals(s.getDictKey()))
                    .collect(Collectors.toMap(Dict::getDictKey, each -> each, (value1, value2) -> value1));
            dictMap.keySet().forEach(key->{
                eventOriginalMap.put(key,dictMap.get(key).getDictValue());
            });
            
        }
        
        return eventOriginalMap;
    }

    @Override
    public boolean dimensionEvent(Long billId) {
        // 判断事件是否存在
        LupBill byId = getById(billId);
        if (Objects.isNull(byId)) {
            throw new ServiceException(LocaleMessageUtil.getMessage(EventTipKey.NoneExistBill));
        }

        // 判断是否已经存在
        int count = lupEventMarkService.count(new LambdaQueryWrapper<LupEventMark>()
                .eq(LupEventMark::getDeleted, 0).eq(LupEventMark::getEventId, billId));
        if (count != 0) {
            throw new ServiceException(LocaleMessageUtil.getMessageByKeyList(EventTipKey.ExistAnnotation,Arrays.asList(EventTipKey.MarkPointTip)));
        }
        LupData lupData = dataService.getById(byId.getDataId());


        ArrayList<MarkDTO> markDTOS = new ArrayList<>();

        MarkDTO markDTO = new MarkDTO();

        if(byId.getGridName().equals(EventTipKey.UnknownAreaName)) {
            byId.setGridName(LocaleMessageUtil.getMessage(byId.getGridName()));
        }

        markDTO.setMarkName(byId.getGridName() +byId.getBillNumber());
        markDTO.setAltitude(0.00);
        markDTO.setFrameColour("248, 124, 28");
        markDTO.setLineColour("248, 124, 28");
        markDTO.setLineLength(2.0);
        markDTO.setMarkType(1);
        markDTO.setShowFrame(1);
        markDTO.setSourceBy(2);
        markDTO.setSurfaceColour("248, 124, 28");
        markDTO.setTransparency(0.5);
        markDTO.setLat(lupData.getLocationLat().doubleValue());
        markDTO.setLon(lupData.getLocationLng().doubleValue());
        markDTOS.add(markDTO);
        Result<List<Map<String, Object>>> add = lupMarkCenterFeign.add(markDTOS);
        if (!add.isSuccess()) {
            throw new ServiceException(add.getMsg());
        }

        if (add.isSuccess() && $.isNotEmpty(add)) {
            // 存储关联关系
            LupEventMark lupEventMark = new LupEventMark();
            lupEventMark.setEventId(billId);
            String markId = add.getData().get(0).get("id").toString();
            lupEventMark.setMarkId(Long.parseLong(markId));
            lupEventMarkService.save(lupEventMark);
        }
        pendingService.clearBillCache(billId);
        return true;
    }

    @Override
    public boolean cancelDimensionEvent(Long billId) {
        // 判断事件是否存在
        LupBill byId = getById(billId);
        if (Objects.isNull(byId)) {
            throw new ServiceException(LocaleMessageUtil.getMessage(EventTipKey.NoneExistBill));
        }

        // 判断是否已经存在
        LupEventMark one = lupEventMarkService.getOne(new LambdaQueryWrapper<LupEventMark>()
                .eq(LupEventMark::getDeleted, 0).eq(LupEventMark::getEventId, billId));
        if (Objects.isNull(one)) {
            throw new ServiceException(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneExistAnnotation,Arrays.asList(EventTipKey.MarkPointTip)));
        }

        Result<?> delete = lupMarkCenterFeign.delete(one.getMarkId());
        if (!delete.isSuccess()) {
            throw new ServiceException(LocaleMessageUtil.getMessageByKeyList(EventTipKey.FailExistAnnotation,Arrays.asList(EventTipKey.MarkPointTip)));
        }
        lupEventMarkService.removeById(one.getId());

        pendingService.clearBillCache(billId);
        return true;
    }

    @Override
    public Result initUnLocateGridUnitBill() {

        //查询需要在未知区域的

        List<LupBill> lupBills = list(Wrappers.<LupBill>lambdaQuery()
                .eq(LupBill::getGridCode,VariableConstants.UNKNOWN_AREA_CODE)
                .eq(LupBill::getDeleted,0));

        lupBills.forEach(bill->{

            LupData data = dataService.getById(bill.getDataId());
            //定位事件网格
            Point queryPoint = new Point(Double.parseDouble(data.getLocationLng().toString()), Double.parseDouble(data.getLocationLat().toString()));
            String centerPoint = OperatorExportToWkt.local().execute(WktExportFlags.wktExportPoint, queryPoint, null);
            List<LupGridUnitVO> unitList = gridUnitMapper.findGridByPoint(bill.getProjectId(), centerPoint);

//            Assert.isTrue(unitList.size() > 0, LocaleMessageUtil.getMessage(EventTipKey.ErrorGridLocate));

            if(unitList.size() > 0) {
                for (LupGridUnitVO a : unitList) {
                    a.setCodeLength(a.getCode().length());
                }

                int maxCodeLength = unitList.stream()
                        .mapToInt(LupGridUnitVO::getCodeLength)
                        .max()
                        .orElseThrow(() -> new IllegalStateException("List is empty"));
                List<LupGridUnitVO> maxUnits = unitList.stream()
                        .filter(unit -> unit.getCodeLength() == maxCodeLength)
                        .collect(Collectors.toList());

                if (maxUnits != null && maxUnits.size() > 0) {
                    LupGridUnitVO unit = maxUnits.get(0);
//                    unitList.stream().max(Comparator.comparing(LupGridUnitVO::getCodeLength)).get();

                    if (unit != null) {
                        bill.setGridCode(unit.getCode());
                        bill.setGridName(unit.getName());

                        bill.setGridUnitId(unit.getId());

                        if (unit.getPid() == 0) {
                            bill.setAreaCode(unit.getCode());
                            bill.setAreaName(unit.getName());
                        } else {
                            LupGridUnit lupGridUnit = gridUnitMapper.selectById(unit.getPid());
                            bill.setAreaCode(lupGridUnit.getCode());
                            bill.setAreaName(lupGridUnit.getName());
                        }
                        //定位正确网格后更新工单数据
                        updateById(bill);
                    }

                }
            }
        });



        return Result.success(LocaleMessageUtil.getMessage(EventTipKey.CommonSuccess));
    }



    private String computeFlyTaskVideo(Long flyTaskId,String eventHappenedTime,Integer origin){

        String videoUrl = null;

        List<Dict> dictList = dictProvider.getValueList("videoPlayBackOrigin").getData();

        Map<String,Dict> dictMap = dictList.stream()  .collect(Collectors.toMap(Dict::getDictKey, Function.identity()));
        Dict dict = dictMap.get(origin.toString());

        if(dict!=null) {

            try {
                WaylineJobInfoDTO waylineJobInfoDTO = new WaylineJobInfoDTO();
                waylineJobInfoDTO.setFlightId(flyTaskId);
                CommonResult<WaylineJobInfoVO> commonResult = waylineJobFeign.waylineJobInfo(waylineJobInfoDTO);
                if (commonResult.isSuccess() && commonResult.getData() != null) {
                    WaylineJobInfoVO waylineJobInfoVO = commonResult.getData();

                    LocalDateTime startTime = DateUtil.toLocalDateTime(new Date(waylineJobInfoVO.getLiveBeginTime()));

                    // 定位发生时间的前5秒的时间
                    LocalDateTime e = DateUtil.parseLocalDateTime(eventHappenedTime);
                    LocalDateTime timeBeforeEvent = e.minusSeconds(5);
                    // 检查定位的时间是否早于开始时间
                    LocalDateTime resultTime = timeBeforeEvent.isBefore(startTime) ? startTime : timeBeforeEvent;
                    String eventTime = resultTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() + "";


                    //http://127.0.0.1:5173/lup-fc/playback?taskId=1801881803493265410&eventTime=1718437265000
                    videoUrl = "taskId=" + flyTaskId + "&eventTime=" + eventTime;
                    //eventTime

                }
            }catch (Exception exception){
                exception.printStackTrace();
            }

        }

        return videoUrl;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean switchBillEnable(SwitchBillEnableDTO switchBillEnableDTO) {

        pendingService.clearBillCache(switchBillEnableDTO.getBillId());

        LupBill bill = new LupBill();
        bill.setId(switchBillEnableDTO.getBillId());
        bill.setUsable(switchBillEnableDTO.getUsable());
        updateById(bill);

        LupBillUsable billUsable = new LupBillUsable();
        billUsable.setBillId(switchBillEnableDTO.getBillId());
        billUsable.setUsable(switchBillEnableDTO.getUsable());
        billUsable.setReason(switchBillEnableDTO.getReason());
        billUsableService.save(billUsable);

        if(switchBillEnableDTO.getFileUrlList()!=null && switchBillEnableDTO.getFileUrlList().size()>0) {
            List<LupBillUsableFile> billUsableFileList = new ArrayList<>();
            switchBillEnableDTO.getFileUrlList().forEach(urlLink->{
                LupBillUsableFile billUsableFile = new LupBillUsableFile();
                billUsableFile.setBillUsableId(billUsable.getId());
                billUsableFile.setFileUrl(urlLink.getFileUrl());
                billUsableFile.setXlmFileId(urlLink.getXlmFileId());
                billUsableFileList.add(billUsableFile);
            });
            billUsableFileService.saveBatch(billUsableFileList);
        }

        return true;
    }

    @Override
    public List<BillUserLabelVO> listBillUserLabelVO(Long flowId) {

        List<BillUserLabelVO> list = baseMapper.listBillUserLabelVO(flowId);

        return list;
    }

    @Override
    public BillUserLabelVO listBillUserLabelOneVO(Long flowId) {
        return baseMapper.listBillUserLabelOneVO(flowId);
    }

    @Override
    @Transactional
    public Boolean changeBillEventType(BillEventTypeChangeDTO billEventTypeChangeDTO) {

        pendingService.clearBillCache(billEventTypeChangeDTO.getBillId());

        LupBill bill = getById(billEventTypeChangeDTO.getBillId());

        LupFlowEntity flowEntity = iLupFlowEntityService.getById(bill.getFlowEntityId());

        //记录切换记录
        LupBillSwitchGrid switchGrid = new LupBillSwitchGrid();
        switchGrid.setBillId(bill.getId());
        switchGrid.setFlowId(bill.getFlowEntityId());
        switchGrid.setProcessTaskId(flowEntity.getProcessTaskId());
        switchGrid.setOperateType(2);

        switchGrid.setOperateUserName(LmContextHolder.getUserName());

        LupDataLabel dataLabel = dataLabelService.getOne(Wrappers.<LupDataLabel>lambdaQuery().eq(LupDataLabel::getDataId,bill.getDataId()));

        LupLabel originalLabel = labelService.getOne(Wrappers.<LupLabel>lambdaQuery().eq(LupLabel::getDeleted,0)
                .eq(LupLabel::getId,dataLabel.getLabelId()));
        switchGrid.setOperateOriginalName(originalLabel.getName());



        LupLabel label = new LupLabel();
        label.setProjectId(Long.parseLong(LmContextHolder.getTenantId()));
        label.setName(billEventTypeChangeDTO.getLabelName());
        label = labelService.saveLabel(label);

        switchGrid.setOperateTargetName(label.getName());

        switchGridService.save(switchGrid);


        LupDataLabel tmp = new LupDataLabel();
        tmp.setId(dataLabel.getId());
        tmp.setLabelId(label.getId());

        dataLabelService.updateById(tmp);
        //判断事件类型是否已经存在

        return Boolean.TRUE;
    }
}
