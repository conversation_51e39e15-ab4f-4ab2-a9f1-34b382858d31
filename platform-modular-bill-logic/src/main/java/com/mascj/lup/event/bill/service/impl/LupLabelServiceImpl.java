package com.mascj.lup.event.bill.service.impl;

import cc.lyiot.framework.common.pojo.CommonResult;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.event.dimension.feign.IPlanTaskProvider;
import com.mascj.event.dimension.vo.LabelVO;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.lup.cds.ai.dto.AlgoListRespDTO;
import com.mascj.lup.cds.ai.feign.AiDeviceFeign;
import com.mascj.lup.event.bill.dto.LabelListDTO;
import com.mascj.lup.event.bill.entity.LupLabel;
import com.mascj.lup.event.bill.mapper.LupLabelMapper;
import com.mascj.lup.event.bill.service.ILupLabelService;
import com.mascj.lup.event.bill.service.LupEventLabelService;
import com.mascj.lup.event.bill.util.ProjectUtil;
import com.mascj.lup.event.bill.vo.EventDataLabelVO;
import com.mascj.lup.event.bill.vo.LabelItemVO;
import com.mascj.lup.event.bill.vo.LupLabelVO;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 事件标签 按名字去重 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
public class LupLabelServiceImpl extends ServiceImpl<LupLabelMapper, LupLabel> implements ILupLabelService {

    @Autowired
    private AiDeviceFeign deviceFeign;
    @Autowired
    private IPlanTaskProvider planTaskProvider;
    @Autowired
    private LupEventLabelService lupEventLabelService;
    @Override
    public List<EventDataLabelVO> labelList() {
        return baseMapper.labelList();
    }

    /**
     * 保存标签  新增或者查询同名字的标签数据id
     *
     * @param label
     * @return
     */
    @Override
    public LupLabel saveLabel(LupLabel label) {

        List<LupLabel> labelList = list(Wrappers.<LupLabel>lambdaQuery()
                .eq(LupLabel::getDeleted,0)
                .eq(LupLabel::getProjectId,label.getProjectId())
                .eq(LupLabel::getName,label.getName().trim()));

        if(labelList.size()>0){
            return labelList.get(0);
        }else {
            save(label);
        }

        return label;
    }

    @Override
    public Result<List<LabelItemVO>> listLabel(LabelListDTO labelDTO) {
        prepareDTO(labelDTO);

        List<Long> userId = new ArrayList<>();
        userId.add(LmContextHolder.getUserId());
        labelDTO.setLabelIdListPerm(lupEventLabelService.labelIdsByUserIds(userId));

        return Result.data(baseMapper.listLabel(labelDTO, ProjectUtil.getProjectId()));
    }

    private void prepareDTO(LabelListDTO dto){

        if(dto.getDateEnd() != null && !"".equals(dto.getDateEnd())){

            dto.setDateEnd(
                    dto.getDateEnd() + " 23:59:59"
            );
        }
    }

    @Override
    public LupLabel findLabelByDataId(Long dataId) {
        return baseMapper.findLabelByDataId(dataId);
    }

    @Override
    public List<LupLabelVO> allEventType() {


        Integer number = 1;
        List<LupLabelVO> list = new ArrayList<>();
        List<String> listName = new ArrayList<>();

        CommonResult<List<AlgoListRespDTO>> listCommonResult = deviceFeign.getAlgoList(0);

        if(listCommonResult.isSuccess()&& ObjectUtil.isNotEmpty(listCommonResult.getData())){

            for (AlgoListRespDTO item : listCommonResult.getData()) {
                if(!listName.contains(item.getAlgoName())) {
                    LupLabelVO lupLabelVO = new LupLabelVO();
                    lupLabelVO.setLabelName(item.getAlgoName());
                    lupLabelVO.setNumber(number++);
                    list.add(lupLabelVO);
                    listName.add(item.getAlgoName());
                }
            }
        }

        Result<List<LabelVO>> labelRes = planTaskProvider.findAllEventType();
        if(labelRes.isSuccess() && ObjectUtil.isNotEmpty(labelRes.getData()) && labelRes.getData().size()>0)
        {
            for (LabelVO e : labelRes.getData()) {
                if(!listName.contains(e.getName())) {
                    LupLabelVO lupLabelVO = new LupLabelVO();
                    lupLabelVO.setLabelName(e.getName());
                    lupLabelVO.setNumber(number++);
                    list.add(lupLabelVO);

                    listName.add(e.getName());
                }
            }
        }
        return list;
    }
}
