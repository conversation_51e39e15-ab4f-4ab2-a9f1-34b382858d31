package com.mascj.lup.event.bill.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.lup.event.bill.entity.LupFlowEntityTimeOut;
import com.mascj.lup.event.bill.mapper.LupFlowEntityTimeOutMapper;
import com.mascj.lup.event.bill.service.ILupFlowEntityTimeOutService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 流程记录表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
public class LupFlowEntityTimeOutServiceImpl extends ServiceImpl<LupFlowEntityTimeOutMapper, LupFlowEntityTimeOut> implements ILupFlowEntityTimeOutService {

}
