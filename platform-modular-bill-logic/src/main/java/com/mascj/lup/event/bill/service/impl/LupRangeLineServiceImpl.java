package com.mascj.lup.event.bill.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.util.StringUtil;
import com.mascj.kernel.common.util.beans.BeanUtil;
import com.mascj.lup.event.bill.dto.LabelListDTO;
import com.mascj.lup.event.bill.dto.LupRangeCategoryAddDTO;
import com.mascj.lup.event.bill.entity.LupLabel;
import com.mascj.lup.event.bill.entity.LupRangeCategory;
import com.mascj.lup.event.bill.entity.LupRangeLine;
import com.mascj.lup.event.bill.mapper.LupLabelMapper;
import com.mascj.lup.event.bill.mapper.LupRangeLineMapper;
import com.mascj.lup.event.bill.service.ILupLabelService;
import com.mascj.lup.event.bill.service.ILupRangeLineService;
import com.mascj.lup.event.bill.util.ProjectUtil;
import com.mascj.lup.event.bill.vo.EventDataLabelVO;
import com.mascj.lup.event.bill.vo.LabelItemVO;
import com.mascj.lup.event.bill.vo.LupRangeCategoryVO;
import com.mascj.lup.event.bill.vo.LupRangeLineVO;
import com.mascj.support.file.api.feign.IFileProvider;
import com.mascj.support.file.api.model.XlmFile;
import lombok.AllArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 事件标签 按名字去重 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
@AllArgsConstructor
public class LupRangeLineServiceImpl extends ServiceImpl<LupRangeLineMapper, LupRangeLine> implements ILupRangeLineService {


    @Override
    public List<LupRangeLineVO> listByRangeCategoryId(Long categoryId) {

        List<LupRangeLine> rangeLineList = list(Wrappers.<LupRangeLine>lambdaQuery()
                .eq(LupRangeLine::getDeleted,0)
                .eq(LupRangeLine::getRangeCategoryId,categoryId));
        List<LupRangeLineVO> lupRangeLineVOList = BeanUtil.copy(rangeLineList, LupRangeLineVO.class);
        return lupRangeLineVOList;
    }
}
