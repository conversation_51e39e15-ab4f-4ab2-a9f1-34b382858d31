package com.mascj.lup.event.bill.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.lup.event.bill.dto.BillReadStateCounterDTO;
import com.mascj.lup.event.bill.dto.BillReadStateDTO;
import com.mascj.lup.event.bill.entity.LupBillUsable;
import com.mascj.lup.event.bill.enums.BillReadStateEnum;
import com.mascj.lup.event.bill.mapper.LupBillUsableMapper;
import com.mascj.lup.event.bill.service.ILupBillUsableService;
import com.mascj.lup.event.bill.vo.BillReadStateCounterVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工单数据表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
@AllArgsConstructor
public class LupBillUsableServiceImpl extends ServiceImpl<LupBillUsableMapper, LupBillUsable> implements ILupBillUsableService {

}
