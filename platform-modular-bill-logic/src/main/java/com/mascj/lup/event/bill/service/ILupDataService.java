package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.vo.*;

import java.util.List;

/**
 * <p>
 * 事件数据 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface ILupDataService extends IService<LupData> {

    List<LupData> listByOriginalDataLogo(String originalDataLogo);

    List<EventDataBaseInfoVO> listAllEventData();


    IPage<EventDataTaskVO> pageByTask(IPage<EventDataTaskVO> page, EventDataTaskQueryDTO eventDataTaskQueryDTO);

    IPage<EventDataVO> pageEventData(IPage<EventDataVO> page, EventDataQueryDTO eventDataQueryDTO);
    IPage<EventDataVO> pageEventDataForLand(IPage<EventDataVO> page, EventDataForLandQueryDTO eventDataQueryDTO);

    EventDataVO detail(Long billId);

    List<CountEventDataVO> countByArea();
    List<CountEventDataVO> countByLabel();

    List<CountEventDataVO> countByLabelOnDay();

    CountEventDataVO countByOrigin(EventDataCountQueryDTO eventDataCountQueryDTO);

    CountEventDataOriginGroupVO countByOriginDataFetchWay(EventDataCountQueryDTO eventDataCountQueryDTO);

    CountEventDataDealResultVO countByDealResult(EventDataCountQueryDTO eventDataCountQueryDTO);

    List<FlyTaskReportBillCountVO> queryFlyTaskReportVO(FlyTaskReportDTO flyTaskReportDTO);

    Boolean dealOldDataPic(String endDate);

    List<BillQueryCountVO> billQueryCount(BillQueryCountDTO billQueryCountDTO);

    List<BillQueryListItemVO> billQueryList(BillQueryCountDTO billQueryCountDTO);
}
