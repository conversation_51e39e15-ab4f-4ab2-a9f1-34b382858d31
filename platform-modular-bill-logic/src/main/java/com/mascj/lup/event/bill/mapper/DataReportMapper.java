package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.mascj.lup.event.bill.dto.ReportSearchDTO;
import com.mascj.lup.event.bill.entity.DataReport;
import com.mascj.lup.event.bill.vo.DataReportVO;
import com.mascj.lup.event.bill.vo.TableDataVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DataReportMapper extends BaseMapper<DataReport> {

    IPage<DataReportVO> listPage(Page<DataReportVO> page, @Param("search") ReportSearchDTO search);

    boolean updateSearchParam(@Param("id")  String id, @Param("searchParam") String searchParam);

    @InterceptorIgnore(tenantLine = "true")
    List<TableDataVO> getAllTableNames(@Param("moduleCodeDBName") String moduleCodeDBName);

    List<Map<String,Object>> listAllDataExport(@Param("clist")String[] clist, @Param("tableName") String tableName);

}
