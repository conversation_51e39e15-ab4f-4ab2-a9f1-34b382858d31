package com.mascj.lup.event.bill.handler;

import com.mascj.kernel.common.util.$;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class OperationResolver {

    private static final Map<String, Method> methodMap = getMethod();

    private static Map<String, Method> getMethod() {
        Map<String, Method> result = new HashMap<>();
        List<Method> methodList = Arrays.stream(OperationResolver.class.getDeclaredMethods())
                .filter(x -> x.isAnnotationPresent(Operation.class))
                .collect(Collectors.toList());

        methodList.forEach(x -> {
            Operation operation = x.getDeclaredAnnotation(Operation.class);

            if ($.isNotBlank(operation.value()) && !result.containsKey(operation.value())) {
                result.put(operation.value(), x);
            }
            if ($.isNotEmpty(operation.values())) {
                Arrays.asList(operation.values()).forEach(val -> {
                    if (!result.containsKey(val)) {
                        result.put(val, x);
                    }
                });
            }
        });

        return result;
    }
}
