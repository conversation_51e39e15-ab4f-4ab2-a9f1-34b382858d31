package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.database.entity.Search;
import com.mascj.lup.event.bill.dto.LabelFlowBatchSaveDTO;
import com.mascj.lup.event.bill.dto.LabelFlowSaveDTO;
import com.mascj.lup.event.bill.entity.LupLabelFlow;
import com.mascj.lup.event.bill.vo.LupLabelFlowTimeLimitVO;
import com.mascj.lup.event.bill.vo.LupLabelFlowVO;
import com.mascj.platform.system.entity.Dict;

import java.util.List;

/**
 * <p>
 * 工单数据表 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface ILupLabelFlowService extends IService<LupLabelFlow> {

    Result<IPage<LupLabelFlowVO>> labelFlowPage(Integer codeValue, Search search);

    LupLabelFlowTimeLimitVO labelFlowDetail(Long id);


    Boolean batchSaveLabelFlow(LabelFlowBatchSaveDTO labelFlowBatchSaveDTO);

    Boolean saveLabelFlow(LabelFlowSaveDTO labelFlowSaveDTO);

    LupLabelFlow labelFlowDetailByState(Long flowId,Integer flowState);

}
