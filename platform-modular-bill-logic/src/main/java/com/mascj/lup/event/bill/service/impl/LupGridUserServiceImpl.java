package com.mascj.lup.event.bill.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.redis.core.RedisService;
import com.mascj.kernel.tools.RedisKey;

import com.mascj.kernel.tools.RedisPlane;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.GridUnitEditDTO;
import com.mascj.lup.event.bill.dto.LupGridUserDTO;
import com.mascj.lup.event.bill.dto.LupUserDTO;
import com.mascj.lup.event.bill.entity.LupGridUnit;
import com.mascj.lup.event.bill.entity.LupGridUser;

import com.mascj.lup.event.bill.mapper.LupGridUserMapper;
import com.mascj.lup.event.bill.service.ILupGridUnitService;
import com.mascj.lup.event.bill.service.ILupGridUserService;
import com.mascj.lup.event.bill.vo.LupGridUserVO;
import com.mascj.support.config.dto.XlmActivityState;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 网格用户关联表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
@AllArgsConstructor
public class LupGridUserServiceImpl extends ServiceImpl<LupGridUserMapper, LupGridUser> implements ILupGridUserService {

    private final RedisTemplate redisTemplate;

    @Override
    public List<LupGridUser> validAddGridUser(LupGridUserDTO gridUserDTO){
        List<LupGridUser> gridUserList = list(Wrappers.<LupGridUser>lambdaQuery()
                .eq(LupGridUser::getGridUnitId,gridUserDTO.getGridUnitId())
                .select(LupGridUser::getUserId)
        );

        boolean valid = true;

        List<Long> d = gridUserDTO.getUserList().stream().map(LupUserDTO::getId).collect(Collectors.toList());

        if((gridUserList.size() == gridUserDTO.getUserList().size()) && gridUserList.size()==0) valid = false;

        if(gridUserList.size() == gridUserDTO.getUserList().size() && valid) {
            for (LupGridUser gridUser : gridUserList) {
                if(!d.contains(gridUser.getUserId())){
                    valid=true;
                    break;
                }else {
                    valid=false;
                }
            }

        }

        Assert.isTrue(valid, LocaleMessageUtil.getMessage(EventTipKey.NoneChangeGridRight));
        return gridUserList;
    }
    /**
     *
     * 1、根据网格 修改网格下的用户权限；
     * 2、需要找出被删除的用户；
     * 3、需要找到被新增的用户；
     * 4、对用户从【父节点到子节点】新增/删除 关联数据；
     *
     *
     * @param gridUserDTO 用户关联表格记录表
     * @return
     */
    @Override
    public List<LupUserDTO> addGridUser(List<LupGridUser> gridUserList,LupGridUserDTO gridUserDTO,List<LupGridUnit> gridUnitList) {

        List<Long> d = gridUserDTO.getUserList().stream().map(LupUserDTO::getId).collect(Collectors.toList());

        List<LupUserDTO> addUserList = new ArrayList<>();
        List<Long> existUserIdList = gridUserList.stream().map(LupGridUser::getUserId).collect(Collectors.toList());
        gridUserDTO.getUserList().forEach(user->{
            if(!existUserIdList.contains(user.getId())){
                addUserList.add(user);
            }
        });
        //增加该网格下所有的 addUserList  用户的关联数据；

        //新增新的网格关联用户记录
        List<LupGridUser> finalGridUserList = new ArrayList<>();
        addUserList.forEach(user -> {
            gridUnitList.forEach(gridUnit->{
                LupGridUser gridUser = new LupGridUser();
                gridUser.setGridUnitId(gridUnit.getId());
                gridUser.setUserId(user.getId());
                gridUser.setUserName(user.getName());
                finalGridUserList.add(gridUser);

            });
            RedisKey redisKey = RedisKey.forService(VariableConstants.UNIT_SCOPE_CACHE, ILupGridUserService.class)
                    .forParameter(String.valueOf(LmContextHolder.getTenantId()),String.valueOf(user.getId()));

            redisTemplate.delete(redisKey.toString());

        });

        saveBatch(finalGridUserList);

        //对新增用的数据权限做缓存处理


        List<Long> delUserList = new ArrayList<>();
        gridUserList.forEach(user->{
            if(!d.contains(user.getUserId())){
                delUserList.add(user.getUserId());
            }
        });
        //删除该网格下所有  delUserList  用户的关联数据；

        delUserList.forEach(userId -> {
            baseMapper.delete(Wrappers.<LupGridUser>lambdaQuery().eq(LupGridUser::getUserId,userId));
            RedisKey redisKey = RedisKey.forService(VariableConstants.UNIT_SCOPE_CACHE, ILupGridUserService.class)
                    .forParameter(String.valueOf(LmContextHolder.getTenantId()),String.valueOf(userId));
            redisTemplate.delete(redisKey.toString());
            dataScope(userId);
        });

        return addUserList;
    }

    @Override
    public void saveUserInfo(Long userId,List<LupGridUnit> list){
        RedisKey redisKey = RedisKey.forService(VariableConstants.UNIT_SCOPE_CACHE, ILupGridUserService.class)
                .forParameter(String.valueOf(LmContextHolder.getTenantId()),String.valueOf(userId));
        redisTemplate.opsForValue().set(redisKey.toString(),list);
    }
    @Override
    public List<LupGridUnit> getUserLupGridUnitInfo(Long userId){

        RedisKey redisKey = RedisKey.forService(VariableConstants.UNIT_SCOPE_CACHE, ILupGridUserService.class)
                .forParameter(String.valueOf(LmContextHolder.getTenantId()),String.valueOf(userId));
        List<LupGridUnit> list = (List<LupGridUnit>)redisTemplate.opsForValue().get(redisKey.toString());
        return list;
    }

    @Override
    public List<LupGridUser> getGridUserByGridId(Long gridId) {

        List<LupGridUser>  gridUserList = list(Wrappers.<LupGridUser>lambdaQuery()
                .eq(LupGridUser::getDeleted,0)
                .eq(LupGridUser::getGridUnitId,gridId));

        return gridUserList;
    }

    @Override
    public boolean delGridUser(LupGridUserDTO gridUserDTO) {
        return false;
    }

    @Override
    public List<Long> dataScope(Long userId) {
        List<Long> gridUnitIdList = list(Wrappers.<LupGridUser>lambdaQuery()
                .eq(LupGridUser::getUserId, userId)
                .select(LupGridUser::getGridUnitId)
        ).stream().map(LupGridUser::getGridUnitId).collect(Collectors.toList());

        return gridUnitIdList;
    }

    @Override
    public LupGridUserVO getGridUserList(Long gridUnitId) {

        LupGridUserVO gridUserVO = new LupGridUserVO();
        gridUserVO.setGridUnitId(gridUnitId);

        List<LupUserDTO> list = list(Wrappers.<LupGridUser>lambdaQuery()
                .eq(LupGridUser::getGridUnitId, gridUnitId))
                .stream().map(
                        user->{
                            LupUserDTO userDTO = new LupUserDTO();

                            userDTO.setId(user.getUserId());
                            userDTO.setName(user.getUserName());

                            return userDTO;
                        }).collect(Collectors.toList());

        gridUserVO.setUserList(list);

        return gridUserVO;
    }
}
