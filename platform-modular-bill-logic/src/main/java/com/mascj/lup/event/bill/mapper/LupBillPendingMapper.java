package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.dto.LupBillPendingOldDataDTO;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupBillPending;
import com.mascj.lup.event.bill.entity.LupSource;
import com.mascj.lup.event.bill.vo.LupBillPendingVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 事件资源记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface LupBillPendingMapper extends BaseMapper<LupBillPending> {

    @InterceptorIgnore(tenantLine = "true")
    List<LupBillPendingVO> queryHistoryPendingBill(@Param("data") LupBillPendingOldDataDTO billPendingOldDataDTO);



}
