package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.database.entity.Search;
import com.mascj.lup.event.bill.entity.LupEventSmsGrade;
import com.mascj.lup.event.bill.vo.LupEventLabelVo;

import java.util.Date;

/**
 * <p>
 * 处置分级短信 服务类
 * </p>
 *
 * <AUTHOR> @since 2024-01-01
 */
public interface ILupEventSmsGradeService extends IService<LupEventSmsGrade> {

    Result set(LupEventSmsGrade lupEventSmsGrade);

    Result<IPage<LupEventLabelVo>> listPage(Integer codeValue, Search search);


    void smsGrade(Long flowEntityId,Integer currentFlowState,  Date date);
}