package com.mascj.lup.event.bill.service;

import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.EventDataDTO;
import com.mascj.lup.event.bill.dto.FixEventDataSourceDTO;
import com.mascj.lup.event.bill.dto.FlySystemDelDTO;
import com.mascj.lup.event.bill.dto.FlySystemEventDataSourceDTO;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.vo.LupBillVO;
import com.mascj.lup.event.bill.vo.SendFourDoVo;
import com.mascj.lup.event.bill.vo.SendFourResutVo;

public interface IDataReceiveService {

    /**
     * 【无人机平台】提交事件数据
     * @param flySystemEventDataSourceDTO
     * @return
     */
    Result postCompareDataSystem(FlySystemEventDataSourceDTO flySystemEventDataSourceDTO);

    /**
     * 方法废弃不适用了 张保国  2024-01-10
     * @param billId
     * @return
     */
    @Deprecated
    Result pushEventData(Long billId);

    Result<SendFourResutVo> pushEventDataFour(SendFourDoVo sendFourDoVo);

    void pushEventDataToOtherServer(Long dataId);
    void doesNotPushEventData(Long dataId);



    Result translateData(LupData lupData, LupBillVO lupBill, String labelName);

    Result delFlyEventData(FlySystemDelDTO flySystemDelDTO);


    /**
     * 【比对系统】提交事件数据
     * @param eventData
     * @return
     */
    Result postEventData(EventDataDTO eventData);


    void dealReceiveData(EventDataDTO eventData);

    Result fixEventData(FixEventDataSourceDTO fixEventDataSourceDTO);
}
