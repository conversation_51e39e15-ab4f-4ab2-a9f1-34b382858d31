package com.mascj.lup.event.bill.service.impl.push.stage;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.*;
import com.mascj.lup.event.bill.enums.DataPushStateEnum;
import com.mascj.lup.event.bill.enums.GenerateBillFlagEnum;
import com.mascj.lup.event.bill.mapper.LupLabelYonganCategoryMapper;
import com.mascj.lup.event.bill.mapper.YonganCategoryMapper;
import com.mascj.lup.event.bill.service.*;
import com.mascj.lup.event.bill.util.GeoLocation;
import com.mascj.lup.event.bill.util.KXPlatformClient;
import com.mascj.lup.event.bill.util.ReadConfigUtil;
import com.mascj.lup.event.bill.vo.SendFourResutVo;
import com.mascj.lup.event.bill.vo.YonganEventReportEntity;
import com.mascj.lup.event.bill.vo.config.HandleModuleConfigVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/3 18:36
 * @describe
 */
@AllArgsConstructor
@Slf4j
@Service("yonganTranslateService")
public class YonganataServiceImpl implements IPushDataService {

    private final ReadConfigUtil readConfigUtil;

    private ILupDataService iLupDataService;

    private ILupBillService billService;
    private ILupDataPushLogService dataPushLogService;

    private ILupLabelService labelService;
    private LupLabelYonganCategoryMapper lupLabelYonganCategoryMapper;
    private YonganCategoryMapper yonganCategoryMapper;



    @Override
    public Result<SendFourResutVo> translateData(PushKQDTO pushKQDTO, LupData lupData, LupBill lupBill, String labelName) throws Exception {

        return  Result.data(null);
    }

    /**
     *
     *
     * @param dataId
     * @return
     */
    @Override
    public Result translateData(Long dataId,Long pushLogId) {


        log.error("开始处理 永安数据 推送 数据  dataId:{}",dataId);
        LupLabel label = labelService.findLabelByDataId(dataId);

        LupLabelYonganCategory lupLabelYonganCategory = lupLabelYonganCategoryMapper.selectOne(new LambdaQueryWrapper<>(new LupLabelYonganCategory())
                .eq(LupLabelYonganCategory::getLabelId, label.getId()));
        if(lupLabelYonganCategory == null){
            log.error("未找到标签对应 关联关系 无需处理");

            return Result.fail("未找到标签对应 关联关系 无需处理");
        }
        Long yonganCategoryId = lupLabelYonganCategory.getYonganCategoryId();
        YonganCategory yonganCategory = yonganCategoryMapper.selectById(yonganCategoryId);
        if(yonganCategory == null){
            log.error(" 对应永安数据类别不存在 无需处理 ");
            return Result.fail("对应永安数据类别不存在 无需处理 ");
        }

        log.error("开始处理 永安数据 推送 数据  dataId:{},yonganCategoryId:{}",dataId,yonganCategoryId);


        LupDataPushLog dataPushLog = new LupDataPushLog();
        dataPushLog.setId(pushLogId);

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();
        PushKQDTO pushKQDTO = handleModuleConfigVO.getPushKQDTO();

        GenerateBillFlagEnum generateBillFlagEnum = GenerateBillFlagEnum.parse(pushKQDTO.getGenerateBillFlag());


        LupData data = iLupDataService.getById(dataId);


        // 处理组装数据：
        YonganEventReportEntity yonganEventDataDTO = new YonganEventReportEntity();








        LupData dataRes = new LupData();
        dataRes.setId(data.getId());


            try {

                GHTranslateDataDTO translateDataDTO = new GHTranslateDataDTO();
                translateDataDTO.setUavName(data.getDeviceName());
                if(ObjectUtil.isEmpty(data.getHappenedTime())) {
                    translateDataDTO.setEventDate(DateUtil.format(data.getCreateTime(), "yyyy-MM-dd"));
                    translateDataDTO.setActionDate(DateUtil.format(data.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                }else{
                    translateDataDTO.setEventDate(DateUtil.format(
                            DateUtil.parseLocalDateTime(
                            data.getHappenedTime()), "yyyy-MM-dd"));
                    translateDataDTO.setActionDate(DateUtil.format(DateUtil.parseLocalDateTime(
                            data.getHappenedTime()), "yyyy-MM-dd HH:mm:ss"));
                }
                translateDataDTO.setEventTitle(label.getName());
                translateDataDTO.setEventDesc(data.getEventDesc());
                List<GHTranslateCommonResourcesDTO> resourcesDTOList = new ArrayList<>();
                DataPicDTO dataPicDTO = JSONUtil.toBean(data.getExtraData(), DataPicDTO.class);
                //多张图
                if (dataPicDTO.getEventPictureUrlList() == null) {
                    dataPicDTO.setEventPictureUrlList(new ArrayList<>());
                }
                if (!dataPicDTO.getEventPictureUrlList().contains(dataPicDTO.getEventPictureUrl())) {
                    dataPicDTO.getEventPictureUrlList().add(dataPicDTO.getEventPictureUrl());
                }


                for (int i = 0; i < dataPicDTO.getEventPictureUrlList().size(); i++) {
                    String res = dataPicDTO.getEventPictureUrlList().get(i);

                    GHTranslateCommonResourcesDTO resourcesDTO = new GHTranslateCommonResourcesDTO();
                    resourcesDTO.setResourceUrl(res);
                    resourcesDTO.setResourceType(1);
                    resourcesDTO.setResourceName(res.substring(res.lastIndexOf("/") + 1));
                    resourcesDTO.setSort(i + 1);
                    resourcesDTOList.add(resourcesDTO);
                }
                translateDataDTO.setCommonResourcesDTOS(resourcesDTOList);
                //translateDataDTO
                translateDataDTO.setLocation(data.getLocationLng().toString() +
                        "," +
                        data.getLocationLat().toString());

                KXPlatformClient platformClient = new KXPlatformClient(pushKQDTO.getHost(), pushKQDTO.getAppKey(), pushKQDTO.getAppSecret());

                System.out.println("[GH]Request:" + JSONUtil.toJsonStr(translateDataDTO));
                //推送数据到第三方
                String response = platformClient.gatewayRequestGH(pushKQDTO.getPushPath(), pushKQDTO.getPushPathCode(), translateDataDTO);
                //成功时 更新 推送状态；增加推送记录；
                JSONObject resJson = JSONUtil.parseObj(response);

                dataRes.setPushMsg(resJson.getStr("msg"));

//                {"code":0,"msg":"无人机识别的环境问题上报成功"}

                //历史的dataId 查出推送日志pushable =

                if (resJson.getInt("code") == 0) {

                    List<LupDataPushLog> dataPushLogList = dataPushLogService.list(Wrappers.<LupDataPushLog>lambdaQuery().eq(LupDataPushLog::getDataId,dataId).ne(LupDataPushLog::getId,pushLogId));

                    List<LupDataPushLog> tmpDataPushLogList = new ArrayList<>();
                    dataPushLogList.forEach(e->{
                        LupDataPushLog log = new LupDataPushLog();
                        log.setId(e.getId());
                        log.setPushable(0);//是否可以重新推送 0否 1是
                        tmpDataPushLogList.add(log);

                    });
                    if(tmpDataPushLogList.size()>0)
                        dataPushLogService.updateBatchById(tmpDataPushLogList);

                    dataPushLog.setPushState(DataPushStateEnum.PushSuccess.getCode());
                    dataPushLog.setPushTime(DateUtil.now());
                    dataPushLog.setPushable(0);
                    dataRes.setPushState(DataPushStateEnum.PushSuccess.getCode());
                } else {
                    dataPushLog.setPushMsg(resJson.getStr("msg"));
                    dataPushLog.setPushState(DataPushStateEnum.PushFail.getCode());
                    dataRes.setPushState(DataPushStateEnum.PushFail.getCode());
                }

                System.out.println("[GH]response:" + response);

            } catch (Exception exp) {

                dataPushLog.setPushState(DataPushStateEnum.PushFail.getCode());
                dataRes.setPushState(DataPushStateEnum.PushFail.getCode());

                exp.printStackTrace();
                if(exp.getMessage().contains("code")
                        && exp.getMessage().contains("msg")
                        && exp.getMessage().contains("[{")
                        && exp.getMessage().contains("}]")){

                    //500 Internal Server Error: [{"code":7,"msg":"系统发生异常，请联系管理员"}]

                    String res = exp.getMessage().substring( exp.getMessage().lastIndexOf("[{")+1,exp.getMessage().lastIndexOf("}]")+1);

                    JSONObject resJson = JSONUtil.parseObj(res);

                    dataPushLog.setPushMsg(resJson.getStr("msg"));
                    dataRes.setPushMsg(resJson.getStr("msg"));

                }else {

                    dataPushLog.setPushMsg(exp.getMessage());
                    dataRes.setPushMsg(exp.getMessage());
                }


            }




        iLupDataService.updateById(dataRes);
        dataPushLogService.updateById(dataPushLog);

        return Result.data(dataPushLog);
    }

    public static void main(String []args){
        String res = "500 Internal Server Error: [{\"code\":7,\"msg\":\"系统发生异常，请联系管理员\"}]";
        System.out.println(res.substring(res.lastIndexOf("[{")+1,res.lastIndexOf("}]")+1));
    }
}
