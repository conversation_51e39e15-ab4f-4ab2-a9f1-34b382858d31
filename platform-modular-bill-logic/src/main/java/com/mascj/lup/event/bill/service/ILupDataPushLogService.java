package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.lup.event.bill.dto.ExportFileDTO;
import com.mascj.lup.event.bill.dto.LupDataPushLogPagedDTO;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.entity.LupDataPushLog;
import com.mascj.lup.event.bill.vo.LupDataPushLogExcelVO;
import com.mascj.lup.event.bill.vo.LupDataPushLogPagedItemVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/8 19:58
 * @describe
 */
public interface ILupDataPushLogService  extends IService<LupDataPushLog> {

    Page<LupDataPushLogPagedItemVO> pagedDataPushLog(LupDataPushLogPagedDTO dataPushLogPagedDTO);

    List<LupDataPushLogExcelVO> exportDataToExcel(ExportFileDTO exportFileDTO);

}
