package com.mascj.lup.event.bill.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.util.$;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.common.util.StringUtil;
import com.mascj.kernel.common.util.beans.BeanUtil;
import com.mascj.kernel.web.tree.ForestNodeMerger;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.dto.LupRangeCategoryAddDTO;
import com.mascj.lup.event.bill.entity.LupRangeCategory;
import com.mascj.lup.event.bill.entity.LupRangeLine;
import com.mascj.lup.event.bill.mapper.LupRangeCategoryMapper;
import com.mascj.lup.event.bill.mapper.LupRangeLineMapper;
import com.mascj.lup.event.bill.service.ILupRangeCategoryService;
import com.mascj.lup.event.bill.service.ILupRangeLineService;
import com.mascj.lup.event.bill.vo.LupRangeCategoryVO;
import com.mascj.support.file.api.feign.IFileProvider;
import com.mascj.support.file.api.model.XlmFile;
import lombok.AllArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 事件标签 按名字去重 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
@AllArgsConstructor
public class LupRangeCategoryServiceImpl extends ServiceImpl<LupRangeCategoryMapper, LupRangeCategory> implements ILupRangeCategoryService {

    private final IFileProvider fileProvider;
    private final ILupRangeLineService rangeLineService;

    @Transactional
    @Override
    public Result add(LupRangeCategoryAddDTO rangeCategoryAddDTO) {


        LupRangeCategory rangeCategory = BeanUtil.copy(rangeCategoryAddDTO, LupRangeCategory.class);

        // 名称去重查询
        LambdaQueryWrapper<LupRangeCategory> eq = new LambdaQueryWrapper<LupRangeCategory>().eq(LupRangeCategory::getDeleted, 0).eq(LupRangeCategory::getName, rangeCategory.getName());
        if (Objects.nonNull(rangeCategory.getId())) {
            eq.ne(LupRangeCategory ::getId , rangeCategory.getId());
        }
        int count = this.count(eq);
        if (count > 0) {
            throw new RuntimeException(LocaleMessageUtil.getMessage(EventTipKey.DuplicationRangeName));
        }


        // 新增到子表  只有当类型为文件 且 jsonstr不为null时
        if (rangeCategory.getType().equals(2)) {
            //读取文件
            readFileContent(rangeCategoryAddDTO);

            rangeCategory = BeanUtil.copy(rangeCategoryAddDTO, LupRangeCategory.class);
            this.saveOrUpdate(rangeCategory);

            if (Objects.isNull(rangeCategoryAddDTO.getXmlFileId()) &&  rangeCategoryAddDTO.getShape() == null) {
                throw  new RuntimeException(LocaleMessageUtil.getMessageByKeyList(EventTipKey.EmptyRangeFile, Arrays.asList(EventTipKey.NoneTip)));
            }
            // 修改时删除之前的数据
            if (Objects.nonNull(rangeCategoryAddDTO.getShape())) {
                if (Objects.nonNull(rangeCategoryAddDTO.getXmlFileId())) {
                    rangeLineService.remove(new LambdaQueryWrapper<LupRangeLine>().eq(LupRangeLine ::getRangeCategoryId , rangeCategory.getId()));
                }
                JSONArray arr = rangeCategoryAddDTO.getShape().getJSONArray("features");
                List<LupRangeLine> rangeLineList = new ArrayList<>();
                if ($.isNotEmpty(arr)) {
                    try {
                        this.checkFile(rangeCategoryAddDTO.getShape());
                    } catch (Exception e)  {
                        throw new RuntimeException(e.getMessage());
                    }

                    // 校验文件
                    for (int i = 0; i < arr.size(); i++) {

                        LupRangeLine rangeLine = new LupRangeLine();
                        rangeLine.setRangeCategoryId(rangeCategory.getId());

                        JSONObject jsonObject = (JSONObject) arr.get(i);
                        JSONObject properties = jsonObject.getObject("properties", JSONObject.class);
                        rangeLine.setProperties(properties.toJSONString());

                        JSONObject geometry = jsonObject.getObject("geometry", JSONObject.class);
                        rangeLine.setGeometry(geometry.toJSONString());

                        if(properties.containsKey("area")) {
                            String gridArea = properties.get("area").toString();
                            rangeLine.setArea(gridArea);
                        }

                        if(properties.containsKey("lat")) {
                            String lat = properties.get("lat").toString();
                            rangeLine.setLat(new BigDecimal(lat));
                        }

                        if(properties.containsKey("lng")) {
                            String lng = properties.get("lng").toString();
                            rangeLine.setLng(new BigDecimal(lng));
                        }

                        if(properties.containsKey("remark")) {
                            String remark = properties.get("remark").toString();
                            rangeLine.setRemark(remark);
                        }

                        rangeLineList.add(rangeLine);
                    }

                    rangeLineService.saveBatch(rangeLineList);
                }
            }
        }else {
            rangeCategory = BeanUtil.copy(rangeCategoryAddDTO, LupRangeCategory.class);
            this.saveOrUpdate(rangeCategory);
        }
        return null;
    }

    @Override
    public List<LupRangeCategoryVO> listAll() {
        List<LupRangeCategory> list = this.list(new LambdaQueryWrapper<LupRangeCategory>()
                .eq(LupRangeCategory ::getDeleted , 0)
                .orderByAsc(LupRangeCategory ::getType));

        if ($.isNotEmpty(list)) {
            List<LupRangeCategoryVO> rangeCategoryVOList = BeanUtil.copy(list, LupRangeCategoryVO.class);
            List<LupRangeCategoryVO> merge = ForestNodeMerger.merge(rangeCategoryVOList);
            return merge;
        }
        return new ArrayList<>();
    }

    private void readFileContent(LupRangeCategoryAddDTO rangeCategoryAddDTO){

        InputStream in = null;
        String jsonStr = "";
        try {
            in = rangeCategoryAddDTO.getFile().getInputStream();
            jsonStr = IOUtils.toString(in, "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        try {
            JSONObject obj = getObjects(jsonStr);
            this.checkFile(obj);

            rangeCategoryAddDTO.setShape(obj);

        } catch (Exception e)  {
            throw new RuntimeException(e.getMessage());
        }
        try {
            Result<XlmFile> result = fileProvider.upload(rangeCategoryAddDTO.getFile());
            XlmFile data = result.getData();
            if (Objects.nonNull(data)) {
                rangeCategoryAddDTO.setXmlFileId(data.getId());
                return;
            }
            throw new RuntimeException(LocaleMessageUtil.getMessage(EventTipKey.FailFileUpload));

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Nullable
    private static JSONObject getObjects(String jsonStr) {
        try {
            JSONObject obj = JSONObject.parseObject(jsonStr);
            return obj;
        } catch (Exception e) {
            throw new RuntimeException(LocaleMessageUtil.getMessage(EventTipKey.ErrorFileFormat));
        }

    }

    private void checkFile(JSONObject obj) {
        JSONArray arr = obj.getJSONArray("features");
        for (int i = 0; i < arr.size(); i++) {
            JSONObject jsonObject = (JSONObject) arr.get(i);

            if ( !jsonObject.containsKey("properties") ) {
                throw new RuntimeException(LocaleMessageUtil.getMessage(EventTipKey.CheckBillFile)+"-" +(i+1));
            }

            JSONObject geometry = jsonObject.getObject("geometry", JSONObject.class);
            if (StringUtil.isBlank(geometry.toJSONString())) {
                throw new RuntimeException(LocaleMessageUtil.getMessage(EventTipKey.CheckBillFile)+"-" +(i+1));
            }
        }
    }
}
