package com.mascj.lup.event.bill.service.impl;

import cc.lyiot.framework.common.pojo.CommonResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.event.dimension.enumeration.ModuleDataSourceTypeEnum;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.cds.survey.feign.SurveyAchievementFeign;
import com.mascj.lup.datacenter.client.feign.DataCenterClientTaskFeign;
import com.mascj.lup.datacenter.client.vo.res.AchievementClientPreviewVO;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.entity.LupSource;
import com.mascj.lup.event.bill.enums.EventSourceType;
import com.mascj.lup.event.bill.geo.GeoTile;
import com.mascj.lup.event.bill.mapper.LupSourceMapper;
import com.mascj.lup.event.bill.service.ILupSourceService;
import com.mascj.lup.event.bill.util.ProjectUtil;
import com.mascj.lup.event.bill.vo.SourceTileVO;
import com.mascj.lup.event.feign.EventStreamFeign;
import com.mascj.lup.event.vo.AchievementPreviewVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 事件资源记录表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
@Slf4j
@AllArgsConstructor
public class LupSourceServiceImpl extends ServiceImpl<LupSourceMapper, LupSource> implements ILupSourceService {

    private final DataCenterClientTaskFeign streamProvider;
    private final SurveyAchievementFeign surveyAchievementFeign;
    @Override
    public Result<List<SourceTileVO>> listSourceTile(BillDTO billDTO) {

        billDTO.setProjectId(ProjectUtil.getProjectId());

        List<SourceTileVO> sourceTileList = new ArrayList<>();

        List<LupSource> lupSourceList = baseMapper.listSourceTile(billDTO);

        Map<String,List<LupSource>> sourceListMap = lupSourceList.stream().collect(Collectors.groupingBy(LupSource::getFlyDate));

        sourceListMap.keySet().forEach(flyDate->{

            SourceTileVO sourceTileVO = new SourceTileVO();
            sourceTileVO.setFlyDate(flyDate);

            List<GeoTile> tileList = new ArrayList<>();
            sourceTileVO.setTileList(tileList);

            List<LupSource> list = sourceListMap.get(flyDate);
            for (LupSource item : list) {
                GeoTile geoTile = new GeoTile();

                geoTile.setOriginalTileSourceId(item.getId());

                EventSourceType eventSourceType = EventSourceType.parse(item.getType());

                if(eventSourceType == EventSourceType.TileSourceType){
                    AchievementClientPreviewVO achievementPreviewVO = fetchTileOptions(item.getOriginalSourceId());
                    if(achievementPreviewVO == null) continue;

                    geoTile.setFlipFlag(achievementPreviewVO.getFlipFlag());
                    geoTile.setTileUrl(achievementPreviewVO.getPreviewUrl());
                    geoTile.setFlyDate(flyDate);
                    geoTile.setMinZoom(achievementPreviewVO.getMinZoom());
                    geoTile.setMaxZoom(achievementPreviewVO.getMaxZoom());
                    geoTile.setTileBounds(JSONUtil.toJsonStr(achievementPreviewVO.getBounds()));
                    geoTile.setTileName(item.getSourceName());
                }else {

                        if(item.getSourceType() == ModuleDataSourceTypeEnum.CompareModuleSource.getValue()){
                            geoTile.setTileUrl(item.getUrl());
                        }else{
                            // 尝试获取瓦片预览数据，如果失败则跳过该瓦片而不是中断整个流程
                            boolean tileDataLoaded = false;
                            try {
                                CommonResult commonResult = surveyAchievementFeign.preview(item.getOriginalTileSourceId());
                                log.info("commonResult:"+JSONUtil.toJsonStr(commonResult));
                                if(commonResult != null && commonResult.getData() != null && !"".equals(commonResult.getData())) {
                                    AchievementClientPreviewVO achievementPreviewVO = BeanUtil.toBean(commonResult.getData(), AchievementClientPreviewVO.class);
                                    geoTile.setTileUrl(achievementPreviewVO.getPreviewUrl());
                                    geoTile.setFlipFlag(achievementPreviewVO.getFlipFlag());
                                    tileDataLoaded = true;
                                } else {
                                    log.warn("获取瓦片预览数据为空，originalTileSourceId: {}, 跳过该瓦片", item.getOriginalTileSourceId());
                                }
                            } catch (Exception e) {
                                log.error("调用surveyAchievementFeign.preview异常，originalTileSourceId: {}, 跳过该瓦片", item.getOriginalTileSourceId(), e);
                            }

                            // 如果无法获取瓦片数据，跳过该瓦片，继续处理其他瓦片
                            if (!tileDataLoaded) {
                                log.info("跳过无效瓦片数据，originalTileSourceId: {}", item.getOriginalTileSourceId());
                                continue;
                            }
                        }


                    geoTile.setFlyDate(flyDate);
                    geoTile.setMinZoom(item.getMinZoom());
                    geoTile.setMaxZoom(item.getMaxZoom());
                    geoTile.setTileBounds(item.getBounds());
                    geoTile.setTileName(item.getSourceName());

                }

                tileList.add(geoTile);
            }

            sourceTileList.add(sourceTileVO);

        });

        List<SourceTileVO> list = sourceTileList.stream().sorted(Comparator.comparing(SourceTileVO::getFlyDate).reversed()).collect(Collectors.toList());;
        return Result.data(list);
    }

    /**
     * 根据 航线任务id
     * @param airlineTaskId
     * @return 返回瓦片预览数据，如果获取失败返回null
     */
    @Override
    public AchievementClientPreviewVO fetchTileOptions(Long airlineTaskId){
        if (airlineTaskId == null) {
            log.warn("航线任务ID为空，无法获取瓦片数据");
            return null;
        }
        try {
            Result<AchievementClientPreviewVO> previewVOResult = streamProvider.preview(airlineTaskId);
            if (previewVOResult == null) {
                log.warn("航线任务瓦片数据查询结果为空，航线任务ID：{}", airlineTaskId);
                return null;
            }
            if (previewVOResult.getData() == null) {
                log.warn("航线任务瓦片数据不存在或已被删除，航线任务ID：{}", airlineTaskId);
                return null;
            }

            return previewVOResult.getData();

        } catch (Exception e) {
            // 记录异常日志但不抛出异常，返回null让调用方处理
            log.error("航线任务瓦片数据查询异常，航线任务ID：{}", airlineTaskId, e);
            return null;
        }
    }

    /**
     * 保存资源 资源已经存在 返回 已存储的资源的id
     *
     * @param source
     * @return
     */
    @Override
    public LupSource saveSource(LupSource source) {

        //类型 1 通过original_source_id查瓦片数据  2 图片地址 3 瓦片地址（自定义的）
        EventSourceType sourceType = EventSourceType.parse(source.getType());
        List<LupSource> sourceList = null;
        if(sourceType == EventSourceType.PictureSourceType) {
            sourceList = list(Wrappers.<LupSource>lambdaQuery().eq(LupSource::getUrl, source.getUrl().trim()));
        }else {
            if(sourceType == EventSourceType.TileSourceType) {
                sourceList = list(Wrappers.<LupSource>lambdaQuery()
                        .eq(LupSource::getDeleted, 0)
                        .eq(LupSource::getProjectId, source.getProjectId())
                        .eq(LupSource::getOriginalSourceId, source.getOriginalSourceId()));
            }
            else {
                sourceList = list(Wrappers.<LupSource>lambdaQuery()
                        .eq(LupSource::getDeleted, 0)
                        .eq(LupSource::getProjectId, source.getProjectId())
                        .eq(LupSource::getUrl, source.getUrl()));
                if(sourceList.size()>0){
                    source.setId(sourceList.get(0).getId());
                    updateById(source);
                }
            }
        }

        if(sourceList.size()>0){
            source.setId(sourceList.get(0).getId());
        }else {
            save(source);
        }

        return source;
    }

    @Override
    public void saveBatchSource(List<LupSource> sourceList) {

        sourceList.forEach(e->{

            saveSource(e);

        });

    }
}
