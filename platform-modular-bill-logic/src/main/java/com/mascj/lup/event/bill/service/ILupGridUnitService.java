package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.dto.ListUnitDTO;
import com.mascj.lup.event.bill.dto.LupGridUnitDTO;
import com.mascj.lup.event.bill.entity.LupGridUnit;
import com.mascj.lup.event.bill.vo.GriUnitVO;
import com.mascj.lup.event.bill.vo.GridUnitShapeVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 网格数据表 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface ILupGridUnitService extends IService<LupGridUnit> {
    void importData(MultipartFile file);

    Result addUnitData(LupGridUnitDTO lupGridUnit);

    Result<List<GriUnitVO>> listUnit(ListUnitDTO unitDTO);

    Result<GridUnitShapeVO> listChildUnit(ListUnitDTO unitDTO);


    List<LupGridUnit> allUnit();

    List<LupGridUnit> allUnit(Long userId);

    /**
     * 根据 unitId 返回包含自己的 网格id
     * @param unitId
     * @return
     */
    List<LupGridUnit> listByUnitId(Long unitId);

}
