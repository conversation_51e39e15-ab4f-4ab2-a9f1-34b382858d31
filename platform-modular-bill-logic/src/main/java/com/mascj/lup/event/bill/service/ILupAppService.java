package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.LupAppAuthCodeDTO;
import com.mascj.lup.event.bill.dto.LupAppDTO;
import com.mascj.lup.event.bill.dto.LupAppOperateDTO;
import com.mascj.lup.event.bill.dto.LupAppSignDTO;
import com.mascj.lup.event.bill.entity.LupApp;
import com.mascj.lup.event.bill.entity.LupBillPending;

/**
 * <AUTHOR>
 * @date 2025/1/13 16:25
 * @describe
 */

public interface ILupAppService extends IService<LupApp> {
    LupApp saveAppInfo(LupAppDTO appDTO);

    LupApp detailApp(LupAppOperateDTO appOperateDTO);

    Result<LupApp> activeAppAuthCode(LupAppAuthCodeDTO authCodeDTO);

    Result validApp(LupAppSignDTO appSignDTO);

    String createSign(String appKey);
}
