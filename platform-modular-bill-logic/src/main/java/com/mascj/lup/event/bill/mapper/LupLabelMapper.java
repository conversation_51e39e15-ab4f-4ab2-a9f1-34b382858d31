package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mascj.lup.event.bill.dto.LabelListDTO;
import com.mascj.lup.event.bill.entity.LupLabel;
import com.mascj.lup.event.bill.vo.EventDataLabelVO;
import com.mascj.lup.event.bill.vo.LabelItemVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 事件标签 按名字去重 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface LupLabelMapper extends BaseMapper<LupLabel> {
    List<EventDataLabelVO> labelList();
    List<LabelItemVO> listLabel(@Param("billDTO") LabelListDTO labelDTO, @Param("projectId") Long projectId);

    LupLabel findLabelByDataId(@Param("dataId")Long dataId);
}
