package com.mascj.lup.event.bill.service.impl;

import cc.lyiot.framework.common.pojo.CommonResult;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.esri.core.geometry.OperatorExportToWkt;
import com.esri.core.geometry.Point;
import com.esri.core.geometry.WktExportFlags;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.exception.ServiceException;
import com.mascj.kernel.common.util.$;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.tools.SetFunction;
import com.mascj.kernel.tools.linq.Enumerable;
import com.mascj.kernel.tools.linq.Linqs;
import com.mascj.lup.cds.djicloud.feign.WaylineJobFeign;
import com.mascj.lup.cds.djicloud.wayline.dto.WaylineJobInfoDTO;
import com.mascj.lup.cds.djicloud.wayline.vo.WaylineJobInfoVO;
import com.mascj.lup.datacenter.dto.MarkDTO;
import com.mascj.lup.datacenter.feign.LupMarkCenterFeign;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.*;
import com.mascj.lup.event.bill.enums.BillReadStateEnum;
import com.mascj.lup.event.bill.enums.EventCompareType;
import com.mascj.lup.event.bill.enums.EventDataType;
import com.mascj.lup.event.bill.enums.EventSourceType;
import com.mascj.lup.event.bill.exceptions.OperationFailedException;
import com.mascj.lup.event.bill.geo.GeoTile;
import com.mascj.lup.event.bill.handler.MetadataHandler;
import com.mascj.lup.event.bill.mapper.LupBillReadMapper;
import com.mascj.lup.event.bill.mapper.LupDataMapper;
import com.mascj.lup.event.bill.mapper.LupGridUnitMapper;
import com.mascj.lup.event.bill.service.*;
import com.mascj.lup.event.bill.support.LupExecutionContext;
import com.mascj.lup.event.bill.util.ProjectUtil;
import com.mascj.lup.event.bill.vo.*;
import com.mascj.lup.event.feign.EventStreamFeign;
import com.mascj.lup.event.vo.AchievementPreviewVO;
import com.mascj.platform.system.entity.Dict;
import com.mascj.platform.system.feign.ISysDictProvider;
import com.mascj.platform.system.feign.ISysUserProvider;
import com.mascj.support.config.dto.XlmActivityState;
import com.mascj.support.config.feign.IAepConfigProvider;
import com.mascj.support.config.feign.IConfigValueProvider;
import com.mascj.support.config.vo.ConfigInVo;
import com.mascj.support.config.vo.meta.MetaTag;
import com.mascj.support.file.api.feign.IFileProvider;
import com.mascj.support.workflow.dto.XlmQueryBaseTaskDto;
import com.mascj.support.workflow.dto.XlmQueryHisTaskDto;
import com.mascj.support.workflow.entity.XlmTaskInfo;
import com.mascj.support.workflow.feign.IXlmQueryProvider;
import io.jsonwebtoken.lang.Assert;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.mascj.kernel.common.util.LocaleMessageUtil.getMessage;

/**
 * <p>
 * 工单数据表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
@AllArgsConstructor
public class LupBillReadServiceImpl extends ServiceImpl<LupBillReadMapper, LupBillRead> implements ILupBillReadService {

    private final IConfigValueProvider configValueProvider;

    @Override
    public BillReadStateCounterVO countBillUnReadCount(BillReadStateCounterDTO readStateCounterDTO) {
        ConfigInVo config = new ConfigInVo();
        config.setTenantId(Long.parseLong(LmContextHolder.getTenantId()));
        config.setIKey("eventBillFlowState");
        Result<String> result = configValueProvider.getConfigByKey(config);
        if(result.isSuccess()){
            readStateCounterDTO.setBillFlowState(Integer.parseInt(result.getData()));
        }
        return baseMapper.countBillUnReadCount(LmContextHolder.getUserId(),readStateCounterDTO);
    }

    @Override
    public Boolean saveBillReadState(BillReadStateDTO billReadStateDTO) {

        LupBillRead billRead = new LupBillRead();
        billRead.setFlowId(billReadStateDTO.getFlowId());
        billRead.setProcessTaskId(billReadStateDTO.getProcessTaskId());
        billRead.setUserId(LmContextHolder.getUserId());
        billRead.setReadState(BillReadStateEnum.ReadBillEnum.getCode());
        save(billRead);
        return true;
    }

    @Override
    public Integer countByUserId(BillReadStateDTO billReadStateDTO) {
        return baseMapper.countByUserId(LmContextHolder.getUserId(),billReadStateDTO);
    }
}
