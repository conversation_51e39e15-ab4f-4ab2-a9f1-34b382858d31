package com.mascj.lup.event.bill.service;

import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.util.DateUtil;
import com.mascj.kernel.common.util.StringUtil;
import com.mascj.lup.event.bill.vo.FlyEventRankTwoVO;
import com.mascj.lup.event.bill.vo.FlyEventRankVO;
import com.mascj.lup.event.bill.vo.FlyEventTjQueryVO;
import com.mascj.lup.event.bill.vo.FlyEventTjResVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public interface EventTjService {

    FlyEventRankTwoVO eventRank(FlyEventTjQueryVO flyEventTjQueryVO);


    default List<String> getYearMonth(String year){
        List<String> list = new ArrayList<>(12);
        for (int i = 1; i < 13; i++) {
            list.add(String.format("%02d",i));
        }
        return list;
    }


    /**
     * 计算两个数据的 月份的数据
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    default  List<String> getMonthsList(String startDateStr, String endDateStr) {
        List<String> monthsList = new ArrayList<>();
        if (StringUtil.isBlank(endDateStr)){
            endDateStr = DateUtil.format(new Date(), "yyyy-MM");

        }

        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        // 解析输入的日期字符串为 YearMonth
        YearMonth startMonth = YearMonth.parse(startDateStr, formatter);
        YearMonth endMonth = YearMonth.parse(endDateStr, formatter);

        // 循环遍历起始日期到结束日期之间的每个月份
        YearMonth currentMonth = startMonth;
        while (!currentMonth.isAfter(endMonth)) {
            monthsList.add(currentMonth.toString()); // 添加当前月份的字符串表示到列表中
            currentMonth = currentMonth.plusMonths(1); // 增加一个月
        }

        return monthsList;
    }


    default List<String> getAllDatesBetween(String startDateStr, String endDateStr) {
        if (StringUtil.isBlank(endDateStr)) {
            endDateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        List<String> datesList = new ArrayList<>();

        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 解析输入的日期字符串为 LocalDate
        LocalDate startDate = LocalDate.parse(startDateStr, formatter);
        LocalDate endDate = LocalDate.parse(endDateStr, formatter);

        // 循环遍历起始日期到结束日期之间的每一天
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            datesList.add(currentDate.format(formatter)); // 添加当前日期的字符串表示到列表中
            currentDate = currentDate.plusDays(1); // 增加一天
        }

        return datesList;
    }

    Result<FlyEventTjResVO> eventTj(FlyEventTjQueryVO flyEventTjQueryVO);
}
