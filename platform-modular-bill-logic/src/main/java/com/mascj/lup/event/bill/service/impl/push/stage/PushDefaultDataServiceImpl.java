package com.mascj.lup.event.bill.service.impl.push.stage;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.DataPicDTO;
import com.mascj.lup.event.bill.dto.KXPushDataDTO;
import com.mascj.lup.event.bill.dto.PushDataDefaultDTO;
import com.mascj.lup.event.bill.dto.PushKQDTO;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.entity.LupDataPushLog;
import com.mascj.lup.event.bill.entity.LupLabel;
import com.mascj.lup.event.bill.enums.DataPushStateEnum;
import com.mascj.lup.event.bill.service.*;
import com.mascj.lup.event.bill.util.GeoLocation;
import com.mascj.lup.event.bill.util.KXPlatformClient;
import com.mascj.lup.event.bill.util.ReadConfigUtil;
import com.mascj.lup.event.bill.vo.SendFourResutVo;
import com.mascj.lup.event.bill.vo.config.HandleModuleConfigVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/3 18:36
 * @describe
 */
@AllArgsConstructor
@Service("DefaultTranslateService")
public class PushDefaultDataServiceImpl implements IPushDataService {

    private final ReadConfigUtil readConfigUtil;

    private ILupDataService iLupDataService;

    private ILupBillService iLupBillService;

    private ILupLabelService labelService;

    private ILupDataPushLogService dataPushLogService;

    @Override
    public Result<SendFourResutVo> translateData(PushKQDTO pushKQDTO, LupData lupData, LupBill lupBill, String labelName) throws Exception {
        SendFourResutVo sendFourResutVo = new SendFourResutVo();
        LupData selectLupData = null;
        try {
            KXPlatformClient platformClient = new KXPlatformClient(pushKQDTO.getHost(),pushKQDTO.getAppKey(),pushKQDTO.getAppSecret());
            KXPushDataDTO data = new KXPushDataDTO();

            data.setWarningSourceType(2);
            data.setManufacturerId(5);
            data.setBusinessCode("基层治理");

            data.setLatitude(lupData.getLocationLat().toString());
            data.setLongitude(lupData.getLocationLng().toString());

            String res = GeoLocation.locationToAddress(pushKQDTO.getGeoLocationKey(),data.getLongitude().toString(),data.getLatitude().toString()).getBody();
            JSONObject resJsonObj = JSONUtil.parseObj(res);
            //{"result":{"formatted_address":"浙江省绍兴市柯桥区杨汛桥街道延寿寺","location":{"lon":120.28543,"lat":30.10462},"addressComponent":{"address":"延寿寺","city":"绍兴市","county_code":"156330603","nation":"中国","poi_position":"西北","county":"柯桥区","city_code":"156330600","address_position":"西北","poi":"延寿寺","province_code":"156330000","province":"浙江省","road":"杨渔线","road_distance":70,"poi_distance":40,"address_distance":40}},"msg":"ok","status":"0"}
            data.setLocationName(
                    resJsonObj.getJSONObject("result").getStr("formatted_address")
            );

            data.setDeviceCode("暂无");
            data.setDeviceType("暂无");
            data.setDeviceName("暂无");

            data.setGeoType("wgs84");

            data.setAlarmType(labelName);

            data.setUpTime(DateUtil.date().toString().replace(" ","T"));

            if(pushKQDTO.getEventContentTemplate() == null) {
                data.setEventContent(String.format("在%s可能发生了【%s】事件！", data.getLocationName(), labelName));
            }else {
                data.setEventContent(String.format(pushKQDTO.getEventContentTemplate(), data.getLocationName(), labelName));
            }

            DataPicDTO dataPicDTO = JSONUtil.toBean(lupData.getExtraData(),DataPicDTO.class);

            //多张图
            if(dataPicDTO.getEventPictureUrlList()==null){
                dataPicDTO.setEventPictureUrlList(new ArrayList<>());
            }
            if(!dataPicDTO.getEventPictureUrlList().contains(dataPicDTO.getEventPictureUrl())){
                dataPicDTO.getEventPictureUrlList().add(dataPicDTO.getEventPictureUrl());
            }
            data.setPicUrl(dataPicDTO.getEventPictureUrlList().stream().collect(Collectors.joining(",")));
            data.setThumbnailUrl(dataPicDTO.getEventPictureUrl());

            data.setAlarmDetailType(labelName);
            data.setHandleStatus(0);//0 未处理 1 已处理


            data.setProvinceCode(lupBill.getGridCode().substring(0,2)+"0000000000");
            data.setCityCode(lupBill.getGridCode().substring(0,4)+"00000000");
            data.setDistrictCode(lupBill.getGridCode().substring(0,6)+"000000");//330603110238
            data.setStreetCode(lupBill.getGridCode().substring(0,9)+"000");
            data.setCommunityCode(lupBill.getGridCode());

            data.setSourceId(lupBill.getBillNumber());
            data.setGridName(lupBill.getGridName());
            data.setAlarmFlag("1");

            String response = platformClient.gatewayRequest(pushKQDTO.getPushPath(),pushKQDTO.getPushPathCode(),data);
            //成功时 更新 推送状态；增加推送记录；
            JSONObject resJson = JSONUtil.parseObj(response);
            Integer status = resJson.getInt("status");

             selectLupData = iLupDataService.getById(lupData.getId());

            selectLupData.setUpdateTime(null);
            selectLupData.setUpdateBy(null);
            if (status == 200) {
                selectLupData.setPushState(1);
                sendFourResutVo.setSuccess(true);
            } else {
                sendFourResutVo.setSuccess(false);

                selectLupData.setPushState(-1);
                String message = resJson.getStr("message");
                sendFourResutVo.setFailMsg(message);
                selectLupData.setPushMsg(message);
            }
            iLupDataService.updateById(selectLupData);
        }catch (Exception e){
            e.printStackTrace();

            selectLupData = iLupDataService.getById(lupData.getId());
            sendFourResutVo.setSuccess(false);

            selectLupData.setPushState(-1);
            sendFourResutVo.setFailMsg(e.getMessage());
            selectLupData.setPushMsg(e.getMessage());
            iLupDataService.updateById(selectLupData);
        }

        return  Result.data(sendFourResutVo);
    }

    /**
     * 1、整理数据body；
     * 2、
     * @param dataId
     * @param pushLogId
     * @return
     */
    @Override
    public Result translateData(Long dataId,Long pushLogId) {

        LupDataPushLog dataPushLog = new LupDataPushLog();
        dataPushLog.setId(pushLogId);

        LupLabel label = labelService.findLabelByDataId(dataId);

        LupData lupData = iLupDataService.getById(dataId);


        LupData dataRes = new LupData();
        dataRes.setId(lupData.getId());
        try {

            HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();
            PushKQDTO pushKQDTO = handleModuleConfigVO.getPushKQDTO();


            PushDataDefaultDTO defaultDTO = new PushDataDefaultDTO();

            defaultDTO.setDataId(dataId);
            defaultDTO.setDeviceSn(lupData.getDeviceSn());
            defaultDTO.setDeviceName(lupData.getDeviceName());
            defaultDTO.setAppKey(pushKQDTO.getAppKey());
            defaultDTO.setEventLocation(lupData.getAddress());
            defaultDTO.setEventDesc(lupData.getEventDesc());
            defaultDTO.setLocationLat(lupData.getLocationLat());
            defaultDTO.setLocationLng(lupData.getLocationLng());
            defaultDTO.setEventTypeName(label.getName());

            defaultDTO.setFlyTaskNumber(lupData.getFlyTaskNumber());
            defaultDTO.setFlyTaskName(lupData.getFlyTaskName());
            defaultDTO.setAirLineName(lupData.getAirLineName());


            DataPicDTO dataPicDTO = JSONUtil.toBean(lupData.getExtraData(), DataPicDTO.class);

            //多张图
            if (dataPicDTO.getEventPictureUrlList() == null) {
                dataPicDTO.setEventPictureUrlList(new ArrayList<>());
            }
            if (!dataPicDTO.getEventPictureUrlList().contains(dataPicDTO.getEventPictureUrl())) {
                dataPicDTO.getEventPictureUrlList().add(dataPicDTO.getEventPictureUrl());
            }
            defaultDTO.setPicList(dataPicDTO.getEventPictureUrlList());

            List<LupBill> lupBillList = iLupBillService.list(Wrappers.<LupBill>lambdaQuery().eq(LupBill::getDeleted, 0).eq(LupBill::getDataId, dataId).orderByDesc(LupBill::getId));

            if (ObjectUtil.isNotEmpty(lupBillList)) {
                LupBill lupBill = lupBillList.get(0);

                defaultDTO.setBillNumber(lupBill.getBillNumber());
                defaultDTO.setGridCode(lupBill.getGridCode());
                defaultDTO.setGridName(lupBill.getGridName());

            }

            //appkey + appsecret + 时间戳
            defaultDTO.setTimestamp(DateUtil.date().toCalendar().getTimeInMillis());


            KXPlatformClient platformClient = new KXPlatformClient(pushKQDTO.getHost(), pushKQDTO.getAppKey(), pushKQDTO.getAppSecret());

            System.out.println("【Default】Request:" + JSONUtil.toJsonStr(defaultDTO));
            //推送数据到第三方
            String response = platformClient.gatewayRequestDefault(pushKQDTO.getPushPath(), pushKQDTO.getPushPathCode(), defaultDTO);
            //成功时 更新 推送状态；增加推送记录；
            JSONObject resJson = JSONUtil.parseObj(response);

            dataRes.setPushMsg(resJson.getStr("msg"));

            if (resJson.getInt("code") == 0 || resJson.getInt("code") == 200) {

                List<LupDataPushLog> dataPushLogList = dataPushLogService.list(Wrappers.<LupDataPushLog>lambdaQuery().eq(LupDataPushLog::getDataId, dataId).ne(LupDataPushLog::getId, pushLogId));

                List<LupDataPushLog> tmpDataPushLogList = new ArrayList<>();
                dataPushLogList.forEach(e -> {
                    LupDataPushLog log = new LupDataPushLog();
                    log.setId(e.getId());
                    log.setPushable(0);//是否可以重新推送 0否 1是
                    tmpDataPushLogList.add(log);

                });
                if (tmpDataPushLogList.size() > 0)
                    dataPushLogService.updateBatchById(tmpDataPushLogList);

                dataPushLog.setPushState(DataPushStateEnum.PushSuccess.getCode());
                dataPushLog.setPushTime(DateUtil.now());
                dataPushLog.setPushable(0);
                dataRes.setPushState(DataPushStateEnum.PushSuccess.getCode());
            } else {
                dataPushLog.setPushMsg(resJson.getStr("msg"));
                dataPushLog.setPushState(DataPushStateEnum.PushFail.getCode());
                dataRes.setPushState(DataPushStateEnum.PushFail.getCode());
            }

            System.out.println("[Default]response:" + response);

        } catch (Exception exp) {

            dataPushLog.setPushState(DataPushStateEnum.PushFail.getCode());
            dataRes.setPushState(DataPushStateEnum.PushFail.getCode());

            exp.printStackTrace();
            if (exp.getMessage().contains("code")
                    && exp.getMessage().contains("msg")
                    && exp.getMessage().contains("[{")
                    && exp.getMessage().contains("}]")) {

                //500 Internal Server Error: [{"code":7,"msg":"系统发生异常，请联系管理员"}]

                String res = exp.getMessage().substring(exp.getMessage().lastIndexOf("[{") + 1, exp.getMessage().lastIndexOf("}]") + 1);

                JSONObject resJson = JSONUtil.parseObj(res);

                dataPushLog.setPushMsg(resJson.getStr("msg"));
                dataRes.setPushMsg(resJson.getStr("msg"));

            } else {

                dataPushLog.setPushMsg(exp.getMessage());
                dataRes.setPushMsg(exp.getMessage());
            }
        }

        iLupDataService.updateById(dataRes);
        dataPushLogService.updateById(dataPushLog);

        return Result.data(dataPushLog);
    }

}
