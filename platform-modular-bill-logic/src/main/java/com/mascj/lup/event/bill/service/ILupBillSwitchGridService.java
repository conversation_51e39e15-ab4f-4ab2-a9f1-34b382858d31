package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.dto.BillSearchDTO;
import com.mascj.lup.event.bill.dto.BillSwitchGridDTO;
import com.mascj.lup.event.bill.dto.ListUnitDTO;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupBillSwitchGrid;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.vo.*;

import java.util.List;

/**
 * <p>
 * 工单数据表 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface ILupBillSwitchGridService extends IService<LupBillSwitchGrid> {

}
