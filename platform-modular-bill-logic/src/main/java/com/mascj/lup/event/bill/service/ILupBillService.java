package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.tools.linq.Enumerable;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupBillSwitchGrid;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.entity.LupFlowEntity;
import com.mascj.lup.event.bill.vo.*;
import com.mascj.support.workflow.entity.XlmTaskInfo;

import java.util.List;

/**
 * <p>
 * 工单数据表 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface ILupBillService extends IService<LupBill> {

    Result<List<BillCountVO>> countBill(ListUnitDTO billDTO);

    List<LupBillCountVo> countBillByGrid(BillDTO billDTO);

    List<LupBillCountByFlowStateVo> countBillByFlowState(Long userId,List<Long> gridIdList,List<Integer> flowStateList);

    IPage<BillSearchVO> pagedBill(IPage page,BillSearchDTO taskSearchDTO);

    List<BillDataVO> listBill(ListUnitDTO unitDTO);
    /**
     * 根据数据插入工单
     * @param data
     * @return
     */
    Result<LupBillVO> addBill(LupData data);

    Result<BillDetailVO> detailByDataId(Integer originType,Long billId);

    List<BillDetailVO> listBillDetailVOByQueryBillPictureInfo(IPage page,QueryBillPictureInfoDTO queryBillPictureInfoDTO);
    Result<BillDetailVO> detailByBillId(Long billId);

    /**
     * 重新构建流程集合
     * @param taskVoList 最终结果
     * @param select 原始流程节点
     * @param billId 工单ID
     */
    void rebuildTaskVoCollection(List<TaskVo> taskVoList, Enumerable<TaskVo> select,Long billId);

    TaskVo convertTask(XlmTaskInfo info, String bizCode);

    List<TagStateCountVo> tagMetadata(BillSearchDTO billSearchDTO);

    List<BillStateCountVo> billStateCount(BillSearchDTO billSearchDTO);

    Boolean switchGridUnit(BillSwitchGridDTO switchGridDTO);

    List<BillSearchExcelVO> exportDataToExcel(ExportFileDTO billSearchDTO);
    List<BillSearchExcelYXQVO> exportYXQDataToExcel(ExportFileDTO billSearchDTO);


    boolean dimensionEvent(Long billId);

    boolean cancelDimensionEvent(Long billId);
    Result initUnLocateGridUnitBill();

    Boolean switchBillEnable(SwitchBillEnableDTO switchBillEnableDTO);

    List<BillUserLabelVO> listBillUserLabelVO(Long flowId);

    BillUserLabelVO listBillUserLabelOneVO(Long flowId);

    Boolean changeBillEventType(BillEventTypeChangeDTO billEventTypeChangeDTO);
}
