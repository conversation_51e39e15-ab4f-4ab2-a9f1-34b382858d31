package com.mascj.lup.event.bill.service.impl;

import com.mascj.lup.event.bill.dto.FlyTaskReportDTO;
import com.mascj.lup.event.bill.service.IEventDataQueryService;
import com.mascj.lup.event.bill.service.ILupBillReportService;
import com.mascj.lup.event.bill.vo.FlyTaskReportVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/11/25 15:30
 * @describe
 */
@Service
@AllArgsConstructor
public class LupBillReportServiceImpl implements ILupBillReportService {

    private final IEventDataQueryService eventDataQueryService;

    @Override
    public FlyTaskReportVO queryFlyTaskReport(FlyTaskReportDTO flyTaskReportDTO) {
        return eventDataQueryService.queryFlyTaskReportVO(flyTaskReportDTO);
    }
}
