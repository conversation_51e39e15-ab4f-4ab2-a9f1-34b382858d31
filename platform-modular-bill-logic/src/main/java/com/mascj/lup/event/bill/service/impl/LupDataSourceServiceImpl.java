package com.mascj.lup.event.bill.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.lup.event.bill.entity.LupDataSource;
import com.mascj.lup.event.bill.mapper.LupDataSourceMapper;
import com.mascj.lup.event.bill.service.ILupDataSourceService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 事件数据资源关联记录表 事件和瓦片图片的关联记录 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
public class LupDataSourceServiceImpl extends ServiceImpl<LupDataSourceMapper, LupDataSource> implements ILupDataSourceService {

}
