package com.mascj.lup.event.bill.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.exception.ServiceException;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.database.entity.Search;
import com.mascj.kernel.database.util.PageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.mapper.LupEventLabelMapper;
import com.mascj.lup.event.bill.entity.LupEventLabel;
import com.mascj.lup.event.bill.service.LupEventLabelService;
import com.mascj.lup.event.bill.vo.LupCommonTreeVO;
import com.mascj.lup.event.bill.vo.LupEventLabelVo;
import com.mascj.platform.system.dto.LabelTenantDto;
import com.mascj.platform.system.entity.Dict;
import com.mascj.platform.system.entity.Org;
import com.mascj.platform.system.entity.Role;
import com.mascj.platform.system.entity.User;
import com.mascj.platform.system.feign.ISysDictProvider;
import com.mascj.platform.system.feign.ISysOrgProvider;
import com.mascj.platform.system.feign.ISysRoleProvider;
import com.mascj.platform.system.feign.ISysUserProvider;
import com.mascj.platform.system.vo.LabelTenantVo;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @since 2024-01-05 14:36:06
 */
@Service
@AllArgsConstructor
@Slf4j
public class LupEventLabelServiceImpl extends ServiceImpl<LupEventLabelMapper, LupEventLabel> implements LupEventLabelService {

    @Autowired
    private LupEventLabelMapper lupEventLabelMapper;

    @Autowired
    private ISysUserProvider iSysUserProvider;

    @Autowired
    private ISysOrgProvider iSysOrgProvider;

    private final ISysDictProvider dictProvider;

    @Override
    public Result<IPage<LupEventLabelVo>> listPage(Integer codeValue, Search search) {
        // 查询标签
        IPage<LupEventLabelVo> page = lupEventLabelMapper.listPage(codeValue, PageUtil.getPage(search));
        List<LupEventLabelVo> records = page.getRecords();

        if (CollectionUtil.isNotEmpty(records)) {
            List<Long> labelIds = records.stream().map(LupEventLabelVo::getLabelId).collect(Collectors.toList());
            List<LupEventLabel> lupEventLabels = lupEventLabelMapper.selectList(new LambdaQueryWrapper<LupEventLabel>()
                    .eq(LupEventLabel::getDeleted, 0).in(LupEventLabel::getLabelId, labelIds));
            if (CollectionUtil.isNotEmpty(lupEventLabels)) {
                // 获取所有组织id
                List<LupEventLabel> orgTList = lupEventLabels.stream().filter(li -> li.getType().equals(1) && Objects.nonNull(li.getOrgId())).collect(Collectors.toList());
                List<LupEventLabel> roleTList = lupEventLabels.stream().filter(li -> li.getType().equals(2)&& Objects.nonNull(li.getRoleId())).collect(Collectors.toList());
                List<LupEventLabel> userTList = lupEventLabels.stream().filter(li -> li.getType().equals(3)&& Objects.nonNull(li.getUserId())).collect(Collectors.toList());
                LabelTenantVo labelTenantVo = new LabelTenantVo();
                labelTenantVo.setOrgIds(orgTList.stream().map(LupEventLabel :: getOrgId).collect(Collectors.toList()));
                labelTenantVo.setRoleIds(roleTList.stream().map(LupEventLabel :: getRoleId).collect(Collectors.toList()));
                labelTenantVo.setUserIds(userTList.stream().map(LupEventLabel :: getUserId).collect(Collectors.toList()));
                LabelTenantDto labelTenantDto = iSysUserProvider.listLabelByIds(labelTenantVo);
                records.forEach(it -> {
                    List<LupEventLabel> orgList = orgTList.stream().filter(li ->li.getLabelId().equals(it.getLabelId())).collect(Collectors.toList());
                    List<LupEventLabel> roleList = roleTList.stream().filter(li -> li.getLabelId().equals(it.getLabelId())).collect(Collectors.toList());
                    List<LupEventLabel> userList = userTList.stream().filter(li ->  li.getLabelId().equals(it.getLabelId())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(orgList)) {
                        it.setOrgIds(orgList.stream().map(LupEventLabel :: getOrgId).collect(Collectors.toList()));
                        if (Objects.nonNull(labelTenantDto) && CollectionUtil.isNotEmpty(labelTenantDto.getOrgList())) {
                            orgList.forEach(its -> {
                                List<String> collect = labelTenantDto
                                        .getOrgList()
                                        .stream()
                                        .filter(li -> li.getId().equals(its.getOrgId()))
                                        .map(Org::getName)
                                        .collect(Collectors.toList());
                                it.getOrgNames().addAll(collect);
                            });
                        }
                    }
                    if (CollectionUtil.isNotEmpty(roleList)) {
                        it.setRoleIds(roleList.stream().map(LupEventLabel :: getRoleId).collect(Collectors.toList()));
                        if (Objects.nonNull(labelTenantDto) && CollectionUtil.isNotEmpty(labelTenantDto.getRoleList())) {
                            roleList.forEach(its -> {
                                List<String> collect = labelTenantDto
                                        .getRoleList()
                                        .stream()
                                        .filter(li -> li.getId().equals(its.getRoleId()))
                                        .map(Role::getName)
                                        .collect(Collectors.toList());
                                it.getRoleNames().addAll(collect);
                            });
                        }
                    }
                    if (CollectionUtil.isNotEmpty(userList)) {
                        it.setUserIds(userList.stream().map(LupEventLabel :: getUserId).collect(Collectors.toList()));
                        if (Objects.nonNull(labelTenantDto) && CollectionUtil.isNotEmpty(labelTenantDto.getUserList())) {
                            userList.forEach(its -> {
                                List<String> collect = labelTenantDto
                                        .getUserList()
                                        .stream()
                                        .filter(li -> li.getId().equals(its.getUserId()))
                                        .map(User::getName)
                                        .collect(Collectors.toList());
                                it.getUserNames().addAll(collect);
                            });
                        }
                    }
                });
            }
        }
        return Result.data(page);
    }

    @Override
    public Result saveConfig(LupEventLabelVo lupEventLabelVo) {
        if (Objects.isNull(lupEventLabelVo.getLabelId())) {
            throw new ServiceException(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneSyncId,Arrays.asList(EventTipKey.NoneTip)));
        }

        if (Objects.isNull(lupEventLabelVo.getType())) {
            throw new ServiceException(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneLabelType, Arrays.asList(EventTipKey.NoneTip)));
        }

        // 删除之前的数据
        LambdaQueryWrapper<LupEventLabel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LupEventLabel :: getLabelId , lupEventLabelVo.getLabelId());
        queryWrapper.eq(LupEventLabel :: getType , lupEventLabelVo.getType());
        baseMapper.delete(queryWrapper);

        ArrayList<LupEventLabel> lupEventLabels = new ArrayList<>();
        if (lupEventLabelVo.getType().equals(1)) {
            List<Long> orgIds = lupEventLabelVo.getOrgIds();
            orgIds.forEach(it -> {
                LupEventLabel lupEventLabel = new LupEventLabel();
                lupEventLabel.setOrgId(it);
                lupEventLabel.setLabelId(lupEventLabelVo.getLabelId());
                lupEventLabel.setType(1);
                lupEventLabels.add(lupEventLabel);
            });
        }
        if (lupEventLabelVo.getType().equals(2)) {
            List<Long> roleIds = lupEventLabelVo.getRoleIds();
            roleIds.forEach(it -> {
                LupEventLabel lupEventLabel = new LupEventLabel();
                lupEventLabel.setRoleId(it);
                lupEventLabel.setLabelId(lupEventLabelVo.getLabelId());
                lupEventLabel.setType(2);
                lupEventLabels.add(lupEventLabel);
            });
        }

        if (lupEventLabelVo.getType().equals(3)) {
            List<Long> userIds = lupEventLabelVo.getUserIds();
            userIds.forEach(it -> {
                LupEventLabel lupEventLabel = new LupEventLabel();
                lupEventLabel.setLabelId(lupEventLabelVo.getLabelId());
                lupEventLabel.setUserId(it);
                lupEventLabel.setType(3);
                lupEventLabels.add(lupEventLabel);
            });
        }

        this.saveBatch(lupEventLabels);
        return Result.condition(true);
    }

    @Override
    public List<Long> premissionList(Long labelId) {
        List<Long> userIds = new ArrayList<>();

        // 获取指定标签下所有用户id
        List<LupEventLabel> lupEventLabels = baseMapper.selectList(new LambdaQueryWrapper<LupEventLabel>()
                .eq(LupEventLabel::getDeleted, 0)
                .eq(Objects.nonNull(labelId), LupEventLabel::getLabelId, labelId));
        if (CollectionUtil.isEmpty(lupEventLabels)) {
            return userIds;
        }

        List<LupEventLabel> orgList  =  lupEventLabels.stream().filter(li -> li.getType().equals(1) ).collect(Collectors.toList());
        List<LupEventLabel> roleList =  lupEventLabels.stream().filter(li -> li.getType().equals(2)).collect(Collectors.toList());
        List<LupEventLabel> userList =  lupEventLabels.stream().filter(li -> li.getType().equals(3)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(orgList)) {
            List<Long> collect = orgList.stream().map(LupEventLabel::getOrgId).filter(Objects::nonNull).collect(Collectors.toList());
            List<Long> longs = iSysUserProvider.listUserByOrgIds(collect);
            userIds.addAll(longs);
        }
        if (CollectionUtil.isNotEmpty(roleList)) {
            List<Long> collect = orgList.stream().map(LupEventLabel::getRoleId).filter(Objects::nonNull).collect(Collectors.toList());
            List<Long> longs = iSysUserProvider.listUserByRoleIds(collect);
            userIds.addAll(longs);
        }
        if (CollectionUtil.isNotEmpty(userList)) {
            List<Long> collect = orgList.stream().map(LupEventLabel::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
            userIds.addAll(collect);
        }

        return userIds.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<Long> labelIdsByUserIds(List<Long> userIds) {
        // 获取角色orgIds
        List<Long> roleIds = iSysUserProvider.listRoleByUserIds(userIds);
        List<Long> orgIds = iSysUserProvider.listOrgByUserIds(userIds);
        LambdaQueryWrapper<LupEventLabel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LupEventLabel :: getDeleted , 0);
        queryWrapper.and(it -> {
            it.in(CollectionUtil.isNotEmpty(roleIds) , LupEventLabel :: getRoleId , roleIds)
                    .or().in(CollectionUtil.isNotEmpty(orgIds), LupEventLabel :: getOrgId , orgIds)
                    .or().in(CollectionUtil.isNotEmpty(userIds), LupEventLabel :: getUserId , userIds);
        });
        return baseMapper.selectList(queryWrapper).stream().map(LupEventLabel :: getLabelId).distinct().collect(Collectors.toList());
    }


    @Override
    public List<LupCommonTreeVO> listLabelTree() {

        Map<String,String> eventOriginalMap = listOrigin();

        List<LupCommonTreeVO> commonTreeVOList = baseMapper.listLabelTree();

        Map<Long,List<LupCommonTreeVO>> commonTreeVOMap = commonTreeVOList.stream().collect(Collectors.groupingBy(LupCommonTreeVO::getParentId));


        List<LupCommonTreeVO> list = new ArrayList<>();

        commonTreeVOMap.keySet().forEach(key->{


            LupCommonTreeVO commonTreeVO = new LupCommonTreeVO();

            commonTreeVO.setValue(eventOriginalMap.get(key.toString()));
            commonTreeVO.setId(key);
            commonTreeVO.setChildrenList(commonTreeVOMap.get(key));

            list.add(commonTreeVO);

        });


        return list;
    }

    public Map<String,String> listOrigin(){

        Map<String,String> eventOriginalMap = new HashMap<>();
        //查询字典
        Result<List<Dict>> result = dictProvider.getList(VariableConstants.EventOriginCode);
        if(result.isSuccess()) {
            Map<String, Dict>  dictMap= result.getData().stream()
                    .filter(s -> s.getDictKey() != null && !"".equals(s.getDictKey()))
                    .collect(Collectors.toMap(Dict::getDictKey, each -> each, (value1, value2) -> value1));
            dictMap.keySet().forEach(key->{
                eventOriginalMap.put(key,dictMap.get(key).getDictValue());
            });

        }

        return eventOriginalMap;
    }
}