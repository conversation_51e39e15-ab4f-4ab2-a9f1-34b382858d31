package com.mascj.lup.event.bill.util;


import com.mascj.lup.event.bill.exceptions.OperationFailedException;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class FileUtils {
    /**
     * @param path 资源目录
     * @return
     * @throws URISyntaxException
     * @throws FileNotFoundException
     */
    public static String readResourceFile(String path) {

        try {
            InputStream inStream = getResourceStream(path);

            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(inStream, StandardCharsets.UTF_8)
            );

            StringBuilder stringBuilder = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }

            return stringBuilder.toString();

        } catch (IOException e) {
            throw OperationFailedException.format("读取文件失败，message=" + e.getMessage());
        }

    }

    /**
     *
     * @param path
     * @return
     */
    public static InputStream getResourceStream(String path) {
        Assert.hasText(path, "path必填项");
        return FileUtils.class.getClassLoader().getResourceAsStream(path);
    }


    /**
     * @param resourcePath
     * @return
     * @throws URISyntaxException
     */
    public static String getLocalPath(String resourcePath) {
        try {
            ClassLoader loader = FileUtils.class.getClassLoader();

            return Objects.requireNonNull(loader.getResource(resourcePath)).toURI().getPath();
        } catch (URISyntaxException e) {
            throw OperationFailedException.format("getLocalPath失败，message=" + e.getMessage());
        }
    }

    /**
     * @param path 本地路径
     * @return
     * @throws FileNotFoundException
     */
    public static String[] readAllLines(String path) {
        Assert.hasText(path, "path必填项");

        File file = new File(path);

        if (!file.exists()) {
            throw OperationFailedException.format("文件不存在, path=" + path);
        }

        List<String> list = new ArrayList<>();

        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(file));

            String tempString = null;

            while ((tempString = reader.readLine()) != null) {
                list.add(tempString);
            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                }
            }
        }

        return list.toArray(new String[0]);
    }

    /**
     * 读取本地文件
     *
     * @param path 文件系统内的文件
     * @return
     * @throws FileNotFoundException
     */
    public static String readAllText(String path) {

        StringBuilder stringBuilder = new StringBuilder();

        String[] lines = readAllLines(path);

        for (String line : lines) {
            stringBuilder.append(line);
        }

        return stringBuilder.toString();
    }


    /**
     * 获取封装得MultipartFile
     *
     * @param inputStream inputStream
     * @param fileName    fileName
     * @return MultipartFile
     */
    public static MultipartFile getMultipartFile(InputStream inputStream, String fileName) {
        FileItem fileItem = createFileItem(inputStream, fileName);
        //CommonsMultipartFile是feign对multipartFile的封装，但是要FileItem类对象
        return new CommonsMultipartFile(fileItem);
    }


    /**
     * FileItem类对象创建
     *
     * @param inputStream inputStream
     * @param fileName    fileName
     * @return FileItem
     */
    public static FileItem createFileItem(InputStream inputStream, String fileName) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "file";
        FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
        int bytesRead = 0;
        byte[] buffer = new byte[10 * 1024 * 1024];
        OutputStream os = null;
        //使用输出流输出输入流的字节
        try {
            os = item.getOutputStream();
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
//            log.error("Stream copy exception", e);
            throw new IllegalArgumentException("文件上传失败");
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
//                    log.error("Stream close exception", e);

                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
//                    log.error("Stream close exception", e);
                }
            }
        }

        return item;
    }

    /**
     * 清理 指定天数之前的 以tmp结尾的文件
     * @param dirPath 目标目录
     * @param days 天数
     */
    public static void clearEndsWithTmpFile(String dirPath,Integer days){
        File directory = new File(dirPath);

        // 获取当前时间减去days天的日期
        LocalDate tenDaysAgo = LocalDate.now().minusDays(days);
        Instant cutoffInstant = tenDaysAgo.atStartOfDay(ZoneId.systemDefault()).toInstant();

        // 遍历目录下的所有文件
        File[] files = directory.listFiles((dir, name) -> name.endsWith(".tmp"));

        if (files != null) {
            for (File file : files) {
                // 获取文件最后修改时间
                Instant lastModifiedInstant = Instant.ofEpochMilli(file.lastModified());

                // 检查文件是否超过10天未修改
                if (Duration.between(cutoffInstant,lastModifiedInstant ).isNegative()) {
                    // 文件超过10天，尝试删除
                    try {
                        Files.delete(file.toPath());
                        System.out.println("Deleted: " + file.getAbsolutePath());
                    } catch (IOException e) {
                        System.err.println("Failed to delete " + file.getAbsolutePath() + ": " + e.getMessage());
                    }
                }
            }
        } else {
            System.out.println("No .tmp files found in " + dirPath);
        }
    }

    /**
     * 清理 指定天数之前的 以tmp结尾的文件
     * @param dirPath 目标目录
     *
     */
    public static void clearEndsWithTmpFile(String dirPath){
        File directory = new File(dirPath);

        // 获取当前时间减去10天的日期
        LocalDate tenDaysAgo = LocalDate.now().minusDays(10);
        Instant cutoffInstant = tenDaysAgo.atStartOfDay(ZoneId.systemDefault()).toInstant();

        // 遍历目录下的所有文件
        File[] files = directory.listFiles((dir, name) -> name.endsWith(".tmp"));

        if (files != null) {
            for (File file : files) {
                // 获取文件最后修改时间
                Instant lastModifiedInstant = Instant.ofEpochMilli(file.lastModified());

                // 检查文件是否超过10天未修改
                if (Duration.between(cutoffInstant,lastModifiedInstant ).isNegative()) {
                    // 文件超过10天，尝试删除
                    try {
                        Files.delete(file.toPath());
                        System.out.println("Deleted: " + file.getAbsolutePath());
                    } catch (IOException e) {
                        System.err.println("Failed to delete " + file.getAbsolutePath() + ": " + e.getMessage());
                    }
                }
            }
        } else {
            System.out.println("No .tmp files found in " + dirPath);
        }
    }
    public static void main(String[] args) {
        // 指定要清理的目录路径
        String directoryPath = "/Users/<USER>/Downloads/分析报告2024-06-24";
        clearEndsWithTmpFile(directoryPath,10);
    }
}
