package com.mascj.lup.event.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lup.event.bill.mapper.LupEventMarkMapper;
import com.mascj.lup.event.bill.entity.LupEventMark;
import com.mascj.lup.event.bill.service.LupEventMarkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * <AUTHOR> @since 2024-01-05 10:56:46
 */
@Service
@Slf4j
public class LupEventMarkServiceImpl extends ServiceImpl<LupEventMarkMapper, LupEventMark> implements LupEventMarkService {

    @Autowired
    private LupEventMarkMapper lupEventMarkMapper;

}