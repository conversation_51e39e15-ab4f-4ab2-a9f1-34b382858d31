package com.mascj.lup.event.bill.service.impl.push.stage;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.DataPicDTO;
import com.mascj.lup.event.bill.dto.KXPushDataDTO;
import com.mascj.lup.event.bill.dto.PushKQDTO;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.entity.LupDataPushLog;
import com.mascj.lup.event.bill.enums.DataPushStateEnum;
import com.mascj.lup.event.bill.service.ILupDataPushLogService;
import com.mascj.lup.event.bill.service.ILupDataService;
import com.mascj.lup.event.bill.service.IPushDataService;
import com.mascj.lup.event.bill.util.GeoLocation;
import com.mascj.lup.event.bill.util.KXPlatformClient;
import com.mascj.lup.event.bill.vo.SendFourResutVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/3 18:36
 * @describe
 */
@AllArgsConstructor
@Service("YXQTranslateService")
public class PushYXQDataServiceImpl  implements IPushDataService {

    private ILupDataService iLupDataService;
    private final ILupDataPushLogService dataPushLogService;

    @Override
    public Result<SendFourResutVo> translateData(PushKQDTO pushKQDTO, LupData lupData, LupBill lupBill, String labelName) throws Exception {
        SendFourResutVo sendFourResutVo = new SendFourResutVo();
        LupData selectLupData = null;
        LupDataPushLog dataPushLog = new LupDataPushLog();
        try {

            dataPushLog.setDataOrigin(lupData.getDataOrigin());
            dataPushLog.setPushStage(pushKQDTO.getStageName());
            dataPushLog.setPushAuto(pushKQDTO.getAutoPush() ? 1 : 0);
            dataPushLog.setPushState(DataPushStateEnum.NonePush.getCode());
            dataPushLog.setDataId(lupData.getId());
            dataPushLog.setPushMode(pushKQDTO.getGenerateBillFlag());
            if (lupBill != null) {
                dataPushLog.setBillId(lupBill.getId());
                dataPushLog.setBillNumber(lupBill.getBillNumber());
            }
            dataPushLog.setGenerateBill(lupData.getGenerateBill());
            dataPushLog.setPushEventFlow(pushKQDTO.getPushEventFlow());
            dataPushLogService.save(dataPushLog);


            KXPlatformClient platformClient = new KXPlatformClient(pushKQDTO.getHost(),pushKQDTO.getAppKey(),pushKQDTO.getAppSecret());
            KXPushDataDTO data = new KXPushDataDTO();

            data.setWarningSourceType(2);
            data.setManufacturerId(5);
            data.setBusinessCode("基层治理");

            data.setLatitude(lupData.getLocationLat().toString());
            data.setLongitude(lupData.getLocationLng().toString());

            String res = GeoLocation.locationToAddress(pushKQDTO.getGeoLocationKey(),data.getLongitude().toString(),data.getLatitude().toString()).getBody();
            JSONObject resJsonObj = JSONUtil.parseObj(res);
            //{"result":{"formatted_address":"浙江省绍兴市柯桥区杨汛桥街道延寿寺","location":{"lon":120.28543,"lat":30.10462},"addressComponent":{"address":"延寿寺","city":"绍兴市","county_code":"156330603","nation":"中国","poi_position":"西北","county":"柯桥区","city_code":"156330600","address_position":"西北","poi":"延寿寺","province_code":"156330000","province":"浙江省","road":"杨渔线","road_distance":70,"poi_distance":40,"address_distance":40}},"msg":"ok","status":"0"}
            data.setLocationName(
                    resJsonObj.getJSONObject("result").getStr("formatted_address")
            );

            data.setDeviceCode("暂无");
            data.setDeviceType("暂无");
            data.setDeviceName("暂无");

            data.setGeoType("wgs84");

            data.setAlarmType(labelName);

            data.setUpTime(DateUtil.date().toString().replace(" ","T"));

            if(pushKQDTO.getEventContentTemplate() == null) {
                data.setEventContent(String.format("在%s可能发生了【%s】事件！", data.getLocationName(), labelName));
            }else {
                data.setEventContent(String.format(pushKQDTO.getEventContentTemplate(), data.getLocationName(), labelName));
            }

            DataPicDTO dataPicDTO = JSONUtil.toBean(lupData.getExtraData(),DataPicDTO.class);

            //多张图
            if(dataPicDTO.getEventPictureUrlList()==null){
                dataPicDTO.setEventPictureUrlList(new ArrayList<>());
            }
            if(!dataPicDTO.getEventPictureUrlList().contains(dataPicDTO.getEventPictureUrl())){
                dataPicDTO.getEventPictureUrlList().add(dataPicDTO.getEventPictureUrl());
            }
            data.setPicUrl(dataPicDTO.getEventPictureUrlList().stream().collect(Collectors.joining(",")));
            data.setThumbnailUrl(dataPicDTO.getEventPictureUrl());

            data.setAlarmDetailType(labelName);
            data.setHandleStatus(0);//0 未处理 1 已处理


            data.setProvinceCode(lupBill.getGridCode().substring(0,2)+"0000000000");
            data.setCityCode(lupBill.getGridCode().substring(0,4)+"00000000");
            data.setDistrictCode(lupBill.getGridCode().substring(0,6)+"000000");//330603110238
            data.setStreetCode(lupBill.getGridCode().substring(0,9)+"000");
            data.setCommunityCode(lupBill.getGridCode());

            data.setSourceId(lupBill.getBillNumber());
            data.setGridName(lupBill.getGridName());
            data.setAlarmFlag("1");

            String response = platformClient.gatewayRequest(pushKQDTO.getPushPath(),pushKQDTO.getPushPathCode(),data);
            //成功时 更新 推送状态；增加推送记录；
            JSONObject resJson = JSONUtil.parseObj(response);
            Integer status = resJson.getInt("status");

             selectLupData = iLupDataService.getById(lupData.getId());

            selectLupData.setUpdateTime(null);
            selectLupData.setUpdateBy(null);
            if (status == 200) {
                selectLupData.setPushState(1);
                sendFourResutVo.setSuccess(true);


                dataPushLog.setPushState(DataPushStateEnum.PushSuccess.getCode());
                dataPushLogService.updateById(dataPushLog);

            } else {
                sendFourResutVo.setSuccess(false);

                selectLupData.setPushState(-1);
                String message = resJson.getStr("message");
                sendFourResutVo.setFailMsg(message);
                selectLupData.setPushMsg(message);

                dataPushLog.setPushMsg(message);
                dataPushLog.setPushState(DataPushStateEnum.PushFail.getCode());
                dataPushLogService.updateById(dataPushLog);
            }
            iLupDataService.updateById(selectLupData);
        }catch (Exception e){
            e.printStackTrace();

            selectLupData = iLupDataService.getById(lupData.getId());
            sendFourResutVo.setSuccess(false);

            selectLupData.setPushState(-1);
            sendFourResutVo.setFailMsg(e.getMessage());
            selectLupData.setPushMsg(e.getMessage());
            iLupDataService.updateById(selectLupData);

            dataPushLog.setPushMsg(e.getMessage());
            dataPushLog.setPushState(DataPushStateEnum.PushFail.getCode());
            dataPushLogService.updateById(dataPushLog);

        }

        return  Result.data(sendFourResutVo);
    }

    @Override
    public Result translateData(Long dataId,Long pushLogId) {
        return null;
    }
}
