package com.mascj.lup.event.bill.service;


import com.mascj.lup.event.bill.dto.ExportFileDTO;
import com.mascj.lup.event.bill.service.sync.ExportFileSyncRunner;

public interface IExportFileStrategyService {

    int initDataTotal(ExportFileDTO exportFileDto);

    void initReportInfo(ExportFileDTO exportFileDto);
    /**
     * 开始导出文件
     * 按照类型执行指定的策略 在各自策略内部完成数据搜索和任务执行
     * @param exportFileDto 查询数据的参数
     */
    void startExportFile(ExportFileDTO exportFileDto);

    /**
     * 开始导出文件
     * 按照类型执行指定的策略 在各自策略内部完成数据搜索和任务执行
     * @param exportFileDto 查询数据的参数
     */
    void startExportFile(ExportFileDTO exportFileDto, ExportFileSyncRunner exportFileSyncRunner) throws  Exception;
}
