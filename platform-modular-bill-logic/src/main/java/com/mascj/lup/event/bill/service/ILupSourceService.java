package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.datacenter.client.vo.res.AchievementClientPreviewVO;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.entity.LupSource;
import com.mascj.lup.event.bill.vo.SourceTileVO;
import com.mascj.lup.event.vo.AchievementPreviewVO;

import java.util.List;

/**
 * <p>
 * 事件资源记录表 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface ILupSourceService extends IService<LupSource> {


    Result<List<SourceTileVO>> listSourceTile(BillDTO billDTO);

    AchievementClientPreviewVO fetchTileOptions(Long airlineTaskId);
    /**
     * 保存资源 资源已经存在 返回 已存储的资源的id
     * @param source
     * @return
     */
    LupSource saveSource(LupSource source);

    void saveBatchSource(List<LupSource> sourceList);
}
