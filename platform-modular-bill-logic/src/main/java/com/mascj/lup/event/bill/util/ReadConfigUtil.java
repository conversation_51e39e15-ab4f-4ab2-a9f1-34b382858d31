package com.mascj.lup.event.bill.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.tools.RedisKey;
import com.mascj.kernel.tools.RedisPlane;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.PushKQDTO;
import com.mascj.lup.event.bill.vo.config.*;
import com.mascj.support.config.dto.XlmActivityState;
import com.mascj.support.config.feign.IConfigValueProvider;
import com.mascj.support.config.vo.ConfigInVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/18 10:18
 * @describe
 */
@Component
public class ReadConfigUtil<T> {

    @Autowired
    private IConfigValueProvider configValueProvider;

    @Autowired
    private RedisPlane redisPlane;



    public T putCommonCache(String key,T data){
        RedisKey redisKey = RedisKey.forService(VariableConstants.lupHandleSystemTag, ReadConfigUtil.class)
                .forParameter(VariableConstants.RedisEventDataCode
                        , String.valueOf(LmContextHolder.getTenantId()),key);
        key = redisKey.toString();

        RedisOpsUtils.set(key,JSONUtil.toJsonStr(data));

        return data;
    }

    public boolean existCommonCache(String key){

        RedisKey redisKey = RedisKey.forService(VariableConstants.lupHandleSystemTag, ReadConfigUtil.class)
                .forParameter(VariableConstants.RedisEventDataCode
                        , String.valueOf(LmContextHolder.getTenantId()),key);

        key = redisKey.toString();
        Boolean exist = RedisOpsUtils.checkExist(key);

        if(!exist){
            RedisOpsUtils.set(key,JSONUtil.toJsonStr(redisKey));
        }

        return exist;
    }
    public boolean delCommonCache(String key){

        RedisKey redisKey = RedisKey.forService(VariableConstants.lupHandleSystemTag, ReadConfigUtil.class)
                .forParameter(VariableConstants.RedisEventDataCode
                        , String.valueOf(LmContextHolder.getTenantId()),key);

        key = redisKey.toString();
        return RedisOpsUtils.del(key);
    }


    public HandleModuleConfigVO readHandleModuleConfig(){

        RedisKey redisKey = RedisKey.forService(VariableConstants.lupHandleSystemTag, ReadConfigUtil.class)
                .forParameter(VariableConstants.HandleModuleConfigCode, String.valueOf(LmContextHolder.getTenantId()));

        return redisPlane.getOrCreate(redisKey,()->{

                    ConfigInVo config = new ConfigInVo();
                    config.setCode(VariableConstants.HandleModuleConfigCode);
                    config.setTenantId(Long.parseLong(LmContextHolder.getTenantId()));

                    Result<JSONObject> configValResult = configValueProvider.getConfigByCode(config);
                    if (configValResult.isSuccess() && configValResult.getData() != null) {

                        JSONObject dataJson = configValResult.getData();
                        HandleModuleConfigCodeVO a = BeanUtil.toBean(dataJson, HandleModuleConfigCodeVO.class);

                        HandleModuleConfigVO handleModuleConfigVO = JSONUtil.toBean(JSONUtil.toJsonStr(dataJson), HandleModuleConfigVO.class);

                        handleModuleConfigVO.setHmcFlowActivityKey(JSONUtil.toList(a.getHmcFlowActivityKey(), HmcFlowActivityVO.class));
                        handleModuleConfigVO.setHmcFlowActivityVOMap(
                                handleModuleConfigVO.getHmcFlowActivityKey().stream().collect(Collectors.toMap(
                                        HmcFlowActivityVO::getFlowState,e->e ,(e1,e2)->e1
                                ))
                        );

                        handleModuleConfigVO.setFlowEntityTimeOutKey(JSONUtil.toList(a.getFlowEntityTimeOutKey(), FlowEntityTimeOutVO.class));

                        handleModuleConfigVO.setFlowEntityTimeOutVOMap(

                                handleModuleConfigVO.getFlowEntityTimeOutKey().stream().collect(Collectors.toMap(
                                        FlowEntityTimeOutVO::getFlowState,e->e ,(e1,e2)->e1
                                ))
                        );

                        handleModuleConfigVO.setEventNotificationApprovalRejectKey(
                                JSONUtil.toBean(a.getEventNotificationApprovalRejectKey(), EventNotificationApprovalRejectVO.class)
                        );

                        handleModuleConfigVO.setEventSmsTimeListKey(JSONUtil.parseArray(a.getEventSmsTimeListKey()).toList(String.class));

                        handleModuleConfigVO.setExportBillDataTemplateStrategy(a.getExportBillDataTemplateStrategyKey());


                        try{
                            ConfigInVo eventHangingSwitchConfig = new ConfigInVo();
                            eventHangingSwitchConfig.setIKey(VariableConstants.EventHangingSwitch);
                            eventHangingSwitchConfig.setTenantId(Long.parseLong(LmContextHolder.getTenantId()));
                            Result<String> eventHangingSwitchConfigResult = configValueProvider.getConfigByKey(eventHangingSwitchConfig);
                            if(eventHangingSwitchConfigResult.isSuccess() && ObjectUtil.isNotEmpty(eventHangingSwitchConfigResult.getData())) {
                                handleModuleConfigVO.setEventHangingSwitch(Boolean.parseBoolean(eventHangingSwitchConfigResult.getData()));
                            }
                        }catch (Exception exp){
                            exp.printStackTrace();
                        }

                        config = new ConfigInVo();
                        config.setCode(VariableConstants.LupBillTranslateConfigCode);
                        config.setTenantId(Long.parseLong(LmContextHolder.getTenantId()));
                        configValResult =  configValueProvider.getConfigByCode(config);
                        if (configValResult.isSuccess() && configValResult.getData() != null) {
                            PushKQDTO pushKQDTO = JSONUtil.toBean(JSONUtil.toJsonStr(configValResult.getData()), PushKQDTO.class);
                            handleModuleConfigVO.setPushKQDTO(pushKQDTO);
                        }


                        return handleModuleConfigVO;
                    }
                    return null;
                },HandleModuleConfigVO.class);
    }

    public PushKQDTO readPushConfig(){

        RedisKey redisKey = RedisKey.forService(VariableConstants.lupHandleSystemTag, ReadConfigUtil.class)
                .forParameter(VariableConstants.LupBillTranslateConfigCode, String.valueOf(LmContextHolder.getTenantId()));

        return redisPlane.getOrCreate(redisKey,()->{
            ConfigInVo config = new ConfigInVo();
            config.setCode(VariableConstants.LupBillTranslateConfigCode);
            config.setTenantId(Long.parseLong(LmContextHolder.getTenantId()));
            Result<JSONObject>configValResult =  configValueProvider.getConfigByCode(config);
            if (configValResult.isSuccess() && configValResult.getData() != null) {
                PushKQDTO pushKQDTO = JSONUtil.toBean(JSONUtil.toJsonStr(configValResult.getData()), PushKQDTO.class);
                return pushKQDTO;
            }
            return null;
        },PushKQDTO.class);
    }
}
