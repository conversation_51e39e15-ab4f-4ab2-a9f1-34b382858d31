package com.mascj.lup.event.bill.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.tools.linq.Linqs;
import com.mascj.lup.event.bill.entity.*;
import com.mascj.lup.event.bill.mapper.LupEventSmsGradeMapper;
import com.mascj.lup.event.bill.mapper.LupFlowSmsMapper;
import com.mascj.lup.event.bill.mapper.LupGridUserMapper;
import com.mascj.lup.event.bill.service.*;
import com.mascj.lup.event.bill.task.INotificationTaskService;
import com.mascj.lup.event.bill.util.ReadConfigUtil;
import com.mascj.lup.event.bill.vo.BillUserLabelVO;
import com.mascj.lup.event.bill.vo.config.HandleModuleConfigVO;
import com.mascj.lup.event.bill.vo.config.HmcFlowActivityVO;
import com.mascj.platform.system.dto.RoleInfo;
import com.mascj.platform.system.dto.UserRoleInfo;
import com.mascj.platform.system.feign.ISysUserProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/18 18:07
 * @describe
 */
@Slf4j
@Service
public class LupFlowSmsServiceImpl extends ServiceImpl<LupFlowSmsMapper, LupFlowSms> implements ILupFlowSmsService {

    @Autowired
    private ReadConfigUtil readConfigUtil;
    @Autowired
    private ILupGridUserService gridUserService;

    @Autowired
    private ISysUserProvider userProvider;

    @Autowired
    private ILupBillService billService;

    @Autowired
    private LupEventLabelService lupEventLabelService;

    @Autowired
    private ILupBillNotificationService billNotificationService;

    @Autowired
    private INotificationTaskService notificationTaskService ;
    @Autowired
    private LupEventSmsGradeMapper lupEventSmsGradeMapper ;

    @Override
    public void recordFlowRejectSms(Long flowId, Integer currentFlowState, Integer approvalFlowState) {
        //驳回判断
        BillUserLabelVO billInfo = billService.listBillUserLabelOneVO(flowId);
        if (billInfo == null) {

            log.error("billInfo:{}", JSONUtil.toJsonStr(billInfo));
            log.error("flowId:{}", JSONUtil.toJsonStr(flowId));
            return;
        }
        Integer dataOrigin = billInfo.getDataOrigin();
        Long labelId = billInfo.getLabelId();
        LupEventSmsGrade lupEventSmsGrade = lupEventSmsGradeMapper.selectOne(new LambdaQueryWrapper<>(new LupEventSmsGrade())
                .eq(LupEventSmsGrade::getLabelId, labelId)
                .eq(LupEventSmsGrade::getCodeValue, dataOrigin));
        if (lupEventSmsGrade == null) {
            return;
        }
        if (lupEventSmsGrade.getType() != null && lupEventSmsGrade.getType() == 2){
            return;
        }


        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();

        if(!handleModuleConfigVO.getEventSmsSwitchKey()){
//            System.out.println("配置关闭 驳回不发送短信");
            return;
        }

        if(handleModuleConfigVO != null && handleModuleConfigVO.getFlowEntityTimeOutVOMap() !=null ) {

            if(handleModuleConfigVO.getEventNotificationApprovalRejectKey().getCurrentFlowState() == currentFlowState
                    &&handleModuleConfigVO.getEventNotificationApprovalRejectKey().getApprovalFlowState() == approvalFlowState){


                //记录发送短信的用户列表
                List<LupBillNotification> billNotificationList = recordNotificationUserList(billInfo,flowId,currentFlowState);

                //记录需要发送的事件通知信息记录
                if(handleModuleConfigVO.getEventNotificationApprovalRejectKey().getImmediateSend()){
                    //立即发送 给指定的人 根据 flowId 找到 相关人员 发送短信 保存到指定数据表


                    try {

                        if (billNotificationList.size() > 0){

//                            尊敬的用户您好，网格名称：${gridName}，事件标签：${eventLabel}，事件编号：${eventNo}， 事件处理结果被驳回，请及时处理！

                        List<Long>userIdList = billNotificationList.stream().map(LupBillNotification::getUserId).collect(Collectors.toList());
                        Map map = new HashMap();
                        map.put("gridName", billInfo.getGridName());//网格名称
                             map.put("eventLabel", billInfo.getEventLabel());//事件标签
                            map.put("eventNo", billInfo.getEventNo());//事件编号
                        //发送消息
                        notificationTaskService.sendSms(userIdList, handleModuleConfigVO.getEventNotificationForDealSourceTypeDictCode(),
                                handleModuleConfigVO.getEventNotificationApprovalRejectKey().getEventNotificationFroRejectDictCodeImmediate(), map);

                            billNotificationList.forEach(e->{
                                e.setStatus(1);

                            });

                            billNotificationService.updateBatchById(billNotificationList);

                    }
                    }catch (Exception e1){
                        e1.printStackTrace();
                    }

                }else
                {
                    //定时发送 给指定的人 根据 flowId 找到 相关人员 发送短信 保存到指定数据表
                    if (billNotificationList.size() > 0){
                        billNotificationList.forEach(e->{
                            e.setTimerSms(1);
                        });

                        billNotificationService.updateBatchById(billNotificationList);
                    }
                }

            }
        }
    }



    /**
     *
     * 1、驳回的条件为前提；
     * 2、查找该事件需要通知的人；
     * 3、根据事件 对应的网格人员；
     *
     *
     * 4、过滤 网格人员 订阅该事件的标签  或者  没有订阅任何标签 也能收到短信
     *
     * 5、网格人员 有 巡查的权限；
     *
     * 查询角色或者权限
     *
     * billId，flowId，userId
     *
     *
     * @param flowId
     */
    private List<LupBillNotification>  recordNotificationUserList(BillUserLabelVO billInfo,Long flowId,Integer configFlowState){
        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();
        Map<Integer,HmcFlowActivityVO> hmcFlowActivityVOMap = handleModuleConfigVO.getHmcFlowActivityVOMap();

        List<BillUserLabelVO> billUserLabelVOList = billService.listBillUserLabelVO(flowId);




        List<Long> userIdList = new ArrayList<>();


        billUserLabelVOList.forEach(item->{

            Integer bf = 0;

            //用户id
            //用户标签id
            List<Long> tmpuserIdList = new ArrayList();
            tmpuserIdList.add(item.getUserId());
            List<Long> labelIdListPerm = lupEventLabelService.labelIdsByUserIds(tmpuserIdList);


            if(labelIdListPerm!=null && labelIdListPerm.contains(item.getLabelId())){
                //有效用户
//                if(!userIdList.contains(item.getUserId()))userIdList.add(item.getUserId());
                bf++;
            }

            if(labelIdListPerm==null || labelIdListPerm.size() == 0){
                //有效用户
//                if(!userIdList.contains(item.getUserId()))userIdList.add(item.getUserId());
                if(bf == 0)bf++;
            }

            //拥有指定的 角色、权限

            Result<UserRoleInfo> userRoleInfoResult = userProvider.getUserRoleById(item.getUserId());
            List<String> roleList = new ArrayList<>();
            if (userRoleInfoResult.isSuccess() && userRoleInfoResult.getData() != null && userRoleInfoResult.getData().getRoles() != null) {
                roleList = Linqs.of(userRoleInfoResult.getData().getRoles()).select(RoleInfo::getCode);
            }


            Result<List<String>> permissionListResult = userProvider.getPermissionByRequest(item.getUserId());

            List<String> permissionList = new ArrayList<>();
            if (permissionListResult.isSuccess() && permissionListResult.getData() != null) {
                permissionList = permissionListResult.getData();
            }


            HmcFlowActivityVO hmcFlowActivityVO = hmcFlowActivityVOMap.get(configFlowState);

            // 找出两个列表的交集
            List<String> intersection = roleList.stream()
                    .filter(hmcFlowActivityVO.getRoleCode()::contains)
                    .collect(Collectors.toList());

            if (permissionList.contains(hmcFlowActivityVO.getPermissionCode()) || intersection.size() > 0) {
                bf++;
            }

            if(bf == 2){// 标签权限 + 功能权限
                if(!userIdList.contains(item.getUserId()))userIdList.add(item.getUserId());
            }

        });




        List<LupBillNotification> billNotificationList = new ArrayList<>();
        if(userIdList.size() > 0){


            userIdList.forEach(userId -> {
                LupBillNotification billNotification = new LupBillNotification();

                billNotification.setFlowId(flowId);
                billNotification.setUserId(userId);
                billNotification.setProcessTaskId(billInfo.getProcessTaskId());
                billNotification.setBillId(billInfo.getBillId());
                billNotification.setFlowState(configFlowState);
                billNotificationList.add(billNotification);

            });

            if(billNotificationList.size() > 0)
            billNotificationService.saveBatch(billNotificationList);





        }


        return billNotificationList;


    }

}
