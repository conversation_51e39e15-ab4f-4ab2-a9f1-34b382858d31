package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.LabelListDTO;
import com.mascj.lup.event.bill.dto.LupRangeCategoryAddDTO;
import com.mascj.lup.event.bill.entity.LupLabel;
import com.mascj.lup.event.bill.entity.LupRangeLine;
import com.mascj.lup.event.bill.vo.EventDataLabelVO;
import com.mascj.lup.event.bill.vo.LabelItemVO;
import com.mascj.lup.event.bill.vo.LupRangeLineVO;

import java.util.List;

/**
 * <p>
 * 事件标签 按名字去重 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface ILupRangeLineService extends IService<LupRangeLine> {
    List<LupRangeLineVO> listByRangeCategoryId(Long categoryId);

}
