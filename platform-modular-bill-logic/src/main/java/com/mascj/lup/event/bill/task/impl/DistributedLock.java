package com.mascj.lup.event.bill.task.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @date 2024/10/16 19:51
 * @describe
 */
@Component
public class DistributedLock {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public boolean lock(String key, long timeout, TimeUnit unit) {
        return stringRedisTemplate.opsForValue().setIfAbsent(key, "locked", timeout, unit);
    }
    public boolean lock(String key, TimeUnit unit) {
        return stringRedisTemplate.opsForValue().setIfAbsent(key, "locked");
    }

    public void unlock(String key) {
        stringRedisTemplate.delete(key);
    }
}
