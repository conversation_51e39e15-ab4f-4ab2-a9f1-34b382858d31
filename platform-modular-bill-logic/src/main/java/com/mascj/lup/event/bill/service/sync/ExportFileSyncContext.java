package com.mascj.lup.event.bill.service.sync;

import com.alibaba.fastjson.JSON;

import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.$;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.redis.core.RedisService;
import com.mascj.kernel.tools.RedisKey;
import com.mascj.kernel.tools.SetFunction;
import com.mascj.kernel.tools.linq.Linqs;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.dto.ExportFileDTO;
import com.mascj.lup.event.bill.sync.ExportFileSyncState;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Component
@AllArgsConstructor
public class ExportFileSyncContext   {

    // 最大并发数
    public final int maxConcurrency = 10;
    private final RedisService redisService;
    private final RedisKey redisKeyTemplate = RedisKey.forEntity("biz-aep-admin-file-export", ExportFileSyncState.class);
    private final RedisKey taskList = redisKeyTemplate.forItem("tasks");

    /**
     * 注册任务
     *
     * @return
     */
    public ExportFileSyncState registerTask(ExportFileDTO exportFileDto) {


        Assert.isTrue(getCurrentTask().size() <= maxConcurrency, LocaleMessageUtil.getMessage(EventTipKey.SyncRepeatExe));

        ExportFileSyncState syncState = SetFunction
//                .lambdaSet(ExportFileSyncState::getAppId, exportFileDto.getAppId())
//                .set(ExportFileSyncState::getBatchCode, exportFileDto.getBizCode())
//                .set(ExportFileSyncState::getTotal, exportFileDto.getDataTotal())
                .lambdaSet(ExportFileSyncState::getUuid, UUID.randomUUID().toString().replace("-", ""))
                .set(ExportFileSyncState::getUserId, LmContextHolder.getUserId())
                .set(ExportFileSyncState::getTenantId, LmContextHolder.getTenantId())
                .set(ExportFileSyncState::getLang, LmContextHolder.getLang())



                .getInstance();

        updateState(syncState);
        newTask(syncState.getUuid());

        return syncState;
    }
    public ExportFileSyncState registerTask(long appId, String batchCode, int total) {
        Assert.isTrue(getCurrentTask().size() <= maxConcurrency, LocaleMessageUtil.getMessage(EventTipKey.SyncRepeatExe));

        ExportFileSyncState syncState = SetFunction
                .lambdaSet(ExportFileSyncState::getAppId, appId)
                .set(ExportFileSyncState::getBatchCode, batchCode)
                .set(ExportFileSyncState::getTotal, total)
                .set(ExportFileSyncState::getUuid, UUID.randomUUID().toString().replace("-", ""))
                .set(ExportFileSyncState::getUserId, LmContextHolder.getUserId())
                .set(ExportFileSyncState::getTenantId, LmContextHolder.getTenantId())
                .getInstance();

        updateState(syncState);
        newTask(syncState.getUuid());

        return syncState;
    }



    /**
     * 获取当前任务
     *
     * @return
     */
    public List<String> getCurrentTask() {
        String content = (String) redisService.get(taskList.toString());
        if ($.isBlank(content)) {
            return new ArrayList<>();
        }

        return JSON.parseArray(content, String.class);
    }

    private void newTask(String id) {
        Assert.hasText(id, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneSyncId, Arrays.asList(EventTipKey.NoneTip)));
        List<String> tasks = getCurrentTask();
        tasks.add(id);

        redisService.set(taskList.toString(), JSON.toJSONString(tasks));
    }

    private void removeTask(String id) {
        Assert.hasText(id, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneSyncId, Arrays.asList(EventTipKey.NoneTip)));
        List<String> tasks = Linqs.of(getCurrentTask()).filter(x -> !x.equals(id));
        redisService.set(taskList.toString(), JSON.toJSONString(tasks));
    }

    /**
     * 获取任务状态
     *
     * @param id
     * @return
     */
    public ExportFileSyncState getTaskState(String id) {
        return (ExportFileSyncState) redisService.get(redisKeyTemplate.forItem(id).toString());
    }

    /**
     * 更新任务状态
     *
     * @param state
     */
    public void updateState(ExportFileSyncState state) {
        final String key = redisKeyTemplate.forItem(state.getUuid()).toString();
        redisService.set(key, state);
        redisService.expire(key, 10 * 60L);
    }

    /**
     * 完成任务
     *
     * @param id
     */
    public void finishTask(String id) {
        // 删除当前任务
        removeTask(id);
        // 状态信息保留10分钟
        redisService.expire(redisKeyTemplate.forItem(id).toString(), 10 * 60L);
    }
}
