package com.mascj.lup.event.bill.service.impl;

import cc.lyiot.framework.common.pojo.CommonResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mascj.event.dimension.dto.ReceiveDataDTO;
import com.mascj.event.dimension.enumeration.ModuleDataSourceTypeEnum;
import com.mascj.event.dimension.feign.IDataPushProvider;
import com.mascj.kernel.common.api.IResultCode;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.cds.survey.feign.SurveyAchievementFeign;
import com.mascj.lup.datacenter.client.feign.DataCenterClientTaskFeign;
import com.mascj.lup.datacenter.client.vo.res.AchievementClientPreviewVO;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.*;
import com.mascj.lup.event.bill.enums.*;
import com.mascj.lup.event.bill.feign.LupDataNotificationFeign;
import com.mascj.lup.event.bill.mapper.LupFlowEntityMapper;
import com.mascj.lup.event.bill.service.*;
import com.mascj.lup.event.bill.support.MessageSender;
import com.mascj.lup.event.bill.util.*;
import com.mascj.lup.event.bill.vo.LupBillVO;
import com.mascj.lup.event.bill.vo.LupSubmitForm;
import com.mascj.lup.event.bill.vo.SendFourDoVo;
import com.mascj.lup.event.bill.vo.SendFourResutVo;
import com.mascj.lup.event.bill.vo.config.HandleModuleConfigVO;
import com.mascj.lup.event.feign.EventStreamFeign;

import com.mascj.support.config.entity.SysConfig;
import com.mascj.support.config.feign.IConfigValueProvider;
import com.mascj.support.config.feign.ISysConfigProvider;
import com.mascj.support.config.vo.ConfigInVo;
import com.mascj.support.file.api.feign.IFileProvider;
import com.mascj.support.file.api.feign.IFileTransferProvider;
import com.mascj.support.file.api.model.XlmFile;
import com.mascj.support.file.api.vo.FileTransferReqVO;
import com.mascj.support.file.api.vo.FileTransferResVO;
import io.jsonwebtoken.lang.Assert;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executor;

@Service
@AllArgsConstructor
@Slf4j
public class DataReceiveServiceImpl implements IDataReceiveService {


    private final ILupBatchService iLupBatchService;
    private final ILupBatchSourceService iLupBatchSourceService;
    private final ILupSourceService sourceService;

    private final ILupLabelService labelService;

    private final ILupDataService dataService;

    private final ILupDataLabelService dataLabelService;

    private final ILupBillService iLupBillService;

    private final IFileProvider fileProvider;
    private final DataCenterClientTaskFeign streamProvider;
    private final ISysConfigProvider sysConfigProvider;
    private final IConfigValueProvider configValueProvider;

    private final ILupDataPushLogService dataPushLogService;

    private final LupDataNotificationFeign dataNotificationFeign;
    private final ILupFlowService lupFlowService;

    @Resource
    private   LupFlowEntityMapper lupFlowEntityMapper;

    @Qualifier("syncProduceImageExecutorPool")  //指定某个bean
    private final Executor syncProduceImageExecutorPool;

    private ApplicationContext applicationContext;

    private final IDataPushProvider dataPushProvider;

    private final MessageSender messageSender;

    private final ReadConfigUtil readConfigUtil;

    private final SurveyAchievementFeign surveyAchievementFeign;

    private final IFileTransferProvider  fileTransferProvider;

    /**
     * 方法废弃不适用了 张保国  2024-01-10
     * @param billId
     * @return
     */
    @Override
    public Result pushEventData(Long billId) {
        LupBill lupBill = iLupBillService.getById(billId);
        LupData lupData = dataService.getById(lupBill.getDataId());

        LupLabel label = labelService.findLabelByDataId(lupData.getId());
        return translateData(lupData,BeanUtil.copyProperties(lupBill, LupBillVO.class),label.getName());
    }


    @Override
    public Result<SendFourResutVo> pushEventDataFour(SendFourDoVo sendFourDoVo) {

        String processTaskId = sendFourDoVo.getProcessTaskId();
        LupFlowEntity ee = new LupFlowEntity();
        ee.setProcessTaskId(processTaskId);
        LupFlowEntity byOne = lupFlowEntityMapper.getByOne(ee);
        if (byOne == null) {
            return Result.data( new SendFourResutVo(false, "任务不存在"));
        }

        //设置租户ID
        LmContextHolder.setTenantId(byOne.getTenantId().toString());

        //从流程里触发向第三方推送数据 流程化的。

        LupBill lupBill = iLupBillService.getOne(new LambdaQueryWrapper<>(new LupBill())
                .eq(LupBill::getFlowEntityId, byOne.getId()));
        if (lupBill == null) {
            return Result.data( new SendFourResutVo(false, "工单不存在"));
        }
        LupData lupData = dataService.getById(lupBill.getDataId());

        LupLabel label = labelService.findLabelByDataId(lupData.getId());
        return translateData(lupData,BeanUtil.copyProperties(lupBill, LupBillVO.class),label.getName());
    }

    /**
     * 推送事件数据到其他服务
     * @param dataId
     */
    @Override
    public void pushEventDataToOtherServer(Long dataId) {


        LupData lupData = dataService.getById(dataId);


        List<LupBill> billList = iLupBillService.list(Wrappers.<LupBill>lambdaQuery().eq(LupBill::getDeleted,0).eq(LupBill::getDataId,dataId).orderByDesc(LupBill::getCreateTime));
        LupBill bill = billList.size() > 0?billList.get(0):null;

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();
        PushKQDTO pushKQDTO = handleModuleConfigVO.getPushKQDTO();
        GenerateBillFlagEnum generateBillFlagEnum = GenerateBillFlagEnum.parse(pushKQDTO.getGenerateBillFlag());

        //是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）

        //未甄别的数据和配置是 只处理已甄别的数据 是不能向第三方传输的

        Boolean pushable = Boolean.TRUE;
        //重复推送  默认 false 可以设置为 允许重复推  重复推的情况下 需要 客户接收位置做好数据保存和更新的操作
        if(!pushKQDTO.getPushRepeat()) {
            //不允许重新推送
            List<LupDataPushLog> dataPushLogList = dataPushLogService.list(Wrappers.<LupDataPushLog>lambdaQuery().eq(LupDataPushLog::getDataId, dataId)
                    .eq(LupDataPushLog::getPushState, DataPushStateEnum.PushSuccess.getCode()));

            if(ObjectUtil.isNotEmpty(dataPushLogList)){
                //已经有了成功推送的数据 就不重新重复推送了
                pushable = Boolean.FALSE;
            }

        }



        if( pushKQDTO!=null && pushKQDTO.getOpen()
        && (
                (lupData.getGenerateBill() == 1 && generateBillFlagEnum == GenerateBillFlagEnum.GenerateBillFlagEnum)
                || generateBillFlagEnum == GenerateBillFlagEnum.AllGenerateBillFlagEnum)
                && pushable
        )
        {

            LupDataPushLog dataPushLog = new LupDataPushLog();

            dataPushLog.setDataOrigin(lupData.getDataOrigin());
            dataPushLog.setPushStage(pushKQDTO.getStageName());
            dataPushLog.setPushAuto(pushKQDTO.getAutoPush() ? 1 : 0);
            dataPushLog.setPushState(DataPushStateEnum.NonePush.getCode());
            dataPushLog.setDataId(dataId);
            dataPushLog.setPushMode(pushKQDTO.getGenerateBillFlag());
            if (bill != null) {
                dataPushLog.setBillId(bill.getId());
                dataPushLog.setBillNumber(bill.getBillNumber());
            }
            dataPushLog.setGenerateBill(lupData.getGenerateBill());
            dataPushLog.setPushEventFlow(pushKQDTO.getPushEventFlow());
            dataPushLogService.save(dataPushLog);

            if (ObjectUtil.isNotEmpty(pushKQDTO) && pushKQDTO.getOpen()
                    && ObjectUtil.isNotEmpty(pushKQDTO.getStageName())
                    ) {

                PushEventFlowEnum pushEventFlowEnum = PushEventFlowEnum.parse(pushKQDTO.getPushEventFlow());
                //流程关联？ 默认流程推送流程 是否自动推？
                if (pushEventFlowEnum == PushEventFlowEnum.DefaultFlow) {
                    //默认流程

                    IPushDataService iPushDataService = applicationContext.getBean(pushKQDTO.getStageName(), IPushDataService.class);
                    //执行策略
                    if (iPushDataService != null) {
                        //记录推送记录
                        //内部更新推送记录结果
                        iPushDataService.translateData(dataId, dataPushLog.getId());
                    }


                } else if ( pushEventFlowEnum == PushEventFlowEnum.PushEventFlow && generateBillFlagEnum == GenerateBillFlagEnum.GenerateBillFlagEnum) {
                    //推送流程

                    try {

                        log.info("获取的租户pushKQDTO： " + JSON.toJSONString(pushKQDTO));
                        log.info("获取的租户pushKQDTO1： " + JSON.toJSONString(pushKQDTO != null && pushKQDTO.getOpen() != null
                                && pushKQDTO.getStageName() != null && pushKQDTO.getOpen()));
                        if (pushKQDTO != null && pushKQDTO.getOpen() != null && pushKQDTO.getStageName() != null && pushKQDTO.getOpen()) {
                            //是四平台才推送
                            LupSubmitForm ee = new LupSubmitForm();
                            if (bill != null) {
                                LupFlowEntity flowEntity = lupFlowEntityMapper.selectById(bill.getFlowEntityId());
                                if (flowEntity != null) {
                                    ee.setProcessInstanceId(flowEntity.getProcessInstanceId());
                                    lupFlowService.submitTaskByProcessId(ee);
                                }
                            }

                        }
                    } catch (Exception exp) {

                        exp.printStackTrace();
                    }

                }
            }

        }

    }

    @Override
    public void doesNotPushEventData(Long dataId) {

        LupData lupData = dataService.getOne(Wrappers.<LupData>lambdaQuery().eq(LupData::getId,dataId).select(LupData::getId,LupData::getPushState));
        lupData.setPushState(2);
        dataService.updateById(lupData);

    }

    @Override
    public Result<SendFourResutVo> translateData(LupData lupData, LupBillVO lupBill,String labelName){

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();

        {

            try {
                PushKQDTO pushKQDTO = handleModuleConfigVO.getPushKQDTO();

                if (pushKQDTO != null && pushKQDTO.getOpen() != null && pushKQDTO.getStageName() != null) {

                    IPushDataService iPushDataService  = applicationContext.getBean(pushKQDTO.getStageName(),IPushDataService.class);
                    //执行策略
                    Result<SendFourResutVo> sendFourResutVoResult = iPushDataService.translateData(pushKQDTO, lupData, lupBill, labelName);


                    return  sendFourResutVoResult;
                }
            }catch (Exception exp){
                exp.printStackTrace();
                SendFourResutVo sendFourResutVo = new SendFourResutVo();
                sendFourResutVo.setFailMsg(exp.getMessage());
                sendFourResutVo.setSuccess(false);
                return Result.data(sendFourResutVo);
            }
        }
        return Result.fail();
    }

    /**
     * 【无人机平台】提交事件数据
     * @param flySystemEventDataSourceDTO
     * @return
     */
    @Override
    public Result postCompareDataSystem(FlySystemEventDataSourceDTO flySystemEventDataSourceDTO) {

        log.error("飞机传输过来的数据： "+  JSON.toJSONString(flySystemEventDataSourceDTO));

        ProjectUtil.getProjectId();

        if(flySystemEventDataSourceDTO.getTenantId()!=null && !"".equals(flySystemEventDataSourceDTO.getTenantId())){
            LmContextHolder.setTenantId(flySystemEventDataSourceDTO.getTenantId());
        }

        if(flySystemEventDataSourceDTO.getUserId()!=null && flySystemEventDataSourceDTO.getUserId() > 0){
            LmContextHolder.setUserId(flySystemEventDataSourceDTO.getUserId());
        }



        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();
        PushKQDTO pushKQDTO = handleModuleConfigVO.getPushKQDTO();

        if(pushKQDTO.getOpen()) {
            GenerateBillFlagEnum generateBillFlagEnum = GenerateBillFlagEnum.parse(pushKQDTO.getGenerateBillFlag());


            if(flySystemEventDataSourceDTO.getGenerateBill() == 0 && generateBillFlagEnum == GenerateBillFlagEnum.GenerateBillFlagEnum){

                Assert.isTrue(Boolean.FALSE
                        , "数据甄别状态没有应对统一传给第三方配置，请检查数据和配置");
            }


        }



        Long projectId = flySystemEventDataSourceDTO.getProjectId();

        List<LupData> lupDataList = dataService.listByOriginalDataLogo(flySystemEventDataSourceDTO.getOriginalDataLogo());


        LupData data;

        GenerateBillEnum generateBillEnum = GenerateBillEnum.parse(flySystemEventDataSourceDTO.getGenerateBill());

        GenerateBillEnum tmpGenerateBillEnum = null;

        if(lupDataList.size()  > 0){
            data = lupDataList.get(0);
            tmpGenerateBillEnum = GenerateBillEnum.parse(data.getGenerateBill());
        }else{
            data = new LupData();
        }

        Assert.isTrue(lupDataList.size()==0
                        || (lupDataList.size() > 0 && data.getGenerateBill() == GenerateBillEnum.GenerateBillNothing.getValue())
                ,LocaleMessageUtil.getMessage(EventTipKey.EventDataCommitRepeat));



        if(lupDataList.size()==0) {



            String tenantId = LmContextHolder.getTenantId();
            //转存文件
            if(ObjectUtil.isNotEmpty(flySystemEventDataSourceDTO.getCurrentPictureUrl()))
                flySystemEventDataSourceDTO.setCurrentPictureUrl(transferPicFile(tenantId,flySystemEventDataSourceDTO.getCurrentPictureUrl()));
            if(ObjectUtil.isNotEmpty(flySystemEventDataSourceDTO.getComparePictureUrl()))
                flySystemEventDataSourceDTO.setComparePictureUrl(transferPicFile(tenantId,flySystemEventDataSourceDTO.getComparePictureUrl()));




            data.setProjectId(projectId);
            data.setExtraData(flySystemEventDataSourceDTO.getExtraData());
            data.setDataOrigin(flySystemEventDataSourceDTO.getEventType());
            data.setDataType(EventDataType.PictureDataType.getValue());
            data.setCompareType(EventCompareType.parse(flySystemEventDataSourceDTO.getCompareType()).getValue());
            data.setOriginalDataLogo(flySystemEventDataSourceDTO.getOriginalDataLogo());

            data.setHappenedTime(flySystemEventDataSourceDTO.getHappenedTime());

            data.setFlyTaskId(flySystemEventDataSourceDTO.getFlyTaskId());
            data.setFlyTaskNumber(flySystemEventDataSourceDTO.getFlyTaskNumber());
            data.setFlyTaskName(flySystemEventDataSourceDTO.getFlyTaskName());
            data.setAirLineName(flySystemEventDataSourceDTO.getAirLineName());
            data.setDeviceId(flySystemEventDataSourceDTO.getDeviceId());
            data.setDeviceName(flySystemEventDataSourceDTO.getDeviceName());
            data.setDeviceSn(flySystemEventDataSourceDTO.getDeviceSn());
            data.setEventDesc(flySystemEventDataSourceDTO.getDesc());

            data.setLocationLng(flySystemEventDataSourceDTO.getCurrentPictureLng());
            data.setLocationLat(flySystemEventDataSourceDTO.getCurrentPictureLat());
            revertEventDataLocation(data);


            LupLabel label = new LupLabel();
            label.setProjectId(projectId);
            label.setName(flySystemEventDataSourceDTO.getEventName());
            label = labelService.saveLabel(label);

            List<LupSource> sourceList = new ArrayList<>();
            LupSource currentSource = new LupSource();
            currentSource.setProjectId(projectId);
            currentSource.setUrl(flySystemEventDataSourceDTO.getCurrentPictureUrl());
            currentSource.setType(EventSourceType.PictureSourceType.getValue());
            currentSource.setFlyDate(flySystemEventDataSourceDTO.getCurrentFlyDate());
            sourceList.add(currentSource);

            data.setCurrentBatchId(saveBatchPictureData(sourceList, projectId));

            sourceList.clear();

            DataPicDTO dataPicDTO = new DataPicDTO();
            dataPicDTO.setEventPictureUrl(flySystemEventDataSourceDTO.getCurrentPictureUrl());
            if (!dataPicDTO.getEventPictureUrlList().contains(flySystemEventDataSourceDTO.getCurrentPictureUrl()))
                dataPicDTO.getEventPictureUrlList().add(flySystemEventDataSourceDTO.getCurrentPictureUrl());

            if (flySystemEventDataSourceDTO.getCompareType() == 2) {
                sourceList = new ArrayList<>();
                Assert.notNull(flySystemEventDataSourceDTO.getComparePictureUrl(), LocaleMessageUtil.getMessage(EventTipKey.NoneComparePictureUrl));
                Assert.isTrue(!"".equals(flySystemEventDataSourceDTO.getComparePictureUrl()), LocaleMessageUtil.getMessage(EventTipKey.NoneComparePictureUrl));

                LupSource compareSource = new LupSource();
                compareSource.setProjectId(projectId);
                compareSource.setUrl(flySystemEventDataSourceDTO.getComparePictureUrl());
                compareSource.setType(EventSourceType.PictureSourceType.getValue());
                compareSource.setFlyDate(flySystemEventDataSourceDTO.getCompareFlyDate());
                sourceList.add(compareSource);
                data.setCompareBatchId(saveBatchPictureData(sourceList, projectId));

                //合并图片 并上传后 得到url


            }
            JSONObject extraDataJson = JSONUtil.createObj();
            extraDataJson.putAll(JSONUtil.parseObj(JSONUtil.toJsonStr(dataPicDTO)));
            if (ObjectUtil.isNotEmpty(flySystemEventDataSourceDTO.getExtraData())) {

                DataPicDTO dto = JSONUtil.toBean(flySystemEventDataSourceDTO.getExtraData(),DataPicDTO.class);
                extraDataJson.putAll(JSONUtil.parseObj(JSONUtil.toJsonStr(dto)));

                //{"airLineName":"一键起飞航线","compareFlyDate":"","comparePictureUrl":"","compareType":1,"currentFlyDate":"2025-03-14","currentPictureLat":31.69734,"currentPictureLng":118.5547,"currentPictureUrl":"https://pri-unit-oss-api.xiaoliangma.com/dock/1882955927490641921/2025-03/14/5ccd560e-9763-4d89-96b0-0d7af6069dcf.png","desc":"11","deviceId":1896810076655796225,"deviceName":"DJI Dock3","deviceSn":"8UUDMCS00ARWG3","eventName":"火焰","eventType":2,"extraData":"","flyTaskId":1900457718151843842,"flyTaskName":"8UUDMCS00ARWG3","flyTaskNumber":"YJQF-20250314-0003","generateBill":1,"happenedTime":"2025-03-14 16:03:41","originalDataLogo":"1900460220901068801","projectId":1882955927490641921,"tenantId":"1882955927490641921","userId":1882956968158113793}

                //{"eventPictureUrlList":[这里是把多个url集成一下放在这里],"eventPictureInfoList":[{"lat":30.213123,"lng":1203434343,"picUrl":"https://fk.com/a.png","picUrlList":[万一多个 就启用这个字段]}]}

                JSONObject eventPictureUrlListObj = JSONUtil.parseObj(flySystemEventDataSourceDTO.getExtraData());
                if (eventPictureUrlListObj.containsKey("eventPictureUrlList")) {

                    JSONArray newArr = new JSONArray();
                   JSONArray arr =  eventPictureUrlListObj.getJSONArray("eventPictureUrlList");
                    for (int i = 0; i < arr.size(); i++) {

                        newArr.add(transferPicFile(tenantId,arr.getStr(i)));

                    }
                    eventPictureUrlListObj.replace("eventPictureUrlList",newArr);

                    extraDataJson.putAll(eventPictureUrlListObj);

                }
            }

            data.setExtraData(JSONUtil.toJsonStr(extraDataJson));


            dataService.save(data);

            LupDataLabel dataLabel = new LupDataLabel();
            dataLabel.setProjectId(projectId);
            dataLabel.setLabelId(label.getId());
            dataLabel.setDataId(data.getId());
            dataLabelService.save(dataLabel);
        }

/**
 *
 * 原始数据已甄别 要求建立工单；
 *
 * 数据是否已经建立过工单  没有建立过工单的才需要建立
 *
 */
        if(generateBillEnum == GenerateBillEnum.GenerateBill &&
                (tmpGenerateBillEnum == null || tmpGenerateBillEnum == GenerateBillEnum.GenerateBillNothing)) {
// 是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）



            Result<LupBillVO> billResult = iLupBillService.addBill(data);

            //走流程    先去掉
            //translateData(data,billResult.getData(),label.getName());


            log.error("获取的租户数据： " + LmContextHolder.getTenantId());


//                try {
//                    PushKQDTO pushKQDTO =  readConfigUtil.readPushConfig();
//                    log.info("获取的租户pushKQDTO： " + JSON.toJSONString(pushKQDTO));
//                    log.info("获取的租户pushKQDTO1： " + JSON.toJSONString(pushKQDTO != null && pushKQDTO.getOpen() != null
//                            && pushKQDTO.getStageName() != null && pushKQDTO.getOpen()));
//                    if (pushKQDTO != null && pushKQDTO.getOpen() != null && pushKQDTO.getStageName() != null && pushKQDTO.getOpen()) {
//                        //是四平台才推送
//                        LupSubmitForm ee = new LupSubmitForm();
//                        ee.setProcessInstanceId(billResult.getData().getProcessInstanceId());
//                        lupFlowService.submitTaskByProcessId(ee);
//
//                    }
//                } catch (Exception exp) {
//
//                    exp.printStackTrace();
//                }

        }


        LupData tmpData = new LupData();
        tmpData.setId(data.getId());
        tmpData.setGenerateBill(flySystemEventDataSourceDTO.getGenerateBill());
        data.setGenerateBill(tmpData.getGenerateBill());
        dataService.updateById(tmpData);


        if(pushKQDTO.getAutoPush())
            pushEventDataToOtherServer(data.getId());

        return Result.data(data);
    }

    @Override
    public Result delFlyEventData(FlySystemDelDTO flySystemDelDTO) {

        Assert.notNull(flySystemDelDTO.getOriginalDataLogo(),LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneOriginalDataLogo,Arrays.asList(EventTipKey.NoneTip)));
        Assert.isTrue(!"".equals(flySystemDelDTO.getOriginalDataLogo()),LocaleMessageUtil.getMessageByKeyList(
                EventTipKey.EmptyOriginalLogo, Arrays.asList(EventTipKey.NoneTip)));

        ProjectUtil.getProjectId();

        if(flySystemDelDTO.getTenantId()!=null && !"".equals(flySystemDelDTO.getTenantId())){
            LmContextHolder.setTenantId(flySystemDelDTO.getTenantId());
        }

        if(flySystemDelDTO.getUserId()!=null && flySystemDelDTO.getUserId() > 0){
            LmContextHolder.setUserId(flySystemDelDTO.getUserId());
        }

        Long projectId = flySystemDelDTO.getProjectId();

        List<LupData> lupDataList = dataService.list(Wrappers.<LupData>lambdaQuery()
        .eq(LupData::getOriginalDataLogo,flySystemDelDTO.getOriginalDataLogo())
                .eq(LupData::getDataOrigin,flySystemDelDTO.getEventType())
                        .eq(LupData::getDataType,EventDataType.PictureDataType.getValue())
                .eq(LupData::getDeleted,0)
                .eq(LupData::getProjectId,projectId)
                .eq(LupData::getTenantId,LmContextHolder.getTenantId())
                .select(LupData::getId)
        );

        Assert.isTrue(lupDataList!=null && lupDataList.size()>0,LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneExistOriginalDataLogo,Arrays.asList(EventTipKey.NoneTip))+flySystemDelDTO.getOriginalDataLogo()+" ");
        lupDataList.forEach(lupData-> {
            lupData.setDeleted(1);
            dataService.updateById(lupData);

            List<LupBill> list = iLupBillService.list(Wrappers.<LupBill>lambdaQuery().eq(LupBill::getDataId, lupData.getId())
                    .eq(LupBill::getDeleted, 0).select(LupBill::getId));

            if (list.size() > 0) {
                list.forEach(e -> {
                    e.setDeleted(1);
                });
                iLupBillService.updateBatchById(list);
            }
        });
        return Result.success(LocaleMessageUtil.getMessage(EventTipKey.NoneDelOriginalDataLogo) +
                flySystemDelDTO.getOriginalDataLogo() +
                " ");
    }


    @Transactional
    @Override
    public Result postEventData(EventDataDTO eventData) {
        log.error("比对系统传输的数据： "+  JSON.toJSONString(eventData));
        LmContextHolder.setTenantId(eventData.getTenantId());
        LmContextHolder.setUserId(eventData.getUserId());

        List<LupData> list =  dataService.list(Wrappers.<LupData>lambdaQuery().eq(
                LupData::getDeleted,0
        )
                .eq(LupData::getOriginalDataLogo,eventData.getOriginalDataLogo())
                        .eq(LupData::getGenerateBill,GenerateBillEnum.GenerateBill.getValue())
        );

        if(list.size()>0 || readConfigUtil.existCommonCache(eventData.getOriginalDataLogo())){
            //code 90001 数据处理中，不能重复提交
            return  Result.fail(90001,"数据不能重复提交,请刷新页面");
        }

        readConfigUtil.putCommonCache(eventData.getOriginalDataLogo(),eventData);

        /**
         *
         * 1、判断事件 已经提交并且处理中 返回 失败 告知code 约定code展示 提示 不要重复提交
         *
         */

        messageSender.sendMessage(JSONUtil.toJsonStr(eventData));

        return Result.data(null);

    }


    @Override
    public void dealReceiveData(EventDataDTO eventData) {

        try {
            //异步线程池处理事件

            //锁定事件 不允许重复提交推送
            //结束后需要告知比对服务 事件推送处理的结果

            Long projectId = eventData.getProjectId();

            if (eventData.getTenantId() != null && !"".equals(eventData.getTenantId())) {
                LmContextHolder.setTenantId(eventData.getTenantId());
            }

            if (eventData.getUserId() != null && eventData.getUserId() > 0) {
                LmContextHolder.setUserId(eventData.getUserId());
            }

            List<LupData> list =  dataService.list(Wrappers.<LupData>lambdaQuery()
                    .eq(LupData::getDeleted,0)
                    .eq(LupData::getOriginalDataLogo,eventData.getOriginalDataLogo()));


            //数据
            LupData data = null;

            GenerateBillEnum generateBillEnum = GenerateBillEnum.parse(eventData.getGenerateBill());

            GenerateBillEnum tmpGenerateBillEnum = null;


            if(list.size()>0) {
                data = list.get(0);
                tmpGenerateBillEnum = GenerateBillEnum.parse(data.getGenerateBill());
            }else {
                data = new LupData();
            }

            if(list.size() == 0) {

                String tenantId = LmContextHolder.getTenantId();


                data.setProjectId(projectId);
                data.setDataOrigin(eventData.getEventType());

                EventDataType eventDataType = EventDataType.parse(eventData.getDataType());
//        if(eventDataType == EventDataType.HumanTileDataType) eventDataType = EventDataType.TileDataType;
                data.setDataType(eventDataType.getValue());

                data.setDataOrigin(DataOrigin.CompareFetch.getValue());

                data.setCompareType(EventCompareType.parse(eventData.getCompareType()).getValue());
                data.setOriginalDataLogo(eventData.getOriginalDataLogo());

                data.setHappenedTime(eventData.getHappenedTime());
                data.setFlyTaskId(eventData.getFlyTaskId());
                data.setFlyTaskNumber(eventData.getFlyTaskNumber());
                data.setFlyTaskName(eventData.getFlyTaskName());
                data.setAirLineName(eventData.getAirLineName());
                data.setDeviceId(eventData.getDeviceId());
                data.setDeviceName(eventData.getDeviceName());
                data.setDeviceSn(eventData.getDeviceSn());
                data.setEventDesc(eventData.getDesc());

                data.setLocateArea(eventData.getLocateArea());
                data.setLocationLng(eventData.getCurrentPictureLng());
                data.setLocationLat(eventData.getCurrentPictureLat());

                revertEventDataLocation(data);


                LupLabel label = new LupLabel();
                label.setProjectId(projectId);
                label.setName(eventData.getEventName());
                label = labelService.saveLabel(label);

                Boolean asyncProduceImage = false;
                MergePictureDTO mergePictureDTO = new MergePictureDTO();
                mergePictureDTO.setTenantId(LmContextHolder.getTenantId());
                mergePictureDTO.setUserId(LmContextHolder.getUserId());

                if (eventDataType == EventDataType.PictureDataType) {

                    //转存文件
                    eventData.setCurrentPictureUrl(transferPicFile(tenantId,eventData.getCurrentPictureUrl()));

                    mergePictureDTO.setSourceMode(PictureMergeSourceMode.PicMode);
                    List<LupSource> sourceList = null;

                    sourceList = new ArrayList<>();

                    LupSource currentSource = new LupSource();
                    currentSource.setProjectId(projectId);
                    currentSource.setUrl(eventData.getCurrentPictureUrl());
                    currentSource.setType(EventSourceType.PictureSourceType.getValue());
                    sourceList.add(currentSource);
                    //保存资源
                    data.setCurrentBatchId(saveBatchPictureData(sourceList));

                    data.setShape(JSONUtil.toJsonStr(eventData.getPixelPointList()));

                    //设置图片的形状的 x、y 点
                    mergePictureDTO.setPixelPointList(eventData.getPixelPointList());


                    mergePictureDTO.setCurrentPicUrl(eventData.getCurrentPictureUrl());

//            mergePictureDTO.setCurrentPicName(eventData.getAirLineName());//航线名称

                    DataPicDTO dataPicDTO = new DataPicDTO();

                    if (eventData.getCompareType() == EventCompareType.DoubleCompareType.getValue()) {
                        //转存文件
                        eventData.setComparePictureUrl(transferPicFile(tenantId,eventData.getComparePictureUrl()));

                        sourceList = new ArrayList<>();
                        Assert.notNull(eventData.getComparePictureUrl(), LocaleMessageUtil.getMessage(EventTipKey.NoneComparePictureUrl));
                        Assert.isTrue(!"".equals(eventData.getComparePictureUrl()), LocaleMessageUtil.getMessage(EventTipKey.NoneComparePictureUrl));

                        LupSource compareSource = new LupSource();
                        compareSource.setProjectId(projectId);
                        compareSource.setUrl(eventData.getComparePictureUrl());
                        compareSource.setType(EventSourceType.PictureSourceType.getValue());
                        sourceList.add(compareSource);

                        data.setCompareBatchId(saveBatchPictureData(sourceList));
                        mergePictureDTO.setIsCompareMode(true);
                        mergePictureDTO.setComparePicUrl(eventData.getComparePictureUrl());

                    } else {
                        dataPicDTO.setEventPictureUrl(eventData.getCurrentPictureUrl());
                        data.setExtraData(JSONUtil.toJsonStr(dataPicDTO));
                    }
                    asyncProduceImage = true;
                } else if (eventDataType == EventDataType.TileDataType) {
                    mergePictureDTO.setSourceMode(PictureMergeSourceMode.TileMode);
                    //瓦片数据  要判断下  是否自定义的
                    final List<LupSource> sourceList = new ArrayList<>();

//            LupSource currentSource = new LupSource();
//            currentSource.setProjectId(projectId);

                    if (eventData.getCurrentTile() == null) {

                        eventData.getCurrentAirlineTaskId().forEach(airlineDataDTO -> {


                            LupSource tmpCurrentSource = new LupSource();
                            tmpCurrentSource.setProjectId(projectId);
                            tmpCurrentSource.setFlyDate(airlineDataDTO.getFlyDate());
                            tmpCurrentSource.setSourceName(airlineDataDTO.getAirlineFlyName());
                            tmpCurrentSource.setType(EventSourceType.TileSourceType.getValue());

                            tmpCurrentSource.setOriginalSourceId(airlineDataDTO.getAirlineTaskId());


                            mergePictureDTO.setCurrentPicName(airlineDataDTO.getAirlineFlyName());
                            getUrlByAirlineTaskId(mergePictureDTO.getCurrentTileList(), airlineDataDTO.getAirlineTaskId());

                            sourceList.add(tmpCurrentSource);

                        });

                    } else {
                        eventData.getCurrentTile().forEach(tile -> {
                            LupSource tmpCurrentSource = new LupSource();
                            tmpCurrentSource.setProjectId(projectId);
                            tmpCurrentSource.setMinZoom(tile.getMinZoom());
                            tmpCurrentSource.setMaxZoom(tile.getMaxZoom());
                            tmpCurrentSource.setBounds(tile.getTileBounds());
                            tmpCurrentSource.setSourceName(tile.getTileName());
                            tmpCurrentSource.setFlyDate(tile.getFlyDate());
                            tmpCurrentSource.setType(EventSourceType.AutoDefineSourceType.getValue());
                            tmpCurrentSource.setUrl(tile.getTileUrl());

                            mergePictureDTO.setCurrentPicName(tile.getTileName());

                            MergePictureTileDTO tmp = new MergePictureTileDTO();
                            tmp.setUrl(tile.getTileUrl());
                            mergePictureDTO.getCurrentTileList().add(tmp);

                            sourceList.add(tmpCurrentSource);
                        });

                    }
                    //保存数据源 同时这是批次id
                    data.setCurrentBatchId(saveBatchPictureData(sourceList));


                    if (eventData.getCompareType() == EventCompareType.DoubleCompareType.getValue()) {
                        mergePictureDTO.setIsCompareMode(true);
                        mergePictureDTO.setSourceMode(PictureMergeSourceMode.TileMode);

                        final List<LupSource> compareSourceList = new ArrayList<>();


                        LupSource compareSource = new LupSource();
                        compareSource.setProjectId(projectId);
                        if (eventData.getCompareTile() == null) {
                            Assert.notNull(eventData.getCompareAirlineTaskId(), LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneCompareAirlineTaskId, Arrays.asList(EventTipKey.NoneTip)));
                            Assert.isTrue(!(eventData.getCompareAirlineTaskId().size() == 0), LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneCompareAirlineTaskId, Arrays.asList(EventTipKey.NoneTip)));

                            eventData.getCompareAirlineTaskId().forEach(airlineDataDTO -> {

                                LupSource tmpCurrentSource = new LupSource();
                                tmpCurrentSource.setProjectId(projectId);

                                tmpCurrentSource.setType(EventSourceType.TileSourceType.getValue());
                                tmpCurrentSource.setFlyDate(airlineDataDTO.getFlyDate());
                                tmpCurrentSource.setSourceName(airlineDataDTO.getAirlineFlyName());
                                tmpCurrentSource.setOriginalSourceId(airlineDataDTO.getAirlineTaskId());


                                mergePictureDTO.setComparePicName(airlineDataDTO.getAirlineFlyName());
                                getUrlByAirlineTaskId(mergePictureDTO.getCompareTileList(), airlineDataDTO.getAirlineTaskId());

                                compareSourceList.add(tmpCurrentSource);

                            });

                        } else {
                            Assert.notNull(eventData.getCompareTile(), LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneCompareTile, Arrays.asList(EventTipKey.NoneTip)));
                            Assert.isTrue(!"".equals(eventData.getCompareTile()), LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneCompareTile, Arrays.asList(EventTipKey.NoneTip)));

                            eventData.getCompareTile().forEach(tile -> {
                                LupSource tmpCurrentSource = new LupSource();
                                tmpCurrentSource.setProjectId(projectId);
                                tmpCurrentSource.setSourceName(tile.getTileName());
                                tmpCurrentSource.setFlyDate(tile.getFlyDate());
                                tmpCurrentSource.setType(EventSourceType.AutoDefineSourceType.getValue());

                                tmpCurrentSource.setUrl(tile.getTileUrl());
                                tmpCurrentSource.setMinZoom(tile.getMinZoom());
                                tmpCurrentSource.setMaxZoom(tile.getMaxZoom());
                                tmpCurrentSource.setBounds(tile.getTileBounds());

                                mergePictureDTO.setComparePicName(tile.getTileName());

                                MergePictureTileDTO tmp = new MergePictureTileDTO();
                                tmp.setUrl(tile.getTileUrl());
                                mergePictureDTO.getCompareTileList().add(tmp);

                                compareSourceList.add(tmpCurrentSource);
                            });
                        }


                        //保存数据源 同时这是批次id
                        data.setCompareBatchId(saveBatchPictureData(compareSourceList));


                    }

                    data.setShape(eventData.getShape());

                    //处理图片
                    asyncProduceImage = true;

                } else if (eventDataType == EventDataType.HumanTileDataType) {

                    mergePictureDTO.setSourceMode(PictureMergeSourceMode.TileMode);
                    //瓦片数据  要判断下  是否自定义的
                    final List<LupSource> sourceList = new ArrayList<>();

                    if (eventData.getCurrentTile() == null) {

                        eventData.getCurrentAirlineTaskId().forEach(airlineDataDTO -> {

                            LupSource tmpCurrentSource = new LupSource();
                            tmpCurrentSource.setProjectId(projectId);
                            tmpCurrentSource.setFlyDate(airlineDataDTO.getFlyDate());
                            tmpCurrentSource.setSourceName(airlineDataDTO.getAirlineFlyName());
                            tmpCurrentSource.setType(EventSourceType.TileSourceType.getValue());

                            tmpCurrentSource.setOriginalSourceId(airlineDataDTO.getAirlineTaskId());

                            mergePictureDTO.setCurrentPicName(airlineDataDTO.getAirlineFlyName());

                            getUrlByAirlineTaskId(mergePictureDTO.getCurrentTileList(), airlineDataDTO.getAirlineTaskId());


                            sourceList.add(tmpCurrentSource);

                        });

                    } else {
                        eventData.getCurrentTile().forEach(tile -> {
                            LupSource tmpCurrentSource = new LupSource();

                            if (tile.getSourceType() == ModuleDataSourceTypeEnum.CompareModuleSource.getValue()) {
                                tmpCurrentSource.setUrl(tile.getTileUrl());
                                MergePictureTileDTO tmp = new MergePictureTileDTO();
                                tmp.setUrl(tile.getTileUrl());
                                mergePictureDTO.getCurrentTileList().add(tmp);
                            } else {
                                CommonResult commonResult = surveyAchievementFeign.preview(tile.getOriginalTileSourceId());
                                log.info("commonResult:" + JSONUtil.toJsonStr(commonResult));
                                if (!"".equals(commonResult.getData())) {
                                    AchievementClientPreviewVO achievementPreviewVO = BeanUtil.toBean(commonResult.getData(), AchievementClientPreviewVO.class);


                                    MergePictureTileDTO tmp = new MergePictureTileDTO();
                                    tmp.setUrl(achievementPreviewVO.getPreviewUrl());
                                    tmp.setFlipFlag(achievementPreviewVO.getFlipFlag());
                                    tmp.setTransferStatus(achievementPreviewVO.getTransferStatus());
//                                tmpCurrentSource .setUrl(achievementPreviewVO.getPreviewUrl());
                                    mergePictureDTO.getCurrentTileList().add(tmp);
                                }
                            }

                            tmpCurrentSource.setOriginalTileSourceId(tile.getOriginalTileSourceId());


                            tmpCurrentSource.setProjectId(projectId);
                            tmpCurrentSource.setMinZoom(tile.getMinZoom());
                            tmpCurrentSource.setMaxZoom(tile.getMaxZoom());
                            tmpCurrentSource.setBounds(tile.getTileBounds());
                            tmpCurrentSource.setSourceName(tile.getTileName());
                            tmpCurrentSource.setFlyDate(tile.getFlyDate());
                            tmpCurrentSource.setType(EventSourceType.AutoDefineSourceType.getValue());

                            tmpCurrentSource.setSourceType(tile.getSourceType());


                            mergePictureDTO.setCurrentPicName(tile.getTileName());


                            sourceList.add(tmpCurrentSource);
                        });


                    }
                    //保存数据源 同时这是批次id
                    data.setCurrentBatchId(saveBatchPictureData(sourceList));


                    if (eventData.getCompareType() == EventCompareType.DoubleCompareType.getValue()) {

                        final List<LupSource> compareSourceList = new ArrayList<>();

                        LupSource compareSource = new LupSource();
                        compareSource.setProjectId(projectId);
                        if (eventData.getCompareTile() == null) {
                            Assert.notNull(eventData.getCompareAirlineTaskId(), LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneCompareAirlineTaskId, Arrays.asList(EventTipKey.NoneTip)));
                            Assert.isTrue(!(eventData.getCompareAirlineTaskId().size() == 0), LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneCompareAirlineTaskId, Arrays.asList(EventTipKey.NoneTip)));

                            eventData.getCompareAirlineTaskId().forEach(airlineDataDTO -> {

                                LupSource tmpCurrentSource = new LupSource();
                                tmpCurrentSource.setProjectId(projectId);

                                tmpCurrentSource.setType(EventSourceType.TileSourceType.getValue());
                                tmpCurrentSource.setFlyDate(airlineDataDTO.getFlyDate());
                                tmpCurrentSource.setSourceName(airlineDataDTO.getAirlineFlyName());
                                tmpCurrentSource.setOriginalSourceId(airlineDataDTO.getAirlineTaskId());

                                mergePictureDTO.setIsCompareMode(true);
                                mergePictureDTO.setSourceMode(PictureMergeSourceMode.TileMode);

                                mergePictureDTO.setComparePicName(airlineDataDTO.getAirlineFlyName());

                                getUrlByAirlineTaskId(mergePictureDTO.getCompareTileList(), airlineDataDTO.getAirlineTaskId());

                                compareSourceList.add(tmpCurrentSource);

                            });

                        } else {
                            Assert.notNull(eventData.getCompareTile(), "缺少字段compareTile ，当且仅当 compareType = 2 时");
                            Assert.isTrue(!"".equals(eventData.getCompareTile()), "缺少字段 compareTile ，当且仅当 compareType = 2 时");

                            eventData.getCompareTile().forEach(tile -> {
                                LupSource tmpCurrentSource = new LupSource();

                                if (tile.getSourceType() == ModuleDataSourceTypeEnum.CompareModuleSource.getValue()) {
                                    tmpCurrentSource.setUrl(tile.getTileUrl());

                                    MergePictureTileDTO tmp = new MergePictureTileDTO();
                                    tmp.setUrl(tile.getTileUrl());
                                    mergePictureDTO.getCompareTileList().add(tmp);
                                } else {
                                    CommonResult commonResult = surveyAchievementFeign.preview(tile.getOriginalTileSourceId());
                                    log.info("commonResult:" + JSONUtil.toJsonStr(commonResult));
                                    if (!"".equals(commonResult.getData())) {
                                        AchievementClientPreviewVO achievementPreviewVO = BeanUtil.toBean(commonResult.getData(), AchievementClientPreviewVO.class);

                                        MergePictureTileDTO tmp =new MergePictureTileDTO();
                                        tmp.setUrl(achievementPreviewVO.getPreviewUrl());
                                        tmp.setFlipFlag(achievementPreviewVO.getFlipFlag());
                                        tmp.setTransferStatus(achievementPreviewVO.getTransferStatus());

//                                    tmpCurrentSource .setUrl(achievementPreviewVO.getPreviewUrl());
                                        mergePictureDTO.getCompareTileList().add(tmp);
                                    }
                                }


                                tmpCurrentSource.setProjectId(projectId);
                                tmpCurrentSource.setSourceName(tile.getTileName());
                                tmpCurrentSource.setFlyDate(tile.getFlyDate());
                                tmpCurrentSource.setType(EventSourceType.AutoDefineSourceType.getValue());

                                tmpCurrentSource.setOriginalTileSourceId(tile.getOriginalTileSourceId());
                                tmpCurrentSource.setSourceType(tile.getSourceType());


                                tmpCurrentSource.setMinZoom(tile.getMinZoom());
                                tmpCurrentSource.setMaxZoom(tile.getMaxZoom());
                                tmpCurrentSource.setBounds(tile.getTileBounds());

                                mergePictureDTO.setIsCompareMode(true);

                                mergePictureDTO.setComparePicName(tile.getTileName());


                                compareSourceList.add(tmpCurrentSource);
                            });
                        }


                        //保存数据源 同时这是批次id
                        data.setCompareBatchId(saveBatchPictureData(compareSourceList));


                    }

                    data.setShape(eventData.getShape());

                    //处理图片
                    asyncProduceImage = true;

                }

                dataService.save(data);

                LupDataLabel dataLabel = new LupDataLabel();
                dataLabel.setProjectId(projectId);
                dataLabel.setLabelId(label.getId());
                dataLabel.setDataId(data.getId());
                dataLabelService.save(dataLabel);



                //处理图片
                if (asyncProduceImage) {
                    //处理并做更新
                    dealShapeData(dataService, data, mergePictureDTO);
                }
            }

            //判断是否要生成工单和推送工单
            if(generateBillEnum == GenerateBillEnum.GenerateBill &&
                    (tmpGenerateBillEnum == null || tmpGenerateBillEnum == GenerateBillEnum.GenerateBillNothing)) {
// 是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）
                Result<LupBillVO> billResult = iLupBillService.addBill(data);



                ReceiveDataDTO receiveDataDTO = new ReceiveDataDTO();
                receiveDataDTO.setTenantId(Long.parseLong(LmContextHolder.getTenantId()));
                receiveDataDTO.setUserId(LmContextHolder.getUserId());
                receiveDataDTO.setId(Long.parseLong(eventData.getOriginalDataLogo()));
                receiveDataDTO.setSuccess(true);
                dataPushProvider.receiveData(receiveDataDTO);

            }

            LupData tmpData = new LupData();
            tmpData.setId(data.getId());
            tmpData.setGenerateBill(eventData.getGenerateBill());
            dataService.updateById(tmpData);

            //推送数据给第三方
            HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();
            PushKQDTO pushKQDTO = handleModuleConfigVO.getPushKQDTO();
            if(pushKQDTO.getAutoPush())
                pushEventDataToOtherServer(data.getId());

        }catch (Exception exp){
            exp.printStackTrace();
            ReceiveDataDTO receiveDataDTO = new ReceiveDataDTO();
            receiveDataDTO.setTenantId(Long.parseLong(LmContextHolder.getTenantId()));
            receiveDataDTO.setUserId(LmContextHolder.getUserId());
            receiveDataDTO.setId(Long.parseLong(eventData.getOriginalDataLogo()));
            receiveDataDTO.setSuccess(false);
            dataPushProvider.receiveData(receiveDataDTO);
        }
        finally {

            readConfigUtil.delCommonCache(eventData.getOriginalDataLogo());
        }
    }

    /**
     * 按日期保存数据资源批次
     * @param sourceList
     * @return
     */
    private Long saveBatchPictureData(List<LupSource> sourceList,Long projectId){
        sourceService.saveBatchSource(sourceList);
        LupBatch batch = new LupBatch();
        batch.setBatchDate(DateUtil.format(new Date(),"yyyy-MM-dd"));
        batch.setProjectId(projectId);
        batch.setBatchName(batch.getBatchDate());
        iLupBatchService.save(batch);

        List<LupBatchSource> batchSourceList = new ArrayList<>();
        sourceList.forEach(item->{
            LupBatchSource batchSource = new LupBatchSource();
            batchSource.setBatchId(batch.getId());
            batchSource.setProjectId(projectId);
            batchSource.setSourceId(item.getId());
            batchSourceList.add(batchSource);
        });
        iLupBatchSourceService.saveBatch(batchSourceList);

        return batch.getId();
    }

    /**
     * 按日期保存数据资源批次
     * @param sourceList
     * @return
     */
    private Long saveBatchPictureData(List<LupSource> sourceList){

        Long projectId = ProjectUtil.getProjectId();

        return saveBatchPictureData(sourceList,projectId);
    }

//    @Async("syncProduceImageExecutorPool")
    public void dealShapeData(ILupDataService dataService, LupData data,MergePictureDTO mergePictureDTO){

//        syncProduceImageExecutorPool.execute(new Runnable() {
//            @Override
//            public void run() {
                try{
                    LmContextHolder.setTenantId(mergePictureDTO.getTenantId());
                    LmContextHolder.setUserId(mergePictureDTO.getUserId());

                    mergePictureDTO.setShape(data.getShape());

                    MultipartFile multipartFile = MergeImageUtil.createMultipartFile(mergePictureDTO);
                    Result<XlmFile> fileResult = fileProvider.upload(multipartFile);

                    DataPicDTO dataPicDTO = new DataPicDTO();
                    dataPicDTO.setEventPictureUrl(fileResult.getData().getPublicUrl());

                    data.setExtraData(JSONUtil.toJsonStr(dataPicDTO));
                    dataService.updateById(data);
//                    dataNotificationFeign.eventDataCountOnChanged();

                    //传输数据
//                    translateData(data,bill,label);
//                    LupSubmitForm ee = new LupSubmitForm();
//                    ee.setProcessInstanceId(bill.getProcessInstanceId());
//                    lupFlowService.submitTaskByProcessId(ee);



                }catch (Exception exp){
                    exp.printStackTrace();
                }
//            }
//        });

    }

    public void pushBillData(LupBillVO bill){
//        ConfigInVo config = new ConfigInVo();
//        config.setCode(VariableConstants.LupBillTranslateConfigCode);
//        config.setTenantId(Long.parseLong(LmContextHolder.getTenantId()));
//        Result<com.alibaba.fastjson.JSONObject>configValResult =  configValueProvider.getConfigByCode(config);

//        log.info("获取的租户configValResult： "+  JSON.toJSONString(configValResult));
//        if(configValResult.isSuccess() && configValResult.getData() != null)
        {
            try {

                PushKQDTO pushKQDTO =  readConfigUtil.readHandleModuleConfig().getPushKQDTO();

                log.info("获取的租户pushKQDTO： "+  JSON.toJSONString(pushKQDTO));
                log.info("获取的租户pushKQDTO1： "+  JSON.toJSONString(pushKQDTO != null && pushKQDTO.getOpen() != null
                        && pushKQDTO.getStageName() != null && pushKQDTO.getOpen()));
                if (pushKQDTO != null && pushKQDTO.getOpen() != null && pushKQDTO.getStageName() != null && pushKQDTO.getOpen()) {
                    //是四平台才推送
                    LupSubmitForm ee1 = new LupSubmitForm();
                    ee1.setProcessInstanceId(bill.getProcessInstanceId());
                    lupFlowService.submitTaskByProcessId(ee1);
                }
            }catch (Exception exp){

                exp.printStackTrace();
            }
        }
    }

    private void getUrlByAirlineTaskId(List<MergePictureTileDTO> urlList,Long airlineTaskId){
        Result<AchievementClientPreviewVO> previewVOResult = streamProvider.preview(airlineTaskId);

        MergePictureTileDTO tmp =new MergePictureTileDTO();
        tmp.setUrl(previewVOResult.getData().getPreviewUrl());
        tmp.setFlipFlag(previewVOResult.getData().getFlipFlag());
        tmp.setTransferStatus(previewVOResult.getData().getTransferStatus());
        urlList.add(tmp);

    }

    private void revertEventDataLocation(LupData data){

        try{
            String res = GeoLocation.locationToAddress(readConfigUtil.readPushConfig().getGeoLocationKey()
                    ,data.getLocationLng().toString()
                    ,data.getLocationLat().toString()).getBody();
            com.alibaba.fastjson.JSONObject resJsonObj = JSON.parseObject(res);
            //{"result":{"formatted_address":"浙江省绍兴市柯桥区杨汛桥街道延寿寺","location":{"lon":120.28543,"lat":30.10462},"addressComponent":{"address":"延寿寺","city":"绍兴市","county_code":"156330603","nation":"中国","poi_position":"西北","county":"柯桥区","city_code":"156330600","address_position":"西北","poi":"延寿寺","province_code":"156330000","province":"浙江省","road":"杨渔线","road_distance":70,"poi_distance":40,"address_distance":40}},"msg":"ok","status":"0"}
            data.setAddress(
                    resJsonObj.getJSONObject("result").getString("formatted_address")
//                    "杨汛桥街道的联社村内（测试数据）"

            );
        }catch (Exception exp){
            exp.printStackTrace();
        }
    }

    @Override
    public Result fixEventData(FixEventDataSourceDTO fixEventDataSourceDTO) {
        /**
         * SELECT * FROM lup_source WHERE original_source_id in ('1849257082998493185','1815586353093857282');
         * -- url / bound type =3
         *
         * SELECT * FROM lup_batch_source WHERE source_id in ('1854783642698645505','1854783641637486594');
         *
         * SELECT id,data_type FROM  lup_data WHERE current_batch_id in (
         * 1854783641700401153,
         * 1854783642744782849,
         * 1854783648411287554,
         * 1854783649325645826,
         * 1854783652840472577,
         * 1854783653624807425
         * ) or compare_batch_id in (
         * 1854783641700401153,
         * 1854783642744782849,
         * 1854783648411287554,
         * 1854783649325645826,
         * 1854783652840472577,
         * 1854783653624807425
         * );
         */

        //根据dataCenterFlyTaskPlanId 对应 original_source_id查找 lup_source 数据
        //补充url bounds 修改 type= 3

        List<LupSource> sourceList = sourceService.list(Wrappers.<LupSource>lambdaQuery()
                        .eq(LupSource::getDeleted,0)
                .eq(LupSource::getOriginalSourceId,fixEventDataSourceDTO.getDataCenterFlyTaskPlanId()));

        for (LupSource lupSource : sourceList) {

            lupSource.setUrl(fixEventDataSourceDTO.getUrl());
            lupSource.setBounds(fixEventDataSourceDTO.getBounds());
            lupSource.setType(3);
            sourceService.updateById(lupSource);

            List<LupBatchSource> batchSourceList = iLupBatchSourceService.list(Wrappers.<LupBatchSource>lambdaQuery().eq(LupBatchSource::getDeleted,0)
                    .eq(LupBatchSource::getSourceId,lupSource.getId()));
            batchSourceList.forEach(item->{
                List<LupData> dataList = dataService.list(Wrappers.<LupData>lambdaQuery()
                        .eq(LupData::getDeleted,0)
                        .eq(LupData::getCurrentBatchId,item.getBatchId()).or()
                        .eq(LupData::getCompareBatchId,item.getBatchId()));

                dataList.forEach(e->{
                    LupData lupData = new LupData();
                    lupData.setId(e.getId());
                    lupData.setDataType(5);
                    dataService.updateById(lupData);
                });

            });

        }


        //lup_batch_source 根据资源查询 批次

        //跟个批次 查询 事件数据 lup_data


        return null;
    }

    private String transferPicFile(String tenantId,String picUrl){

        if(ObjectUtil.isNotEmpty(picUrl)) {
            FileTransferReqVO fileTransferReqVO = new FileTransferReqVO();
            fileTransferReqVO.setSourcePath(picUrl);
            fileTransferReqVO.setTentantId(tenantId);
            fileTransferReqVO.setModule(VariableConstants.lupHandleSystemTag);
            Result<FileTransferResVO> fileTransferResVOResult = fileTransferProvider.fileTransfer(fileTransferReqVO);

            return fileTransferResVOResult.getData().getTargetPath();
        }

        return null;
    }
}
