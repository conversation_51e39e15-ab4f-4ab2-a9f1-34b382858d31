package com.mascj.lup.event.bill.service.impl;

import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.redis.core.RedisService;
import com.mascj.lup.datacenter.feign.LupExportTaskFeign;
import com.mascj.lup.datacenter.vo.LupExportTaskCallbackVo;
import com.mascj.lup.event.bill.dto.ExportDataBaseDTO;
import com.mascj.lup.event.bill.entity.DataReport;
import com.mascj.lup.event.bill.mapper.DataReportMapper;
import com.mascj.lup.event.bill.service.IDataTranslateService;
import com.mascj.lup.event.bill.util.FileUtils;
import com.mascj.lup.event.bill.vo.TableDataVO;
import com.mascj.support.file.api.feign.IFileProvider;
import com.mascj.support.file.api.model.XlmFile;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.SocketTimeoutException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @date 2024/11/13 16:10
 * @describe
 */
@Service
@Configuration
public class DataTranslateServiceImpl extends ServiceImpl<DataReportMapper, DataReport> implements IDataTranslateService {

    @Value("${liangma.datasource.bill.url}")
    private String DB_URL;

    @Value("${liangma.datasource.bill.username}")
    private String USER ;
    @Value("${liangma.datasource.bill.password}")
    private String PASS ;

//    private String csvPrefixPath = "/Users/<USER>/Desktop/SaaS脚本/";

//    private String moduleCode = "work-bill";

    @Autowired
    private IFileProvider fileProvider;

    @Autowired
    private RedisService redisService;

    @Autowired
    private LupExportTaskFeign exportTaskFeign;


    @Override
    public Result exportData(ExportDataBaseDTO exportDataBaseDTO) {

        String moduleCodeDBName=exportDataBaseDTO.getModuleCodeDBName();
        String moduleCode = exportDataBaseDTO.getModular();


        //建立一个临时目录
        Path tmpRootDir = null;

        try {



            List<TableDataVO> tableNames = baseMapper.getAllTableNames(moduleCodeDBName);
            if(tableNames.size()>0) {
                tmpRootDir = Files.createTempDirectory("tmp"+exportDataBaseDTO.getLupExportItemId());

//                FileUtils.clearEndsWithTmpFile(tmpRootDir.getParent().toString(),0);
                String fileRoot = moduleCode;
                //存放每一个合并单个文件的文件的目录 最终用于压缩
                Path zipDir =  tmpRootDir.resolve(fileRoot);

                //创建一个 存放zip文件的 dir的目录
                Files.createDirectory(zipDir);
                String p = zipDir.toString();
                System.out.println(p);
                String   csvPrefixPath = p+"/";
                System.out.println("csvPrefixPath："+csvPrefixPath);

                Connection conn = DriverManager.getConnection(DB_URL, USER, PASS);

                for (TableDataVO tb : tableNames) {
                    if ("flyway_schema_history".equals(tb.getTableName())) continue;
                    if ("v_bill_data".equals(tb.getTableName())) continue;
                    if ("DATABASECHANGELOG".equals(tb.getTableName())) continue;
                    if ("DATABASECHANGELOGLOCK".equals(tb.getTableName())) continue;
                    if ("flyer_task_data".equals(tb.getTableName())) continue;

                    if ("lup_bill_exchange_relation".equals(tb.getTableName())) continue;
                    if ("test-point".equals(tb.getTableName())) continue;

//                    xlm_biz_flyer.DATABASECHANGELOG
//xlm_biz_flyer.DATABASECHANGELOGLOCK
//                    flyer_task_data

                    System.out.println("TableDataVO:"+JSONUtil.toJsonStr(tb));
                    exportTableToCSV(conn,exportDataBaseDTO, moduleCodeDBName+"."+tb.getTableName(),csvPrefixPath);
                }

                conn.close();
                conn = null;

                zipDirectory(p,tmpRootDir.toString()+"/"+moduleCode+".zip");
//            writeToLocal(imgResRootDir+fileSaveDir+projectId.replace("/","")+".zip",in);
                FileInputStream f = new FileInputStream(tmpRootDir.toString()+"/"+moduleCode+".zip");
                //生成待上传文件
                MultipartFile file = FileUtils.getMultipartFile(f, moduleCode+".zip");

                Result<XlmFile> xlmFileResult = uploadFileInChunks(exportDataBaseDTO.getTenantId(),file,exportDataBaseDTO.getLupExportItemId());


                LupExportTaskCallbackVo exportTaskCallbackVo = new LupExportTaskCallbackVo();
                exportTaskCallbackVo.setTenantId(Long.parseLong(exportDataBaseDTO.getTenantId()));
                exportTaskCallbackVo.setCallbackId(exportDataBaseDTO.getLupExportItemId());
                if(xlmFileResult.isSuccess()){
                    System.out.println(xlmFileResult);



                    exportTaskCallbackVo.setFileId(xlmFileResult.getData().getId());
                    exportTaskCallbackVo.setStatus(2);//4
                    exportTaskCallbackVo.setPath(xlmFileResult.getData().getUrl());

                }else{
                    exportTaskCallbackVo.setStatus(4);//4
                }

                Result result = exportTaskFeign.callBack(exportTaskCallbackVo);
                if(result.isSuccess()){
                    System.out.println("【exportTaskFeign.callBack】回调成功");
                }


            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if(tmpRootDir!=null) {
                deleteDirectory(tmpRootDir.toString());
            }
        }

        return null;
    }

    public Result<XlmFile> uploadFileInChunks(String tenantId, MultipartFile file,Long reportId) throws IOException, InterruptedException {
        Result<XlmFile> fileResult = null;

        System.out.
                println(file.getSize());
        int chunkSize = 1024 * 1024; // 1MB 分块大小
        try (InputStream fileStream = file.getInputStream()) {
            byte[] buffer = new byte[chunkSize];
            int bytesRead;
            int chunkNumber = 1;

            double fileSize = file.getSize();
            double filePartNumber = fileSize / chunkSize;
            int totalChunks = (int) Math.ceil(filePartNumber);
            String fileName = file.getOriginalFilename();
            System.out.println("fileProvider.uploadChunk-请求开始"+fileSize);
            Boolean bf = true;
            while ((bytesRead = fileStream.read(buffer)) != -1 ) {
                try {
                    Thread.sleep(100);
                    InputStream chunkStream = new ByteArrayInputStream(Arrays.copyOfRange(buffer, 0, bytesRead));

                    //生成临时文件

                    MultipartFile fileChunkNumber = FileUtils.getMultipartFile(chunkStream, fileName);


                    System.out.println("fileProvider.uploadChunk-请求开始");
                    Result<XlmFile> fileResultTmp = fileProvider.uploadChunk(reportId,Long.parseLong(tenantId),fileChunkNumber, chunkNumber++, totalChunks, fileName);
                    System.out.println("fileProvider.uploadChunk-请求结束"+fileResultTmp);
                    if (fileResultTmp != null) {
                        fileResult = fileResultTmp;
                        bf = false;
                        if(fileResult.getData()!=null) {
                            String fileResultKey = "support:file:" + reportId;
                            redisService.del(fileResultKey);
                        }


                    }


                }catch (Exception exception){
                    System.out.println("fileProvider.uploadChunk-异常---");
                    //应该是文件合并导致的 请求等到超时异常
                    exception.printStackTrace();
                    System.out.println("fileProvider.uploadChunk-异常---");
                }
            }

            if(bf){
                //刷redis 看数据合并成功上传到minio得到结果了吗？
                String fileResultKey = "support:file:"+reportId;
                int waitSecond = 0;
                do{
                    System.out.println("-=====>>>>>服务等待了"+waitSecond+"秒<<<<<======-");
                    if(waitSecond > 0)
                        //等待1秒
                        Thread.sleep(1000);
                    waitSecond++;

                    if(waitSecond > 7200){
                        throw new SocketTimeoutException("请求超时");
                    }

                    if(redisService.hasKey(fileResultKey)) {
                        XlmFile xlmFile = JSONUtil.toBean(redisService.get(fileResultKey).toString(), XlmFile.class);
                        fileResult =Result.data(xlmFile);
                        bf = false;
                        redisService.del(fileResultKey);
                    }

                }while (bf);

            }

        } catch (Exception e) {
            // 处理异常
            throw e;
        }
        return fileResult;
    }


    private void exportTableToCSV(Connection conn,ExportDataBaseDTO exportDataBaseDTO, String tableName,String csvPrefixPath) throws SQLException, IOException {
//        SELECT * FROM flyway_schema_history where tenant_id = '1778002821514211330'

        String query = "SELECT * FROM " + tableName +" where tenant_id = '" +
                LmContextHolder.getTenantId() +
                "' and date(create_time) between '" +
                exportDataBaseDTO.getStartTime() +
                "' and '" +
                exportDataBaseDTO.getEndTime() +
                "'";

        if(
                tableName.contains("map_project_tenant")
        ||
        tableName.contains("map_webodm_relation")
        ){
            query = "SELECT * FROM " + tableName +" where tenant_id = '" +
                    LmContextHolder.getTenantId()+"' ";
        }
        if(tableName.contains("lup_grid_unit")){
            query= "SELECT `lup_grid_unit`.`id` AS `id`,`lup_grid_unit`.`name` AS `name`,`lup_grid_unit`.`code` AS `code`,`lup_grid_unit`.`pid` AS `pid`,`lup_grid_unit`.`hierarchy` AS `hierarchy`,ST_ASGEOJSON (`lup_grid_unit`.`polygon`) AS `polygon`,ST_ASGEOJSON (`lup_grid_unit`.`centriod`) AS `centriod`,`lup_grid_unit`.`geometry_str` AS `geometry_str`,`lup_grid_unit`.`centriod_str` AS `centriod_str`,`lup_grid_unit`.`grid_area` AS `grid_area`,`lup_grid_unit`.`description` AS `description`,`lup_grid_unit`.`status` AS `status`,`lup_grid_unit`.`sort` AS `sort`,`lup_grid_unit`.`create_time` AS `create_time`,`lup_grid_unit`.`create_by` AS `create_by`,`lup_grid_unit`.`update_time` AS `update_time`,`lup_grid_unit`.`update_by` AS `update_by`,`lup_grid_unit`.`is_deleted` AS `is_deleted`,`lup_grid_unit`.`tenant_id` AS `tenant_id`,`lup_grid_unit`.`project_id` AS `project_id` FROM `lup_grid_unit` WHERE (`lup_grid_unit`.`tenant_id`=" +
                    LmContextHolder.getTenantId() +
                    ")";
        }

        if(tableName.contains("flyer_geometry_info")){
            query= "SELECT   `id` AS `id`,  `name` AS `name`,  `address` AS `address`,  `city_code` AS `city_code`,  `city_name` AS `city_name`,  `district_name` AS `district_name`,  `distirct_code` AS `distirct_code`,ST_ASGEOJSON (  `geometry`) AS `geometry`,  `create_by` AS `create_by`,  `update_by` AS `update_by`,  `create_time` AS `create_time`,  `update_time` AS `update_time`,  `is_deleted` AS `is_deleted`,  `tenant_id` AS `tenant_id`,  `user_id` AS `user_id` FROM " +
                    tableName +
                    " WHERE tenant_id='" +
                    LmContextHolder.getTenantId() +
                    "'" ;
        }


        String [] clist;
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(query);

//             FileWriter writer = new FileWriter(csvPrefixPath+tableName + ".csv");
             Writer writer = new OutputStreamWriter(new FileOutputStream("" +
                     csvPrefixPath+tableName  +
                     ".csv"), "UTF-8");
             CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(clist = getColumnNames(rs)))) {

            while (rs.next()) {
                List<String> row = new ArrayList<>();
                for (int i = 1; i <= rs.getMetaData().getColumnCount(); i++) {
                    row.add(rs.getString(i));
                }
                csvPrinter.printRecord(row);
            }
            csvPrinter.flush();
        }catch (Exception ex){
            System.out.println(tableName);
            ex.printStackTrace();
        }
    }

    private static String[] getColumnNames(ResultSet rs) throws SQLException {
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();
        String[] columnNames = new String[columnCount];
        for (int i = 1; i <= columnCount; i++) {
            columnNames[i - 1] = metaData.getColumnName(i);
        }
        return columnNames;
    }

    public static void zipDirectory(String dirPath, String zipFilePath) throws IOException {
        Path outputZipFile = Paths.get(zipFilePath);
        Path sourceDir = Paths.get(dirPath);

        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(outputZipFile.toFile()))) {
            Files.walk(sourceDir)
                    .filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        ZipEntry zipEntry = new ZipEntry(sourceDir.relativize(path).toString());
                        try {
                            zos.putNextEntry(zipEntry);
                            Files.copy(path, zos);
                            zos.closeEntry();
                        } catch (IOException e) {
                            System.err.println("无法压缩文件: " + path);
                            e.printStackTrace();
                        }
                    });
        }
    }

    public static void deleteDirectory(String directoryPath) {
        Path directory = Paths.get(directoryPath);
        if (Files.exists(directory)) {
            try {
                // 递归删除目录及其所有内容
                Files.walk(directory)
                        .sorted(Comparator.reverseOrder())
                        .map(Path::toFile)
                        .forEach(File::delete);
            } catch (IOException e) {
                System.err.println("删除目录时发生错误: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }


}
