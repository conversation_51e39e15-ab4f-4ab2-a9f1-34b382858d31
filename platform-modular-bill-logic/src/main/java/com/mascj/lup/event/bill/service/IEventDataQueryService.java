package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/14 09:34
 * @describe
 */
public interface IEventDataQueryService {
    List<EventDataLabelVO> labelList();

    List<EventDataBaseInfoVO> listAllEventData();

    Page<EventDataTaskVO> pageByTask(Page<EventDataTaskVO> page, EventDataTaskQueryDTO eventDataTaskQueryDTO);

    IPage<EventDataVO> page(IPage<EventDataVO> page, EventDataQueryDTO eventDataQueryDTO);
    Page<EventDataVO> pageForLand(Page<EventDataVO> page, EventDataForLandQueryDTO eventDataQueryDTO);
    EventDataVO detail(Long billId);

    List<CountEventDataVO> countByArea();

    List<CountEventDataVO> countByLabel();

    List<CountEventDataVO> countByLabelOnDay();


    CountEventDataOriginGroupVO countByOrigin(EventDataCountQueryDTO eventDataCountQueryDTO);
    CountEventDataDealResultVO countByDealResult(EventDataCountQueryDTO eventDataCountQueryDTO);

    FlyTaskReportVO queryFlyTaskReportVO(FlyTaskReportDTO flyTaskReportDTO);

    List<BillQueryCountVO> billQueryCount(BillQueryCountDTO billQueryCountDTO);
    List<BillQueryListItemVO> billQueryList(BillQueryCountDTO billQueryCountDTO);
}
