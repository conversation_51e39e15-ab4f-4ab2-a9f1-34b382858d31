package com.mascj.lup.event.bill.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.lup.event.bill.entity.LupBillPending;
import com.mascj.lup.event.bill.entity.LupBillPendingFile;
import com.mascj.lup.event.bill.mapper.LupBillPendingFileMapper;
import com.mascj.lup.event.bill.mapper.LupBillPendingMapper;
import com.mascj.lup.event.bill.service.ILupBillPendingFileService;
import com.mascj.lup.event.bill.service.ILupBillPendingService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 事件资源记录表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Service
@Slf4j
@AllArgsConstructor
public class LupBillPendingFileServiceImpl extends ServiceImpl<LupBillPendingFileMapper, LupBillPendingFile> implements ILupBillPendingFileService {

}
