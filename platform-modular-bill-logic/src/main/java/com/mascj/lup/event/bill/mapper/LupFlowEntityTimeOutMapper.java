package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mascj.lup.event.bill.entity.LupFlowEntity;
import com.mascj.lup.event.bill.entity.LupFlowEntityTimeOut;

import java.util.List;

/**
 * <p>
 * 流程记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface LupFlowEntityTimeOutMapper extends BaseMapper<LupFlowEntityTimeOut> {

}
