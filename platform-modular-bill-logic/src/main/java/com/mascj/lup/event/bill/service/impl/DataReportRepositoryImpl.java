package com.mascj.lup.event.bill.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.exception.ServiceException;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.common.util.StringUtil;
import com.mascj.kernel.database.util.PageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.dto.ExportFileDTO;
import com.mascj.lup.event.bill.dto.ReportParamDTO;
import com.mascj.lup.event.bill.dto.ReportSearchDTO;
import com.mascj.lup.event.bill.entity.DataReport;
import com.mascj.lup.event.bill.entity.DataReportTemplate;
import com.mascj.lup.event.bill.mapper.DataReportMapper;

import com.mascj.lup.event.bill.service.IDataReportRepository;
import com.mascj.lup.event.bill.service.IDataReportTemplateRepository;
import com.mascj.lup.event.bill.vo.DataReportDetailsVO;
import com.mascj.lup.event.bill.vo.DataReportSetVO;
import com.mascj.lup.event.bill.vo.DataReportVO;
import com.mascj.platform.system.entity.App;
import com.mascj.platform.system.entity.Org;
import com.mascj.platform.system.feign.ISysAppProvider;
import com.mascj.platform.system.feign.ISysOrgProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

@Repository
public class DataReportRepositoryImpl extends ServiceImpl<DataReportMapper, DataReport> implements IDataReportRepository {

    @Autowired
    private ISysAppProvider sysAppProvider;
    @Autowired
    private ISysOrgProvider sysOrgProvider;
    @Autowired
    private IDataReportTemplateRepository aepReportTemplateRepository;

    @Override
    public IPage<DataReportVO> listPage(ReportSearchDTO search) {
        String timeValue = search.getTimeValue();
        if (StringUtil.isNotBlank(timeValue)) {
            search.setBeginTime(timeValue + " " + "00:00:00");
            search.setEndTime(timeValue + " " + "23:59:59");
        }
        return baseMapper.listPage(PageUtil.getPage(search), search);
    }

    @Override
    public Boolean saveReport(ExportFileDTO exportFileDto) {

        String appName = "";
        DataReport aepReport = new DataReport();
        aepReport.setAppName(appName);
        aepReport.setAppCode(exportFileDto.getBizCode());
        aepReport.setCurrentLine(0);
        aepReport.setTotalLine(exportFileDto.getDataTotal());
        aepReport.setFileName(exportFileDto.getFileName());
        aepReport.setTemplateId(exportFileDto.getTemplateId());

        DataReportTemplate aepReportTemplate = aepReportTemplateRepository.getById(exportFileDto.getTemplateId());
        if (aepReportTemplate == null) {
            throw new ServiceException(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneReportTemplate,Arrays.asList(EventTipKey.NoneTip)));
        }
        baseMapper.insert(aepReport);

        return true;
    }

    @Override
    public Boolean setReport(DataReportSetVO aepReportSetVO) {
        String id = aepReportSetVO.getId();
        String appId = aepReportSetVO.getTypeId();
        String batchId = aepReportSetVO.getBatchId();
        String orgId = aepReportSetVO.getOrgId();
        String batchCode = aepReportSetVO.getBatchCode();
        String appName = "";
        String appCode = "";
        String orgCode = "";
        String bizCode = "";
        Result<App> result = sysAppProvider.get(appId);
        if (result.getCode() == 200) {
            App data = result.getData();
            if (data != null) {
                appName = data.getName();
                appCode = data.getCode();
            }
        }
        if (StringUtil.isBlank(appCode)) {
            throw new ServiceException(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneApp,Arrays.asList(EventTipKey.NoneTip)));
        }
//        String codes = aepReportSetVO.getCodes();
//        List<String> codeList = JSONArray.parseArray(codes, String.class);
        Result<List<Org>> orgListRs = sysOrgProvider.getOrgList(orgId);
        if (orgListRs.getCode() == 200) {
            List<Org> data = orgListRs.getData();
            if (data.size() > 0) {
                Org org = data.get(0);
                orgCode = org.getCode();
            }
        }
        if (StringUtil.isBlank(orgCode)) {
            throw new ServiceException(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneOrg,Arrays.asList(EventTipKey.NoneTip)));
        }
        String fileName = aepReportSetVO.getFileName();
        String templateId = aepReportSetVO.getTemplateId();
        DataReport aepReport = new DataReport();
        aepReport.setAppName(appName);
        aepReport.setAppCode(appCode);
        aepReport.setAppId(Long.parseLong(appId));
        aepReport.setCurrentLine(0);
        aepReport.setTotalLine(0);
        aepReport.setFileName(fileName);
        aepReport.setTemplateId(templateId);
        aepReport.setBatchCode(batchCode);

        DataReportTemplate aepReportTemplate = aepReportTemplateRepository.getById(templateId);
        if (aepReportTemplate == null) {
            throw new ServiceException(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneReportTemplate,Arrays.asList(EventTipKey.NoneTip)));
        }

//        aepReport.setSearchCondition(searchParam);
        if (StringUtil.isNotBlank(id)) {
            aepReport.setId(Long.parseLong(id));
        }
        baseMapper.insert(aepReport);
        Long newId = aepReport.getId();
        System.err.println("newId"+newId);
        //更新查询参数
        ReportParamDTO reportParamDTO = new ReportParamDTO();
        reportParamDTO.setAppCode(appCode);
        reportParamDTO.setAppId(Long.parseLong(appId));
        reportParamDTO.setOrgCode(orgCode);
        reportParamDTO.setFileName(fileName);

        reportParamDTO.setTemplateId(templateId);
        reportParamDTO.setBatchCode(batchCode);
        reportParamDTO.setBatchId(batchId);
        reportParamDTO.setReportType(aepReportTemplate.getReportTypeTag());
        reportParamDTO.setOrgId(orgId);
        reportParamDTO.setReportId(newId+"");
        reportParamDTO.setBizCode(bizCode);
        String searchParam = JSON.toJSONString(reportParamDTO);

        baseMapper.updateSearchParam(newId+"",searchParam);
        return true;
    }

    @Override
    public DataReportDetailsVO getDetails(String id) {
        QueryWrapper<DataReport> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        DataReport aepReport = baseMapper.selectById(id);
        if (aepReport == null) {
            throw new ServiceException(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneQuery, Arrays.asList(EventTipKey.NoneTip)));
        }
        String searchCondition = aepReport.getSearchCondition();
        if (StringUtil.isBlank(searchCondition)) {
            throw new ServiceException(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneQuery, Arrays.asList(EventTipKey.NoneTip)));
        }
        DataReportDetailsVO aepReportDetailsVO = JSON.
                parseObject(searchCondition, DataReportDetailsVO.class);
        aepReportDetailsVO.setId(id);
        return aepReportDetailsVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delReport(List<Long> ids) {
        ids.forEach(id -> {
            this.removeById(id);
        });
        return Boolean.TRUE;
    }

    /**
     * 生成进度更新方法
     */
    @Override
    public Boolean updateProgress(DataReport aepReport) {
        if (aepReport == null){
            throw new ServiceException(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneTranslateObj, Arrays.asList(EventTipKey.NoneTip)));
        }
        UpdateWrapper<DataReport> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id",aepReport.getId());
        baseMapper.update(aepReport,updateWrapper);
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateStatus(Long id,Integer status) {
        DataReport aepReport = new DataReport();
        aepReport.setState(status);
        UpdateWrapper<DataReport> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id",id);
        baseMapper.update(aepReport,updateWrapper);
        return Boolean.TRUE;

    }


}
