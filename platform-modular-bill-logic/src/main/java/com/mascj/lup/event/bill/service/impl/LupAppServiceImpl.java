package com.mascj.lup.event.bill.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.lup.event.bill.dto.LupAppAuthCodeDTO;
import com.mascj.lup.event.bill.dto.LupAppDTO;
import com.mascj.lup.event.bill.dto.LupAppOperateDTO;
import com.mascj.lup.event.bill.dto.LupAppSignDTO;
import com.mascj.lup.event.bill.entity.LupApp;
import com.mascj.lup.event.bill.entity.LupBillPending;
import com.mascj.lup.event.bill.mapper.LupAppMapper;
import com.mascj.lup.event.bill.mapper.LupBillPendingMapper;
import com.mascj.lup.event.bill.service.ILupAppService;
import com.mascj.lup.event.bill.service.ILupBillPendingService;
import com.mascj.lup.event.bill.util.AppKeyGenerator;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13 16:26
 * @describe
 */
@Service
@Slf4j
@AllArgsConstructor
public class LupAppServiceImpl extends ServiceImpl<LupAppMapper, LupApp> implements ILupAppService {

    @Override
    public LupApp saveAppInfo(LupAppDTO appDTO) {

        List<LupApp> appList = list(Wrappers.<LupApp>lambdaQuery().eq(LupApp::getName,appDTO.getName()));

        Assert.isTrue(appList.size() == 0,"应用名称已重复请慎重考虑");

        LupApp app = BeanUtil.toBean(appDTO,LupApp.class);

        app.setAppKey(AppKeyGenerator.generateAppKey());
        app.setAppSecret(AppKeyGenerator.generateAppSecret());
        app.setAuthCode(AppKeyGenerator.generateAppAuthCode());

        save(app);

        return app;
    }

    @Override
    public LupApp detailApp(LupAppOperateDTO appOperateDTO) {

        LupApp app = getOne(Wrappers.<LupApp>lambdaQuery()
                .eq(LupApp::getDeleted,0).eq(ObjectUtil.isNotEmpty(appOperateDTO.getName()),LupApp::getName,appOperateDTO.getName())

                                .eq(ObjectUtil.isNotEmpty(appOperateDTO.getAuthCode()),LupApp::getAuthCode,appOperateDTO.getAuthCode())
        );

        return app;
    }

    @Override
    public Result<LupApp> activeAppAuthCode(LupAppAuthCodeDTO authCodeDTO) {

        LmContextHolder.setTenantId("-1");

        Result res = null;
        LupApp app = getOne(Wrappers.<LupApp>lambdaQuery().eq(LupApp::getAuthCode,authCodeDTO.getAuthCode()));

        if(app == null) {
            res = Result.fail("应用不存在");
            return res;
        }

//        if(app.getActive() == 1 ){
//            res = Result.fail("授权码已经激活，不能重复使用");
//            return res;
//        }
        LupApp updateApp = new LupApp();
        updateApp.setId(app.getId());
        updateApp.setActive(1);
        updateApp.setActiveCount(app.getActiveCount()+1);
        updateApp.setAppKey(app.getAppKey());
        updateApp.setAppSecret(app.getAppSecret());
        //激活授权码
        updateById(updateApp);

        return Result.data(updateApp);
    }

    @Override
    public Result validApp(LupAppSignDTO appSignDTO) {

        LmContextHolder.setTenantId("-1");
        Result res = null;
        LupApp app = getOne(Wrappers.<LupApp>lambdaQuery().eq(LupApp::getAppKey,appSignDTO.getAppKey()));

        if(app == null) {
            res = Result.fail("应用不存在");
            return res;
        }

        if(app.getActive() == 0){
            res = Result.fail("应用未激活");
            return res;
        }

        boolean isExpired = false;
        if(ObjectUtil.isNotEmpty(app.getValidDateTime())) {
            // 解析日期时间字符串为DateTime对象
            DateTime dateTime1 = DateUtil.parseDateTime(app.getValidDateTime());
            // 比较两个日期时间
            isExpired = dateTime1.isBefore(DateUtil.date());
        }

        if(ObjectUtil.isNotEmpty(app.getValidDateTime())
                && isExpired){
            res = Result.fail("授权时间到期");
            return res;
        }
        String localSign = DigestUtils.md5Hex(app.getAppKey() + app.getAppSecret()).toUpperCase();

        res = Result.condition(localSign.equals(appSignDTO.getSign()));
        return res;
    }

    @Override
    public String createSign(String appKey) {

        LupApp app = getOne(Wrappers.<LupApp>lambdaQuery().eq(LupApp::getAppKey,appKey));

        Assert.notNull(app,"应用不存在");

        boolean isExpired = true;
        if(ObjectUtil.isNotEmpty(app.getValidDateTime())) {
            // 解析日期时间字符串为DateTime对象
            DateTime dateTime1 = DateUtil.parseDateTime(app.getValidDateTime());
            // 比较两个日期时间
            isExpired = dateTime1.isBefore(DateUtil.date());
        }

        Assert.isTrue(ObjectUtil.isEmpty(app.getValidDateTime())
                || !isExpired
                ,"授权时间到期");

        String localSign = DigestUtils.md5Hex(app.getAppKey() + app.getAppSecret()).toUpperCase();

        return localSign;
    }
}
