package com.mascj.lup.event.bill.service;

import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.FlowEntityTimeOutSaveDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2024/10/15 14:57
 * @describe
 */
@Api(value = "流程时间限制",tags = "流程时间限制")

public interface IFlowEntityTimeOutService {

    @ApiOperation(value = "保存流程时间限制数据")
    Result saveFlowEntityTimeOut(@RequestBody FlowEntityTimeOutSaveDTO timeOutSaveDTO);

}
