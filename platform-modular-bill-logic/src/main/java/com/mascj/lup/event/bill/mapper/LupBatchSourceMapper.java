package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mascj.lup.event.bill.entity.LupBatch;
import com.mascj.lup.event.bill.entity.LupBatchSource;
import com.mascj.lup.event.bill.entity.LupSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 事件数据标签关联记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface LupBatchSourceMapper extends BaseMapper<LupBatchSource> {

    List<LupSource> listByBatch(@Param("batchId") Long batchId);
}
