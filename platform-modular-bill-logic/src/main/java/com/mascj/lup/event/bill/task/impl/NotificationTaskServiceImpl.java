package com.mascj.lup.event.bill.task.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.CollectionUtil;
import com.mascj.kernel.tools.RedisKey;
import com.mascj.kernel.tools.RedisPlane;
import com.mascj.kernel.tools.linq.Linqs;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.LupMsgTemplateDTO;
import com.mascj.lup.event.bill.entity.LupBillNotification;
import com.mascj.lup.event.bill.entity.LupGridUnit;
import com.mascj.lup.event.bill.entity.LupGridUser;
import com.mascj.lup.event.bill.service.ILupBillNotificationService;
import com.mascj.lup.event.bill.service.ILupBillService;
import com.mascj.lup.event.bill.service.ILupGridUserService;
import com.mascj.lup.event.bill.task.INotificationTaskService;
import com.mascj.lup.event.bill.util.ReadConfigUtil;
import com.mascj.lup.event.bill.vo.LupBillCountByFlowStateVo;
import com.mascj.lup.event.bill.vo.config.*;
import com.mascj.platform.micro.entity.SysTenant;
import com.mascj.platform.system.dto.RoleInfo;
import com.mascj.platform.system.dto.UserRoleInfo;
import com.mascj.platform.system.entity.Tenant;
import com.mascj.platform.system.feign.ISysTenantProvider;
import com.mascj.platform.system.feign.ISysUserProvider;
import com.mascj.support.config.feign.IConfigValueProvider;
import com.mascj.support.config.vo.ConfigInVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/16 19:48
 * @describe
 */
@Service
@Component
@Slf4j
public class NotificationTaskServiceImpl implements INotificationTaskService {

    @Autowired
    private RedisPlane redisPlane;

    @Autowired
    private DistributedLock distributedLock;

    @Autowired
    private ISysTenantProvider sysTenantProvider;

    @Autowired
    @Qualifier("syncProduceImageExecutorPool")  //指定某个bean
    private Executor syncProduceImageExecutorPool;

    @Autowired
    private ReadConfigUtil readConfigUtil;

    @Autowired
    private ILupGridUserService gridUserService;

    @Autowired
    private ISysUserProvider userProvider;

    @Autowired
    private ILupBillService billService;

    @Autowired
    private RabbitTemplate rabbitTemplate;


    @Autowired
    private ILupBillNotificationService billNotificationService;

    private final static String channel ="lup-msg-passageway-server.lupChannel";


//    @Scheduled(cron = "* * * * * *")//
    @Scheduled(cron = "0 * * * * *")//
    public void executeTask() {

        String lockKeyExecuteTask = "lup-event-bill-server:task:notification:sms:lock-executeTask";

        distributedLock.lock(lockKeyExecuteTask, 15, TimeUnit.MINUTES);


        try {
            String currentTime = DateUtil.format(DateUtil.date(), "HH:mm");

            List<Tenant> tenantList = readTenantData();

            tenantList.forEach(tenant -> {

                syncProduceImageExecutorPool.execute(new Runnable() {
                    @Override
                    public void run() {

//

                        String lockKey = "";
                        try {
                            Thread.sleep(1000);
                            lockKey = "lup-event-bill-server:task:notification:sms:lock-" + tenant.getId();
                            if (distributedLock.lock(lockKey, 10, TimeUnit.MINUTES)) {


                                //设定租户数据
                                LmContextHolder.setTenantId(tenant.getId().toString());

                                dealSmsNotification(currentTime);


                            } else {
                                System.out.println("Task is already being executed by another instance.");
                            }

                        } catch (Exception exp) {
                            exp.printStackTrace();
                        } finally {
                            distributedLock.unlock(lockKey);

                        }
                    }
                });

            });
        }catch (Exception exp){exp.printStackTrace();}
        finally {

            distributedLock.unlock(lockKeyExecuteTask);
        }
    }

    private List<Tenant> readTenantData(){

            RedisKey redisKey = RedisKey.forService(VariableConstants.lupSystemTenant, ReadConfigUtil.class);

            return redisPlane.getOrCreateList(redisKey,()->{
                List<Tenant> tenantList = sysTenantProvider.getTenantList();
                return tenantList;
            },Tenant.class);

    }

    private void dealSmsNotification(String currentTime) {
        /**
         *
         *
         * 1、查询事件规则
         *
         * 查询 需要执行的时间点  在指定的时间点。 继续执行短信
         *
         *
         * 2、查询 流程节点 和流程节点关联配置
         *
         * 3、查询网格人员 按照流程节点 对应权限 查询 每个节点应该手短信的人员
         *
         * 4、
         *
         * 查询网格关联的人员
         *
         */


        //1、指定的时间节点 执行发送短信的业务逻辑
        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();

        if(!handleModuleConfigVO.getEventSmsSwitchKey()){
//            System.out.println("配置关闭 不扫描数据");
            return;
        }

        if(handleModuleConfigVO.getEventSmsTimeListKey().contains(currentTime)){


            System.out.println("Task executed at: " + new java.util.Date());

            Map<Integer,HmcFlowActivityVO> hmcFlowActivityVOMap = handleModuleConfigVO.getHmcFlowActivityVOMap();

            if(hmcFlowActivityVOMap!=null) {
                System.out.println("任务时间已经到达需要执行业务逻辑代码");

                //2、找到网格配置的关联人
                List<LupGridUser> gridUserList = gridUserService.list();
                Map<Long, List<LupGridUser>> gridUserMap = gridUserList.stream().collect(Collectors.groupingBy(LupGridUser::getUserId));


                Map<Integer,List<Long>> userSmsCount = new HashMap<>();

                gridUserMap.keySet().forEach(userId -> {

                    Result<UserRoleInfo> userRoleInfoResult = userProvider.getUserRoleById(userId);
                    List<String> roleList = new ArrayList<>();
                    if(userRoleInfoResult.isSuccess() && userRoleInfoResult.getData()!=null && userRoleInfoResult.getData().getRoles()!=null) {
                         roleList = Linqs.of(userRoleInfoResult.getData().getRoles()).select(RoleInfo::getCode);
                    }





                    Result<List<String>> permissionListResult = userProvider.getPermissionByRequest(userId);

                    List<String> permissionList= new ArrayList<>();
                    if(permissionListResult.isSuccess() && permissionListResult.getData()!=null){
                        permissionList =  permissionListResult.getData();
                    }
                    List<LupGridUser> gridUserList1 = gridUserMap.get(userId);

                    List<Long> gridIdList = gridUserList1.stream().map(LupGridUser::getGridUnitId).collect(Collectors.toList());


                    //判断 用户 有的权限和角色 是否在 配置中 在的话  记录 flowState
                    //根据网格和flowsState 查询数据count
                    //发送待处理的事件短信

                    List<Integer> flowStateList = new ArrayList<>();

                    for (Integer configFlowState : hmcFlowActivityVOMap.keySet()) {

                        HmcFlowActivityVO hmcFlowActivityVO = hmcFlowActivityVOMap.get(configFlowState);

                        // 找出两个列表的交集
                        List<String> intersection = roleList.stream()
                                .filter(hmcFlowActivityVO.getRoleCode()::contains)
                                .collect(Collectors.toList());

                        if(permissionList.contains(hmcFlowActivityVO.getPermissionCode()) || intersection.size()>0){
                            //加入流程条件
                            flowStateList.add(configFlowState);
                        }

                    }

                    //3、根据流程阶段条件 用户网格的条件 查询事件数量

                    if(gridIdList.size() > 0 && flowStateList.size() > 0) {
                        List<LupBillCountByFlowStateVo> billCountByFlowStateVoList = billService.countBillByFlowState(userId,gridIdList, flowStateList);

                        //4、根据模板发送通知
                        if (billCountByFlowStateVoList.size() > 0) {


                            Integer totalBillCount = billCountByFlowStateVoList.stream().mapToInt(LupBillCountByFlowStateVo::getBillCount).sum();

                            if(userSmsCount.containsKey(totalBillCount)){
                                List userIdList = userSmsCount.get(totalBillCount);
                                if(userIdList!=null && !userIdList.contains(userId)){
                                    userIdList.add(userId);
                                }
                            }else{
                                userSmsCount.put(totalBillCount,new ArrayList<>());
                                userSmsCount.get(totalBillCount).add(userId);
                            }

                            System.out.println("totalBillCount:" + totalBillCount);



                        }

                    }

                });

                userSmsCount.keySet().forEach(totalBillCount->{
                    try{
                        Map map = new HashMap();
                        map.put("eventNum", totalBillCount);//事件数量
                        //发送消息
                        sendSms(userSmsCount.get(totalBillCount), handleModuleConfigVO.getEventNotificationForDealSourceTypeDictCode(),handleModuleConfigVO.getEventNotificationForDealDictCode(),map);
                    }catch (Exception e1){
                        e1.printStackTrace();
                    }
                });

                //
                List<LupBillNotification> billNotificationList = billNotificationService.list(Wrappers.<LupBillNotification>lambdaQuery()
                        .eq(LupBillNotification::getStatus,0)
                        .eq(LupBillNotification::getDeleted,0)
                        .eq(LupBillNotification::getTimerSms,1));

                if(billNotificationList.size()>0){


                    Map<Long,List<LupBillNotification>> billLupBillNotificationMap = billNotificationList.stream().collect(Collectors.groupingBy(LupBillNotification::getUserId));


                    Map<Integer,List<Long>> smsUserMap= new HashMap<>();
                    billLupBillNotificationMap.keySet().forEach(userId->{

                        List<LupBillNotification> listData = billLupBillNotificationMap.get(userId);

                        List<Long> list = new ArrayList<>();
                        listData.forEach(item ->{
                            if(!list.contains(item.getBillId())){
                                list.add(item.getBillId());
                            }
                        });

                         Integer size = list.size();
                        List<Long> userIdList = new ArrayList<>();
                         if(smsUserMap.containsKey(size)){
                             userIdList = smsUserMap.get(size);

                         }else{
                             userIdList = new ArrayList<>();
                             smsUserMap.put(size,userIdList);
                         }
                        if(!userIdList.contains(userId)){
                            userIdList.add(userId);
                        }

                    });

                    smsUserMap.keySet().forEach(totalBillCount->{

                        try{
//                            尊敬的用户您好，处置系统有${eventNum}个事件处理结果被驳回，请及时处理！
                            Map map = new HashMap();
                            map.put("eventNum", totalBillCount);//事件数量
                            //发送消息
                            sendSms(smsUserMap.get(totalBillCount), handleModuleConfigVO.getEventNotificationForDealSourceTypeDictCode(),handleModuleConfigVO.getEventNotificationApprovalRejectKey().getEventNotificationFroRejectDictCodeTimer(), map);
                        }catch (Exception e1){
                            e1.printStackTrace();
                        }

                    });


                    billNotificationList.forEach(e->{
                        e.setStatus(1);

                    });

                    billNotificationService.updateBatchById(billNotificationList);

                }
            }

            // 执行任务结束
            System.out.println("Task executed end at: " + new java.util.Date());
        }
    }

    @Override
    public void sendSms(List<Long> userIdList,Integer sourceType,String code,Map map) {
        try{
            LupMsgTemplateDTO msgTemplateDTO = new LupMsgTemplateDTO();

            msgTemplateDTO.setUserIds(userIdList);
            msgTemplateDTO.setType(code);
            msgTemplateDTO.setSourceType(sourceType);
            msgTemplateDTO.setTenantId(LmContextHolder.getTenantId());

            //发送消息
            msgTemplateDTO.setParams(map);

            log.error("发生数据： "+ JSON.toJSONString(msgTemplateDTO));
            log.error("发生数据channel： "+ channel);
            rabbitTemplate.convertAndSend(channel,msgTemplateDTO);
        }catch (Exception e1){
            e1.printStackTrace();
        }
    }
}
