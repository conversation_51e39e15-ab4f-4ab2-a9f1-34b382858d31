package com.mascj.lup.event.bill.service.impl.flow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.$;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.common.util.StringUtil;
import com.mascj.kernel.common.util.beans.BeanUtil;
import com.mascj.kernel.tools.LogPrinter;
import com.mascj.kernel.tools.RedisKey;
import com.mascj.kernel.tools.SetFunction;
import com.mascj.kernel.tools.linq.Linqs;
import com.mascj.lup.event.bill.constant.Constants;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.LupVariableConstants;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupFlowEntity;
import com.mascj.lup.event.bill.exceptions.OperationFailedException;
import com.mascj.lup.event.bill.feign.IXlmCusTaskProvider;
import com.mascj.lup.event.bill.feign.vo.XlmSubmitTaskByInstanceCusDto;
import com.mascj.lup.event.bill.mapper.LupBillMapper;
import com.mascj.lup.event.bill.mapper.LupFlowEntityMapper;
import com.mascj.lup.event.bill.service.ILupFlowService;
import com.mascj.lup.event.bill.support.LupExecutionContext;
import com.mascj.lup.event.bill.util.RedisOpsUtils;
import com.mascj.lup.event.bill.vo.LupSubmitForm;
import com.mascj.lup.event.bill.vo.LupUserInfo;
import com.mascj.platform.system.dto.RoleInfo;
import com.mascj.platform.system.dto.UserInfo;
import com.mascj.platform.system.dto.UserRoleInfo;
import com.mascj.platform.system.entity.User;
import com.mascj.platform.system.feign.ISysTenantProvider;
import com.mascj.platform.system.feign.ISysUserProvider;
import com.mascj.support.config.dto.XlmBizCategory;
import com.mascj.support.config.dto.XlmVariableInfo;
import com.mascj.support.config.feign.IAepConfigProvider;
import com.mascj.support.workflow.dto.XlmEndProcessDto;
import com.mascj.support.workflow.dto.XlmQueryHisTaskDto;
import com.mascj.support.workflow.dto.XlmStartProcessDto;
import com.mascj.support.workflow.dto.XlmSubmitTaskDto;
import com.mascj.support.workflow.entity.XlmProcessInstance;
import com.mascj.support.workflow.entity.XlmTaskInfo;
import com.mascj.support.workflow.enums.HandleTaskEnum;
import com.mascj.support.workflow.feign.IXlmInstanceProvider;
import com.mascj.support.workflow.feign.IXlmQueryProvider;
import com.mascj.support.workflow.feign.IXlmTaskProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LupFlowServiceImpl implements ILupFlowService {

    @Autowired
    private IAepConfigProvider aepConfigProvider;
    @Autowired
    private IXlmInstanceProvider instanceProvider;
    @Autowired
    private ISysUserProvider userProvider;
    @Autowired
    private ISysTenantProvider systemProvider;
    @Autowired
    private LupExecutionContext lupExecutionContext;
    @Autowired
    private LupFlowEntityMapper lupFlowEntityMapper;
    @Autowired
    private IXlmQueryProvider queryProvider;
    @Autowired
    private IXlmTaskProvider taskProvider;
    @Autowired
    private IXlmCusTaskProvider cusTaskProvider;
    @Autowired
    private LupBillMapper lupBillMapper;


    @Override
    public void endTask(LupSubmitForm submitTaskDto) {
        Long flowId = submitTaskDto.getFlowId();
        LupFlowEntity flowEntity = null;
        if (flowId != null){
            flowEntity = lupFlowEntityMapper.selectById(submitTaskDto.getFlowId());
        }
        XlmEndProcessDto dto = new XlmEndProcessDto();
        String processInstanceId = flowEntity.getProcessInstanceId();
        if (StringUtil.isBlank(processInstanceId)){
            throw new RuntimeException("流程未启动成功");
        }
        dto.setInstanceId(processInstanceId);
//        dto.setComment("无");
        dto.setHandleUserId(LmContextHolder.getUserId());
        instanceProvider.end(dto);

        //修改表里数据

        LupFlowEntity ee = new LupFlowEntity();
        ee.setId(flowId);
        ee.setFourSuccess(2);
        lupFlowEntityMapper.updateById(ee);
    }

    @Override
    public Result<String> startupAepFlow(Long billId, Map<String, Object> formData) {
        String key = Constants.LOCAL_PROJECT_NAME + ":startupAepFlow:" + billId;
        Boolean lockLua = RedisOpsUtils.getLockLua(key, 60L);
        try {
            Assert.isTrue(lockLua, LocaleMessageUtil.getMessage(EventTipKey.FlowStarting));
            if (lockLua) {
                XlmBizCategory bizCategory = aepConfigProvider.getBizList().getData()
                        .stream()
                        .filter(x -> Constants.HANDLE_TYPE.equals(x.getCode()))
                        .findFirst()
                        .orElse(null);

                Assert.isTrue($.isNotNull(bizCategory) && $.isNotBlank(bizCategory.getProcessDefinitionId()), LocaleMessageUtil.getMessage(EventTipKey.FailFetchBizCategory));
                String processDefinitionId = bizCategory.getProcessDefinitionId();


                /*****************************创建流程************************************/

//                flow.setProcessDefinitionId(processDefinitionId);

                if ($.isNotEmpty(formData)) {
                    appendUserInfo(formData);
                }

                LupFlowEntity flow = new LupFlowEntity();
                flow.setProcessDefinitionId(processDefinitionId);
                flow.setBizCode(bizCategory.getCode());
                lupFlowEntityMapper.insert(flow);

                LupBill bill = new LupBill();
                bill.setId(billId);
                bill.setFlowEntityId(flow.getId());
//                bill.setDataId(1691288551860191233L);
                lupBillMapper.updateById(bill);

                XlmStartProcessDto startProcessDto = SetFunction
                        .lambdaSet(XlmStartProcessDto::getHandleUserId, LmContextHolder.getUserId())
                        .set(XlmStartProcessDto::getProcessDefinitionId, processDefinitionId)
                        .set(XlmStartProcessDto::getVariables, lupExecutionContext.computeGlobalVariables(flow, Collections.emptyMap()))
                        .set($.isNotEmpty(formData), XlmStartProcessDto::getFormData, formData)
                        .getInstance();

                Result<XlmProcessInstance> instanceResult = instanceProvider.start(startProcessDto);
                if (instanceResult.isSuccess()) {
                    XlmProcessInstance processInstance = instanceResult.getData();

                    flow.setProcessInstanceId(processInstance.getProcessInstanceId());
                    flow.setProcessDefinitionId(processInstance.getProcessDefinitionId());

                    int i = lupFlowEntityMapper.updateById(flow);
                    if (i == 0) {
                        throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailUpdate));
                    }
                    LogPrinter.start(LocaleMessageUtil.getMessage(EventTipKey.FlowStarting))
                            .appendLine("flowId: {}", flow.getId())
                            .appendLine("instanceId: {}", processInstance.getProcessInstanceId())
                            .appendLine("definitionId: {}", processInstance.getProcessDefinitionId())
                            .appendLine("tenantId: {}", LmContextHolder.getTenantId())
                            .info();
                    return Result.data(processInstance.getProcessInstanceId());
                } else {
                    throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailStartFlow), flow.getId(), instanceResult.getMsg());
                }
            }
        } finally {
            if (lockLua) {
                RedisOpsUtils.unLockLua(key);
            }
        }
        return Result.fail("");
    }

    @Override
    public void submitTaskByProcessId(LupSubmitForm submitTaskDto) {
        @Valid XlmSubmitTaskByInstanceCusDto submitDto = new XlmSubmitTaskByInstanceCusDto();
        Map<String, Object> formData = submitTaskDto.getFormData();
        if(formData == null){formData = new HashMap<>();}


        appendUserInfoNull(formData , submitDto);
        submitDto.setHandleUserId(LmContextHolder.getUserId());
        submitDto.setProcessInstanceId(submitTaskDto.getProcessInstanceId());
        submitDto.setFormData(formData);
        submitDto.setComment(submitTaskDto.getComment());
        Result<HandleTaskEnum> result = cusTaskProvider.submitInId(submitDto);

        if (!result.isSuccess()) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailCommitATask),
                    submitDto.getProcessInstanceId(),
                    result.getMsg());
        }

        HandleTaskEnum taskEnum = result.getData();
        if (!taskEnum.equals(HandleTaskEnum.SUCCESS)) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailExpCommitTask),
                    submitDto.getProcessInstanceId(),
                    taskEnum.getMessage());
        }
    }

    public void submitTask(LupSubmitForm submitTaskDto) {

        Assert.notNull(submitTaskDto, LocaleMessageUtil.getMessage(EventTipKey.NoneTaskSubmit));
        Long flowId = submitTaskDto.getFlowId();
        LupFlowEntity flowEntity = null;
        if (flowId != null){
            flowEntity = lupFlowEntityMapper.selectById(submitTaskDto.getFlowId());
        }else {
            Long workBillId = submitTaskDto.getWorkBillId();
            LupBill lupBill = lupBillMapper.selectById(workBillId);
            flowEntity = lupFlowEntityMapper.selectById(lupBill.getFlowEntityId());
        }
        Assert.hasText(submitTaskDto.getTaskId(), LocaleMessageUtil.getMessage(EventTipKey.NoneTaskId));
        Assert.notNull(submitTaskDto.getFormData(), LocaleMessageUtil.getMessage(EventTipKey.NoneFormData));



        /*******************************参数校验********************************************/
        Assert.notNull(flowEntity, LocaleMessageUtil.getMessage(EventTipKey.NoneFlowId) + submitTaskDto.getFlowId());

        Assert.isTrue(submitTaskDto.getTaskId().equals(flowEntity.getProcessTaskId()),
                $.format("当前工单的TaskId与提交的taskId不一致：id={}, taskId={}", flowEntity.getId(), submitTaskDto.getTaskId()));

        XlmQueryHisTaskDto queryTaskDto = SetFunction
                .lambdaSet(XlmQueryHisTaskDto::getTaskId, submitTaskDto.getTaskId())
                .getInstance();

        XlmTaskInfo taskInfo = queryProvider.queryHisTask(queryTaskDto).getData().stream().findFirst().orElse(null);

        if ($.isNull(taskInfo)) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.NoneFlow), submitTaskDto.getTaskId());
        }

        if ($.isNotNull(taskInfo.getEndTime())) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.NoneTaskCommitRepeat), submitTaskDto.getTaskId());
        }

        /*****************************提取表单变量*****************************************/

        Map<String, Object> newFormData = extractVariable(flowEntity, submitTaskDto.getFormData());

        submitTaskDto.setFormData(newFormData);

        /*****************************更新工单*****************************************/

        if (updateFormData2Bill(flowEntity, submitTaskDto.getFormData())) {
            // 获取flowEntity的最新属性
            flowEntity = lupFlowEntityMapper.selectById(flowEntity.getId());
//            Assert.notNull(flowEntity, "不存在的FlowEntity， Id=" + submitTaskDto.getFlowId());
        }

        /*****************************任务提交*****************************************/

        if ($.isNotEmpty(submitTaskDto.getFormData())) {
            appendUserInfo(submitTaskDto.getFormData());
        }

        LupUserInfo userInfo = getCurrentUserInfo();

        XlmSubmitTaskDto submitDto = SetFunction
                .lambdaSet(XlmSubmitTaskDto::getTaskId, submitTaskDto.getTaskId())
                .set(XlmSubmitTaskDto::getComment, submitTaskDto.getComment())
                .set(XlmSubmitTaskDto::getHandleUserId, userInfo.getUserId())
                .set(XlmSubmitTaskDto::getHandleUserName, userInfo.getUserName())
                .set(XlmSubmitTaskDto::getFormData, submitTaskDto.getFormData())
                .set(XlmSubmitTaskDto::getVariables, lupExecutionContext.computeGlobalVariables(flowEntity, submitTaskDto.getFormData()))
                .getInstance();

        Result<HandleTaskEnum> result = taskProvider.submit(submitDto);

        if (!result.isSuccess()) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailCommitATask),
                    submitDto.getTaskId(),
                    result.getMsg());
        }

        HandleTaskEnum taskEnum = result.getData();
        if (!taskEnum.equals(HandleTaskEnum.SUCCESS)) {
            throw OperationFailedException.format(
                    LocaleMessageUtil.getMessage(EventTipKey.FailExpCommitTask),
                    submitDto.getTaskId(),
                    taskEnum.getMessage());
        }

    }

    public long getCurrentUserId() {
        return LmContextHolder.getUserId();
    }

    public LupUserInfo getCurrentUserInfo() {
        Long userId = LmContextHolder.getUserId();
        Assert.isTrue(userId > 0, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneUser,Arrays.asList(EventTipKey.NoneTip)));
        Result<User> userById = userProvider.getUserById(userId);
        User data = userById.getData();
        if ($.isNull(data)  ) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneUser,Arrays.asList(EventTipKey.NoneTip)));
        }

        return SetFunction
                .lambdaSet(LupUserInfo::getUserId, userId)
                .set(LupUserInfo::getUserName, data.getName())
                .getInstance();
    }


    /**
     * 添加用户的身份信息
     *
     * @param formData
     */
    private void appendUserInfoNull(Map<String, Object> formData, XlmSubmitTaskByInstanceCusDto submitDto) {

        Long userId =  LmContextHolder.getUserId();
        if (userId == null){
            return;
        }

        User data = userProvider.getUserById(userId).getData();
        if ($.isNull(data)   ) {
            log.error(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneUser,Arrays.asList(EventTipKey.NoneTip)));
            return;
            //throw OperationFailedException.format("未获取到用户的身份信息");
        }

        formData.put(LupVariableConstants.handler_user_name, data .getName());
        submitDto.setHandleUserName(data .getName());
        formData.put(LupVariableConstants.handler_user_phone, data.getPhone());
    }


    /**
     * 添加用户的身份信息
     *
     * @param formData
     */
    private void appendUserInfo(Map<String, Object> formData) {
        Assert.notEmpty(formData, LocaleMessageUtil.getMessage(EventTipKey.NoneFormData));

        long userId =  LmContextHolder.getUserId();
//        if (formData.containsKey(VariableConstants.handler_user_id) && $.isNotNull(formData.get(VariableConstants.handler_user_id))) {
//            try {
//                userId = Long.parseLong(String.valueOf(formData.get(VariableConstants.handler_user_id)));
//            } catch (Exception e) {
//                throw OperationFailedException.format("userId反解析失败，userId=" + formData.get(VariableConstants.handler_user_id));
//            }
//        }
        UserRoleInfo userRoleInfo = userProvider.getUserRoleById(userId).getData();
        if ($.isNull(userRoleInfo) || $.isNull(userRoleInfo.getUser())) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneUser,Arrays.asList(EventTipKey.NoneTip)));
        }

        if ($.isEmpty(userRoleInfo.getRoles())) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneRole,Arrays.asList(EventTipKey.NoneTip)));
        }

        formData.put(LupVariableConstants.handler_user_name, userRoleInfo.getUser().getName());

        formData.put(LupVariableConstants.handler_user_phone, userRoleInfo.getUser().getPhone());
        formData.put(LupVariableConstants.handler_user_role,  $.join(Linqs.of(userRoleInfo.getRoles()).select(RoleInfo::getName)));
    }


    /**
     * 变量提取， jsonPath
     * <p>
     * 解决前端不好处理的数据
     *
     * @param formData
     * @return
     */
    private Map<String, Object> extractVariable(LupFlowEntity flowEntity, Map<String, Object> formData) {
        List<XlmVariableInfo> variables = lupExecutionContext.getVariableInfo(flowEntity.getBizCode(), flowEntity.getProcessDefinitionId())
                .values()
                .stream()
                .filter(x -> $.isNotBlank(x.getExtraction()))
                .collect(Collectors.toList());

        if ($.isNotEmpty(variables)) {
            final String jsonContent = JSON.toJSONString(formData);

            for (XlmVariableInfo variable : variables) {
                try {
                    Object variableValue = JSONPath.read(jsonContent, variable.getExtraction());

                    if ($.isNotNull(variableValue)) {
                        formData.put(variable.getName(), variableValue);
                    }
                } catch (Exception ex) {
                    LogPrinter.start("extractVariable Failed")
                            .appendLine("variableName: {}", variable.getName())
                            .appendLine("extraction: {}", variable.getExtraction())
                            .appendLine("exception: {}", ex.getMessage())
                            .warn();
                }
            }
        }


        return formData;
    }

    /**
     * 更新表单数据到工单
     *
     * @param flowEntity
     * @param formData
     * @return 数据库是否变更
     */
    private boolean updateFormData2Bill(LupFlowEntity flowEntity, Map<String, Object> formData) {

        List<XlmVariableInfo> variables = lupExecutionContext.getVariableInfo(flowEntity.getBizCode(), flowEntity.getProcessDefinitionId())
                .values()
                .stream()
                .filter(XlmVariableInfo::isUpdateToBill)
                .collect(Collectors.toList());

        boolean databaseChanged = false;

        if ($.isNotEmpty(variables)) {

            Map<String, Object> variableMap = new HashMap<>();
            for (XlmVariableInfo item : variables) {
                if (formData.containsKey(item.getName())) {
                    Object itemResult = formData.get(item.getName());
                    if ($.isNotNull(itemResult)) {
                        variableMap.put(item.getName(), itemResult);
                    }
                }
            }


            if ($.isNotEmpty(variableMap)) {
                List<String> variableNames = variables.stream().map(XlmVariableInfo::getName).collect(Collectors.toList());

                try {
                     saveFlowField(flowEntity, variableNames, variableMap);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw OperationFailedException.format("updateField失败，message={}", e.getMessage());
                }

                databaseChanged = true;
            }

        }
        return databaseChanged;
    }



    /**
     * 更新FlowEntity的特定Field
     *
     * @param flow
     * @param variables 需更新的field列表
     * @param values    对应field的值
     * @return
     */
    public Boolean saveFlowField(LupFlowEntity flow, List<String> variables, Map<String, Object> values) {
        Assert.notNull(flow, LocaleMessageUtil.getMessage(EventTipKey.NoneFlowTipKey));//"请指定参数flow");
        Assert.notEmpty(values, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneFlowVal,Arrays.asList(EventTipKey.NoneTip)));
        Assert.notEmpty(variables, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneFlowVariable,Arrays.asList(EventTipKey.NoneTip)));

        LupBill workBill = lupBillMapper.selectOne(new LambdaQueryWrapper<>(new LupBill())
                .eq(LupBill::getFlowEntityId, flow.getId()));
        Assert.notNull(workBill, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneQuery, Arrays.asList(EventTipKey.NoneTip)));

        // 获取最新的version
        LupFlowEntity flowEntity = lupFlowEntityMapper.selectById(flow.getId());
        Assert.notNull(flowEntity, LocaleMessageUtil.getMessage(EventTipKey.NoneFlowId) + flow.getId());

        Set<String> flowKeys = BeanUtil.toMap(flowEntity).keySet();
        Set<String> workBillKeys = BeanUtil.toMap(workBill).keySet();

        boolean updateFlowEntity = false;
        boolean updateWorkBill = false;
        for (String variableName : variables) {
            if (values.containsKey(variableName)) {
                if (flowKeys.contains(variableName)) {
                    updateFlowEntity = true;
                    BeanUtil.setProperty(flowEntity, variableName, values.get(variableName));
                } else if (workBillKeys.contains(variableName)) {
                    updateWorkBill = true;
                    BeanUtil.setProperty(workBill, variableName, values.get(variableName));
                }
            }
        }

        if (updateFlowEntity &&  lupFlowEntityMapper.updateById(flowEntity) == 0) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailFlowUpdate), flow.getId());
        }
        if (updateWorkBill &&  lupBillMapper.updateById(workBill) == 0) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailBillUpdate), workBill.getId());
        }

        return true;
    }


}
