package com.mascj.lup.event.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.mascj.kernel.common.util.StringUtil;
import com.mascj.kernel.database.util.PageUtil;
import com.mascj.lup.event.bill.dto.DataReportTemplateSetDTO;
import com.mascj.lup.event.bill.dto.ReportTemplateSearchDTO;
import com.mascj.lup.event.bill.entity.DataReportTemplate;
import com.mascj.lup.event.bill.mapper.DataReportTemplateMapper;
import com.mascj.lup.event.bill.service.IDataReportTemplateRepository;
import com.mascj.lup.event.bill.vo.DataReportTemplateVO;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class DataReportTemplateRepositoryImpl extends ServiceImpl<DataReportTemplateMapper, DataReportTemplate> implements IDataReportTemplateRepository {

    @Override
    public IPage<DataReportTemplateVO> listPage(ReportTemplateSearchDTO search) {
        return baseMapper.listPage(PageUtil.getPage(search), search);
    }

    @Override
    public Boolean setReportTemplate(DataReportTemplateSetDTO aepReportTemplateSetVO) {
        String fileTemplateUrl = aepReportTemplateSetVO.getFileTemplateUrl();
        String fileTemplateName = aepReportTemplateSetVO.getFileTemplateName();
        String reportTypeTag = aepReportTemplateSetVO.getReportTypeTag();
        String orgId = aepReportTemplateSetVO.getOrgId();
        String id = aepReportTemplateSetVO.getId();
        DataReportTemplate aepReportTemplate = new DataReportTemplate();
        aepReportTemplate.setFileTemplateUrl(fileTemplateUrl);
        aepReportTemplate.setFileTemplateName(fileTemplateName);
        if (StringUtil.isNotBlank(orgId)) {
            aepReportTemplate.setOrgId(Long.parseLong(orgId));
        } else {
            aepReportTemplate.setOrgId(0L);
        }
        aepReportTemplate.setReportTypeTag(reportTypeTag);
        if (StringUtil.isNotBlank(id)) {
            aepReportTemplate.setId(Long.parseLong(id));
        }
        this.saveOrUpdate(aepReportTemplate);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delTemplate(List<Long> ids) {
        ids.forEach(id -> {
            this.removeById(id);
        });
        return Boolean.TRUE;
    }

    @Override
    public List<Map<String, String>> getTemplateList(String orgId) {
        QueryWrapper<DataReportTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);
        queryWrapper.in("org_id", orgId,0);
        List<Map<String, String>> list = new ArrayList<>();
        List<DataReportTemplate> aepReportTemplates = baseMapper.selectList(queryWrapper);
        for (DataReportTemplate aepReportTemplate : aepReportTemplates) {
            String fileTemplateName = aepReportTemplate.getFileTemplateName();
            String orgId1 = aepReportTemplate.getOrgId()+"";
            String id = aepReportTemplate.getId() + "";
            Map<String, String> map = new HashMap<>();
            map.put("id", id);
            map.put("name", fileTemplateName);
            map.put("orgId", orgId1);
            list.add(map);
        }
        return list;
    }
}
