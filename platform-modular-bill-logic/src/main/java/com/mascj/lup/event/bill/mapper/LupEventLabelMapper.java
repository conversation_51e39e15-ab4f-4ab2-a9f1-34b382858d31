package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lup.event.bill.entity.LupEventLabel;
import com.mascj.lup.event.bill.vo.LupCommonTreeVO;
import com.mascj.lup.event.bill.vo.LupEventLabelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 *
 * <AUTHOR> @since 2024-01-05 14:36:06
 *
 */
@Mapper
public interface LupEventLabelMapper extends BaseMapper<LupEventLabel> {

    IPage<LupEventLabelVo> listPage(@Param("codeValue") Integer codeValue, @Param("page") Page<Object> page);


    List<LupCommonTreeVO> listLabelTree();

}