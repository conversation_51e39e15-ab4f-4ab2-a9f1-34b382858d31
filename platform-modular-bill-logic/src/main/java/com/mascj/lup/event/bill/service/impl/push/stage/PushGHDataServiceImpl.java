package com.mascj.lup.event.bill.service.impl.push.stage;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.entity.LupDataPushLog;
import com.mascj.lup.event.bill.entity.LupLabel;
import com.mascj.lup.event.bill.enums.DataPushStateEnum;
import com.mascj.lup.event.bill.enums.GenerateBillFlagEnum;
import com.mascj.lup.event.bill.service.*;
import com.mascj.lup.event.bill.util.GeoLocation;
import com.mascj.lup.event.bill.util.KXPlatformClient;
import com.mascj.lup.event.bill.util.ReadConfigUtil;
import com.mascj.lup.event.bill.vo.SendFourResutVo;
import com.mascj.lup.event.bill.vo.config.HandleModuleConfigVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/3 18:36
 * @describe
 */
@AllArgsConstructor
@Service("GHTranslateService")
public class PushGHDataServiceImpl implements IPushDataService {

    private final ReadConfigUtil readConfigUtil;

    private ILupDataService iLupDataService;

    private ILupBillService billService;
    private ILupDataPushLogService dataPushLogService;

    private ILupLabelService labelService;



    @Override
    public Result<SendFourResutVo> translateData(PushKQDTO pushKQDTO, LupData lupData, LupBill lupBill, String labelName) throws Exception {
        SendFourResutVo sendFourResutVo = new SendFourResutVo();
        LupData selectLupData = null;
        try {
            KXPlatformClient platformClient = new KXPlatformClient(pushKQDTO.getHost(),pushKQDTO.getAppKey(),pushKQDTO.getAppSecret());
            KXPushDataDTO data = new KXPushDataDTO();

            data.setWarningSourceType(2);
            data.setManufacturerId(5);
            data.setBusinessCode("基层治理");

            data.setLatitude(lupData.getLocationLat().toString());
            data.setLongitude(lupData.getLocationLng().toString());

            String res = GeoLocation.locationToAddress(pushKQDTO.getGeoLocationKey(),data.getLongitude().toString(),data.getLatitude().toString()).getBody();
            JSONObject resJsonObj = JSONUtil.parseObj(res);
            //{"result":{"formatted_address":"浙江省绍兴市柯桥区杨汛桥街道延寿寺","location":{"lon":120.28543,"lat":30.10462},"addressComponent":{"address":"延寿寺","city":"绍兴市","county_code":"156330603","nation":"中国","poi_position":"西北","county":"柯桥区","city_code":"156330600","address_position":"西北","poi":"延寿寺","province_code":"156330000","province":"浙江省","road":"杨渔线","road_distance":70,"poi_distance":40,"address_distance":40}},"msg":"ok","status":"0"}
            data.setLocationName(
                    resJsonObj.getJSONObject("result").getStr("formatted_address")
            );

            data.setDeviceCode("暂无");
            data.setDeviceType("暂无");
            data.setDeviceName("暂无");

            data.setGeoType("wgs84");

            data.setAlarmType(labelName);

            data.setUpTime(DateUtil.date().toString().replace(" ","T"));

            if(pushKQDTO.getEventContentTemplate() == null) {
                data.setEventContent(String.format("在%s可能发生了【%s】事件！", data.getLocationName(), labelName));
            }else {
                data.setEventContent(String.format(pushKQDTO.getEventContentTemplate(), data.getLocationName(), labelName));
            }

            DataPicDTO dataPicDTO = JSONUtil.toBean(lupData.getExtraData(),DataPicDTO.class);

            //多张图
            if(dataPicDTO.getEventPictureUrlList()==null){
                dataPicDTO.setEventPictureUrlList(new ArrayList<>());
            }
            if(!dataPicDTO.getEventPictureUrlList().contains(dataPicDTO.getEventPictureUrl())){
                dataPicDTO.getEventPictureUrlList().add(dataPicDTO.getEventPictureUrl());
            }
            data.setPicUrl(dataPicDTO.getEventPictureUrlList().stream().collect(Collectors.joining(",")));
            data.setThumbnailUrl(dataPicDTO.getEventPictureUrl());

            data.setAlarmDetailType(labelName);
            data.setHandleStatus(0);//0 未处理 1 已处理


            data.setProvinceCode(lupBill.getGridCode().substring(0,2)+"0000000000");
            data.setCityCode(lupBill.getGridCode().substring(0,4)+"00000000");
            data.setDistrictCode(lupBill.getGridCode().substring(0,6)+"000000");//330603110238
            data.setStreetCode(lupBill.getGridCode().substring(0,9)+"000");
            data.setCommunityCode(lupBill.getGridCode());

            data.setSourceId(lupBill.getBillNumber());
            data.setGridName(lupBill.getGridName());
            data.setAlarmFlag("1");

            String response = platformClient.gatewayRequest(pushKQDTO.getPushPath(),pushKQDTO.getPushPathCode(),data);
            //成功时 更新 推送状态；增加推送记录；
            JSONObject resJson = JSONUtil.parseObj(response);
            Integer status = resJson.getInt("status");

             selectLupData = iLupDataService.getById(lupData.getId());

            selectLupData.setUpdateTime(null);
            selectLupData.setUpdateBy(null);
            if (status == 200) {
                selectLupData.setPushState(1);
                sendFourResutVo.setSuccess(true);
            } else {
                sendFourResutVo.setSuccess(false);

                selectLupData.setPushState(-1);
                String message = resJson.getStr("message");
                sendFourResutVo.setFailMsg(message);
                selectLupData.setPushMsg(message);
            }
            iLupDataService.updateById(selectLupData);
        }catch (Exception e){
            e.printStackTrace();

            selectLupData = iLupDataService.getById(lupData.getId());
            sendFourResutVo.setSuccess(false);

            selectLupData.setPushState(-1);
            sendFourResutVo.setFailMsg(e.getMessage());
            selectLupData.setPushMsg(e.getMessage());
            iLupDataService.updateById(selectLupData);
        }

        return  Result.data(sendFourResutVo);
    }

    /**
     * ## 接口统一规范
     *
     * ## 服务器地址
     *
     * | 环境    | 服务器地址 | APP ID | APP Secret |
     * | ------- | ---------- | ------ | ---------- |
     * | 测试环境 | http://rivers.api.dev.jietuhuanbao.com | d530dc3736d29891 | 18cca0f0ade24d579a608c7195c37d08 |
     * | 生产环境 | https://rivers.api.jietuhuanbao.com | f37a9f210badd381 | e861fa90dcd0439db389efc7b4cb6200 |
     *
     * ## 请求规范
     *
     * - 使用 json 格式
     * - 请求头带认证参数 `appId` `appSecret`
     *
     * ## 返回规范
     *
     * 返回数据格式为 json，使用统一返回结构
     *
     * ```js
     * {
     *   code: 0,
     *   data: {},
     *   msg: ''
     * }
     * ```
     *
     * code 0 表示成功，其他表示失败，后续会补充
     *
     * ## 功能接口
     *
     * ### 环境问题上报
     *
     * POST /river-server-webapi/api/uav/uavFlight/insertWarningEvent
     *
     * ```js
     * {
     *   uavName:    'uav1',                  // 无人机名称
     *   eventDate:  '2024-01-01',            // 巡查日期: 年月日
     *   actionDate: '2024-01-01 00:00:00',   // 采集时间：年月日时分秒
     *   location:   '120.123,30.123',        // 坐标: 经度,纬度
     *   eventTitle: '',                      // 事件标题
     *   eventDesc:  '',                      // 事件描述
     *
     *   weather: {                           // 天气数据
     *     weather:       '晴',               // 天气
     *     temperature:   '23',               // 温度
     *     windDirection: '北风',             // 风向
     *     windPower:     '3-4级',            // 风力
     *     humidity:      '78%',              // 湿度
     *   },
     *
     *   commonResourcesDTOS: [                // 附件列表
     *     {
     *       resourceType: 1,                  // 资源类型：1图；2视频；3文件
     *       resourceName: 'xxx.jpg',          // 资源名称
     *       resourceUrl:  'http://xxx.jpg',   // 资源地址
     *       sort:         1,                  // 排序
     *     }
     *   ],
     * }
     * ```
     * @param dataId
     * @return
     */
    @Override
    public Result translateData(Long dataId,Long pushLogId) {

        LupLabel label = labelService.findLabelByDataId(dataId);

        LupDataPushLog dataPushLog = new LupDataPushLog();
        dataPushLog.setId(pushLogId);

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();
        PushKQDTO pushKQDTO = handleModuleConfigVO.getPushKQDTO();

        GenerateBillFlagEnum generateBillFlagEnum = GenerateBillFlagEnum.parse(pushKQDTO.getGenerateBillFlag());


        LupData data = iLupDataService.getById(dataId);

        LupData dataRes = new LupData();
        dataRes.setId(data.getId());



            try {

                GHTranslateDataDTO translateDataDTO = new GHTranslateDataDTO();
                translateDataDTO.setUavName(data.getDeviceName());
                if(ObjectUtil.isEmpty(data.getHappenedTime())) {
                    translateDataDTO.setEventDate(DateUtil.format(data.getCreateTime(), "yyyy-MM-dd"));
                    translateDataDTO.setActionDate(DateUtil.format(data.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                }else{
                    translateDataDTO.setEventDate(DateUtil.format(
                            DateUtil.parseLocalDateTime(
                            data.getHappenedTime()), "yyyy-MM-dd"));
                    translateDataDTO.setActionDate(DateUtil.format(DateUtil.parseLocalDateTime(
                            data.getHappenedTime()), "yyyy-MM-dd HH:mm:ss"));
                }
                translateDataDTO.setEventTitle(label.getName());
                translateDataDTO.setEventDesc(data.getEventDesc());
                List<GHTranslateCommonResourcesDTO> resourcesDTOList = new ArrayList<>();
                DataPicDTO dataPicDTO = JSONUtil.toBean(data.getExtraData(), DataPicDTO.class);
                //多张图
                if (dataPicDTO.getEventPictureUrlList() == null) {
                    dataPicDTO.setEventPictureUrlList(new ArrayList<>());
                }
                if (!dataPicDTO.getEventPictureUrlList().contains(dataPicDTO.getEventPictureUrl())) {
                    dataPicDTO.getEventPictureUrlList().add(dataPicDTO.getEventPictureUrl());
                }


                for (int i = 0; i < dataPicDTO.getEventPictureUrlList().size(); i++) {
                    String res = dataPicDTO.getEventPictureUrlList().get(i);

                    GHTranslateCommonResourcesDTO resourcesDTO = new GHTranslateCommonResourcesDTO();
                    resourcesDTO.setResourceUrl(res);
                    resourcesDTO.setResourceType(1);
                    resourcesDTO.setResourceName(res.substring(res.lastIndexOf("/") + 1));
                    resourcesDTO.setSort(i + 1);
                    resourcesDTOList.add(resourcesDTO);
                }
                translateDataDTO.setCommonResourcesDTOS(resourcesDTOList);
                //translateDataDTO
                translateDataDTO.setLocation(data.getLocationLng().toString() +
                        "," +
                        data.getLocationLat().toString());

                KXPlatformClient platformClient = new KXPlatformClient(pushKQDTO.getHost(), pushKQDTO.getAppKey(), pushKQDTO.getAppSecret());

                System.out.println("[GH]Request:" + JSONUtil.toJsonStr(translateDataDTO));
                //推送数据到第三方
                String response = platformClient.gatewayRequestGH(pushKQDTO.getPushPath(), pushKQDTO.getPushPathCode(), translateDataDTO);
                //成功时 更新 推送状态；增加推送记录；
                JSONObject resJson = JSONUtil.parseObj(response);

                dataRes.setPushMsg(resJson.getStr("msg"));

//                {"code":0,"msg":"无人机识别的环境问题上报成功"}

                //历史的dataId 查出推送日志pushable =

                if (resJson.getInt("code") == 0) {

                    List<LupDataPushLog> dataPushLogList = dataPushLogService.list(Wrappers.<LupDataPushLog>lambdaQuery().eq(LupDataPushLog::getDataId,dataId).ne(LupDataPushLog::getId,pushLogId));

                    List<LupDataPushLog> tmpDataPushLogList = new ArrayList<>();
                    dataPushLogList.forEach(e->{
                        LupDataPushLog log = new LupDataPushLog();
                        log.setId(e.getId());
                        log.setPushable(0);//是否可以重新推送 0否 1是
                        tmpDataPushLogList.add(log);

                    });
                    if(tmpDataPushLogList.size()>0)
                        dataPushLogService.updateBatchById(tmpDataPushLogList);

                    dataPushLog.setPushState(DataPushStateEnum.PushSuccess.getCode());
                    dataPushLog.setPushTime(DateUtil.now());
                    dataPushLog.setPushable(0);
                    dataRes.setPushState(DataPushStateEnum.PushSuccess.getCode());
                } else {
                    dataPushLog.setPushMsg(resJson.getStr("msg"));
                    dataPushLog.setPushState(DataPushStateEnum.PushFail.getCode());
                    dataRes.setPushState(DataPushStateEnum.PushFail.getCode());
                }

                System.out.println("[GH]response:" + response);

            } catch (Exception exp) {

                dataPushLog.setPushState(DataPushStateEnum.PushFail.getCode());
                dataRes.setPushState(DataPushStateEnum.PushFail.getCode());

                exp.printStackTrace();
                if(exp.getMessage().contains("code")
                        && exp.getMessage().contains("msg")
                        && exp.getMessage().contains("[{")
                        && exp.getMessage().contains("}]")){

                    //500 Internal Server Error: [{"code":7,"msg":"系统发生异常，请联系管理员"}]

                    String res = exp.getMessage().substring( exp.getMessage().lastIndexOf("[{")+1,exp.getMessage().lastIndexOf("}]")+1);

                    JSONObject resJson = JSONUtil.parseObj(res);

                    dataPushLog.setPushMsg(resJson.getStr("msg"));
                    dataRes.setPushMsg(resJson.getStr("msg"));

                }else {

                    dataPushLog.setPushMsg(exp.getMessage());
                    dataRes.setPushMsg(exp.getMessage());
                }


            }




        iLupDataService.updateById(dataRes);
        dataPushLogService.updateById(dataPushLog);

        return Result.data(dataPushLog);
    }

    public static void main(String []args){
        String res = "500 Internal Server Error: [{\"code\":7,\"msg\":\"系统发生异常，请联系管理员\"}]";
        System.out.println(res.substring(res.lastIndexOf("[{")+1,res.lastIndexOf("}]")+1));
    }
}
