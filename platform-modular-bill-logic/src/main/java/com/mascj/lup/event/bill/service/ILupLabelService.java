package com.mascj.lup.event.bill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.LabelListDTO;
import com.mascj.lup.event.bill.entity.LupLabel;
import com.mascj.lup.event.bill.vo.EventDataLabelVO;
import com.mascj.lup.event.bill.vo.LabelItemVO;
import com.mascj.lup.event.bill.vo.LupLabelVO;

import java.util.List;

/**
 * <p>
 * 事件标签 按名字去重 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface ILupLabelService extends IService<LupLabel> {

    List<EventDataLabelVO> labelList();
    /**
     * 保存标签  新增或者查询同名字的标签数据id
     * @param label
     * @return
     */
    LupLabel saveLabel(LupLabel label);

    Result<List<LabelItemVO>> listLabel(LabelListDTO labelDTO);

    LupLabel findLabelByDataId(Long dataId);

    List<LupLabelVO> allEventType();

}
