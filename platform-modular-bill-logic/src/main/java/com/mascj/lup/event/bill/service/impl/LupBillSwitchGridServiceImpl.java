package com.mascj.lup.event.bill.service.impl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupBillSwitchGrid;
import com.mascj.lup.event.bill.mapper.LupBillMapper;
import com.mascj.lup.event.bill.mapper.LupBillSwitchGridMapper;
import com.mascj.lup.event.bill.service.ILupBillService;
import com.mascj.lup.event.bill.service.ILupBillSwitchGridService;
import org.springframework.stereotype.Service;

@Service
public class LupBillSwitchGridServiceImpl extends ServiceImpl<LupBillSwitchGridMapper, LupBillSwitchGrid> implements ILupBillSwitchGridService {

}
