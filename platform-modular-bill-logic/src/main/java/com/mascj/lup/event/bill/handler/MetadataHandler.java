package com.mascj.lup.event.bill.handler;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.$;
import com.mascj.kernel.tools.RedisKey;
import com.mascj.kernel.tools.RedisPlane;
import com.mascj.lup.event.bill.tools.ResultOptional;
import com.mascj.platform.system.adminSubject.AdminSubjectCache;
import com.mascj.support.config.feign.ITableConfigProvider;
import com.mascj.support.config.vo.meta.MetaOperation;
import com.mascj.support.config.vo.meta.MetaTag;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@AllArgsConstructor
public class MetadataHandler {
    private final ITableConfigProvider tableConfigProvider;

    private final RedisPlane redisPlane;

    public List<MetaTag> getTableMetadata(String bizCode) {
        RedisKey redisKey = RedisKey.forService("lup-event-bill", MetadataHandler.class)
                .forItem(MetaTag.class.getSimpleName())
                .forParameter(bizCode, String.valueOf(LmContextHolder.getUserId()));

        return redisPlane.getOrCreateList(redisKey, () -> ResultOptional.of(tableConfigProvider.getTableMetadata(bizCode))
                .orElseThrow()
                .orElse(new ArrayList<>())
                .getData(), MetaTag.class, 60L);

    }



}
