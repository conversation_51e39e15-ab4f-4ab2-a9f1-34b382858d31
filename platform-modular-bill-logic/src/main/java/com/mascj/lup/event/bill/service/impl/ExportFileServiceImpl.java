package com.mascj.lup.event.bill.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.dto.ExportFileDTO;
import com.mascj.lup.event.bill.entity.DataReport;
import com.mascj.lup.event.bill.service.IDataReportRepository;
import com.mascj.lup.event.bill.service.IExportFileService;
import com.mascj.lup.event.bill.service.IExportFileStrategyService;
import com.mascj.lup.event.bill.service.sync.ExportFileSyncContext;
import com.mascj.lup.event.bill.service.sync.ExportFileSyncRunner;
import com.mascj.lup.event.bill.sync.ExportFileSyncState;
import com.mascj.lup.event.bill.util.ReadConfigUtil;
import com.mascj.lup.event.bill.vo.config.HandleModuleConfigVO;
import com.mascj.platform.system.adminSubject.AdminSubjectCache;
import com.mascj.platform.system.entity.App;
import com.mascj.platform.system.feign.ISysAppProvider;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@AllArgsConstructor
@Service
public class ExportFileServiceImpl implements IExportFileService {

    private final IDataReportRepository reportRepository;

    private ApplicationContext applicationContext;

    private ExportFileSyncContext registerTask;

    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Autowired
    private ReadConfigUtil readConfigUtil;

    /**
     * 开始导出
     *
     * @param exportFileDto 查询数据的参数
     */
    @Override
    public String startExport(ExportFileDTO exportFileDto) {

        if(ObjectUtil.isEmpty(exportFileDto.getFileTemplateType())) {
            HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();
            exportFileDto.setFileTemplateType(handleModuleConfigVO.getExportBillDataTemplateStrategy());
        }

        IExportFileStrategyService exportFileStrategyService = applicationContext.getBean(exportFileDto.getFileTemplateTypeEnum().getValue(),IExportFileStrategyService.class);

        exportFileDto.setDataTotal( exportFileStrategyService.initDataTotal(exportFileDto));

        Assert.isTrue(exportFileDto.getDataTotal() > 0, LocaleMessageUtil.getMessage(EventTipKey.NoneDataExport));

        ExportFileSyncState syncState = registerTask.registerTask(exportFileDto);

        DataReport report = new DataReport();
        exportFileDto.setReport(report);

//        Result<App> result = appProvider.getByBizCode(exportFileDto.getBizCode());
//
//        Assert.isTrue(200 == result.getCode(),"没有找到对应的应用");
//        App app = result.getData();

//        syncState.setAppId(app.getId());
//
//        report.setAppName(app.getName());
//        report.setAppCode(app.getBizCode());
        exportFileStrategyService.initReportInfo(exportFileDto);
        report.setCurrentLine(0);
        report.setSyncId(syncState.getUuid());
        report.setTotalLine(exportFileDto.getDataTotal());
        reportRepository.save(report);

        exportFileDto.setReportId(report.getId());

        executorService.execute(new ExportFileSyncRunner(syncState, applicationContext,exportFileStrategyService, exportFileDto));

        return syncState.getUuid();
    }
}
