package com.mascj.lup.event.bill.util;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mascj.lup.event.bill.dto.KXPushDataDTO;
import org.springframework.http.HttpHeaders;
import org.springframework.util.MultiValueMap;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/1/3 15:24
 * @describe
 */
public class SignatureUtils {


    /**
     * 允许时差
     */
    private static final long allowableTimeDifference = 60000;

    /**
     * 检查服务商签名的方法
     * @param appKey
     * @param appSecret
     * @param timestamp
     * @param nonce
     * @param signature
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public static boolean checkSignature(String appKey, String appSecret, long timestamp, String nonce, String signature) throws NoSuchAlgorithmException, InvalidKeyException {
        long currentTimeMillis = System.currentTimeMillis();
        if (timestamp > currentTimeMillis + allowableTimeDifference || timestamp < currentTimeMillis - allowableTimeDifference) {
            throw new RuntimeException("error");
        }
        return signature(appKey, appSecret, timestamp, nonce).equals(signature);
    }

    /**
     * 服务商获取accessToken的签名方法
     * @param appKey
     * @param appSecret
     * @param timestamp
     * @param nonce
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public static String signature(String appKey, String appSecret, long timestamp, String nonce) throws NoSuchAlgorithmException, InvalidKeyException {
        String message = appKey + appSecret + timestamp + nonce;
        return signature(appSecret, message);
    }

    /**
     * 底层加签方法
     * @param appSecret
     * @param message
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    private static String signature(String appSecret, String message) throws NoSuchAlgorithmException, InvalidKeyException {
        // 将密钥转换为字节数组
        byte[] key = appSecret.getBytes(StandardCharsets.UTF_8);

        // 使用HMAC-SHA256算法创建Mac对象
        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, "HmacSHA256");
        hmacSha256.init(secretKeySpec);

        // 计算消息的摘要
        byte[] digest = hmacSha256.doFinal(message.getBytes(StandardCharsets.UTF_8));

        // 对摘要进行Base64编码以生成签名
        String signature = Base64.getUrlEncoder().withoutPadding().encodeToString(digest);
        return signature;
    }

    /**
     * 方法签名
     * @param authorization
     * @param appKey
     * @param appSecret
     * @param timestamp
     * @param nonce
     * @param requestApi
     * @param bodyStr
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public static String methodSignature(String authorization, String appKey, String appSecret, long timestamp, String nonce, String requestApi, String bodyStr) throws NoSuchAlgorithmException, InvalidKeyException {
        String message = authorization + appKey + appSecret + timestamp + nonce + requestApi + bodyStr;
        return signature(appSecret, message);
    }




    public static void main(String []args){
                String host ="http://**************:7002";
        String appKey = "923d094315154f35a507e9b2c1179e03";
        String appSecret = "1ef15cc00b364a27a533707391d7b28f";



        try {
            KXPlatformClient platformClient = new KXPlatformClient(host,appKey,appSecret);
            KXPushDataDTO data = new KXPushDataDTO();
            String tianditu_key = "60ba7f4e6f5bace1de5e21449811a56b";
//            data.setWarningSourceType(2);
            data.setManufacturerId(5);
            data.setBusinessCode("基层治理");

            data.setLatitude("30.10462");
            data.setLongitude("120.28543");

            String res = GeoLocation.locationToAddress(tianditu_key,data.getLongitude().toString(),data.getLatitude().toString()).getBody();
            JSONObject resJsonObj = JSON.parseObject(res);
            //{"result":{"formatted_address":"浙江省绍兴市柯桥区杨汛桥街道延寿寺","location":{"lon":120.28543,"lat":30.10462},"addressComponent":{"address":"延寿寺","city":"绍兴市","county_code":"156330603","nation":"中国","poi_position":"西北","county":"柯桥区","city_code":"156330600","address_position":"西北","poi":"延寿寺","province_code":"156330000","province":"浙江省","road":"杨渔线","road_distance":70,"poi_distance":40,"address_distance":40}},"msg":"ok","status":"0"}
            data.setLocationName(
                    resJsonObj.getJSONObject("result").getString("formatted_address")
//                    "杨汛桥街道的联社村内（测试数据）"

            );
            resJsonObj = null;
            res = null;
            data.setDeviceCode("暂无");
            data.setDeviceType("100");
            data.setDeviceName("暂无");

            data.setGeoType("wgs84");

            data.setAlarmType("已有存量墓穴");

            data.setUpTime(DateUtil.date().toString().replace(" ","T"));

            data.setEventContent("已有存量墓穴");
            data.setPicUrl("https://cdn.uav.lyiot.cc/yxqdata/1733039082149703682/2023-12/09/a4a3eb42-d698-4366-8f60-6eea1f47243f.jpg");
            data.setThumbnailUrl("https://cdn.uav.lyiot.cc/yxqdata/1733039082149703682/2023-12/09/a4a3eb42-d698-4366-8f60-6eea1f47243f.jpg");
            data.setAlarmDetailType("已有存量墓穴");
            data.setHandleStatus(0);//0 未处理 1 已处理

            data.setProvinceCode("330000000000");
            data.setCityCode("330600000000");
            data.setDistrictCode("330603000000");
            data.setStreetCode("330603110000");
            data.setCommunityCode("330603110237");
            data.setAlarmFlag("1");

            String response = platformClient.gatewayRequest("/video-fusion/upnotify/commonReport","488",data);
            JSONObject resJson = JSON.parseObject(response);
            Integer status = resJson.getInteger("status");
            if (status == 200) {
                String token = resJson.getJSONObject("data").getString("token");
//            return token;
            } else {
                String message = resJson.getString("message");
                throw new RuntimeException(message);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
//        catch (InvalidKeyException e) {
//            throw new RuntimeException(e);
//        }

//        MultiValueMap<String, String> headerMap = new HttpHeaders();
//        Map<String, Object> gatewayAccessTokenParam = new HashMap<>();
//
//        gatewayAccessTokenParam.put("appKey", appKey);
//        long timestamp = System.currentTimeMillis();
//        gatewayAccessTokenParam.put("timestamp", timestamp);
//        String nonce = UUID.randomUUID().toString();
//        gatewayAccessTokenParam.put("nonce", nonce);
//        String signature = null;
//        //获取签名
//        try {
//            signature = SignatureUtils.signature(appKey, appSecret, timestamp, nonce);
//        } catch (Exception e) {
//            throw new RuntimeException("生成签名失败");
//        }
//        gatewayAccessTokenParam.put("signature", signature);
//        String requestUrl = host + "/auth/getToken";
//
//
//        String response =  KXPlatformClient.doRequest(requestUrl, "POST", headerMap, gatewayAccessTokenParam, "application/json");
//
//
//        JSONObject resJson = JSON.parseObject(response);
//        Integer status = resJson.getInteger("status");
//        if (status == 200) {
//            String token = resJson.getJSONObject("data").getString("token");
////            return token;
//        } else {
//            String message = resJson.getString("message");
//            throw new RuntimeException(message);
//        }


//        //{
//        //	"appKey":"923d094315154f35a507e9b2c1179e03",
//        //	"timestamp":"20240101 01:01:01",
//        //	"nonce":"1111324234234",
//        //	"signature":"trtytuy"
//        //}
//

//
//        Map<String,Object> parameterMap = new HashMap<>();
//        parameterMap.put("appKey","923d094315154f35a507e9b2c1179e03");
//        parameterMap.put("timestamp", DateUtil.date().getTime());
//        parameterMap.put("nonce","123");
//        try {
//            parameterMap.put("signature",signature(JSONUtil.toJsonStr(parameterMap),appSecret));
//        } catch (NoSuchAlgorithmException e) {
//            throw new RuntimeException(e);
//        } catch (InvalidKeyException e) {
//            throw new RuntimeException(e);
//        }
//        ;
//

//        System.out.println("1");
    }



}
