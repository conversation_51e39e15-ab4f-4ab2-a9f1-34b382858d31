package com.mascj.lup.event.bill.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mascj.lup.event.bill.entity.LupGridUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 网格数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface LupGridUserMapper extends BaseMapper<LupGridUser> {

    void batchDelete(@Param("gridUser") LupGridUser gridUser);
}
