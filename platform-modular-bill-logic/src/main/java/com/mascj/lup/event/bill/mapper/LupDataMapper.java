package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 事件数据 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface LupDataMapper extends BaseMapper<LupData> {

    List<EventDataBaseInfoVO> listAllEventData();

    List<EventDataTaskVO> pageByTask(IPage page, @Param("data") EventDataTaskQueryDTO search);
    List<EventDataVO> pageEventData(IPage page, @Param("data") EventDataQueryDTO search);

    List<EventDataVO> pageEventDataForLand(IPage page, @Param("data") EventDataForLandQueryDTO search);


    List<FlyTaskReportBillCountVO> queryFlyTaskReportVO( @Param("data") FlyTaskReportDTO flyTaskReportDTO);
    EventDataVO detail(@Param("billId") Long billId);

    List<CountEventDataVO> countByArea();
    List<CountEventDataVO> countByLabel();
    List<CountEventDataVO> countByLabelOnDay();

    List<CountEventDataVO> countByOriginByDate(@Param("data")EventDataCountQueryDTO eventDataCountQueryDTO);

    List<CountEventDataVO> countByOriginByMonth(@Param("data")EventDataCountQueryDTO eventDataCountQueryDTO);

    List<CountEventDataVO> countByOriginByYear(@Param("data")EventDataCountQueryDTO eventDataCountQueryDTO);

    List<CountEventDataOriginVO> countByOriginDataFetchWayByDate(@Param("data")EventDataCountQueryDTO eventDataCountQueryDTO);



    List<CountEventDataDealResultVO> countByDealResultByDate(@Param("data")EventDataCountQueryDTO eventDataCountQueryDTO);

    List<CountEventDataDealResultVO> countByDealResultByMonth(@Param("data")EventDataCountQueryDTO eventDataCountQueryDTO);

    List<CountEventDataDealResultVO> countByDealResultByYear(@Param("data")EventDataCountQueryDTO eventDataCountQueryDTO);

    @InterceptorIgnore(tenantLine = "true")
    List<LupData> dealOldDataPic(@Param("endDate")String endDate);


    List<BillQueryCountVO> billQueryCount(@Param("data")BillQueryCountDTO billQueryCountDTO);

    List<BillQueryListItemVO> billQueryList(@Param("data")BillQueryCountDTO billQueryCountDTO);
}
