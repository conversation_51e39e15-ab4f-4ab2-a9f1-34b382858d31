package com.mascj.lup.event.bill.service;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.IService;

import com.mascj.kernel.common.constant.SystemConstant;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.LupGridUserDTO;
import com.mascj.lup.event.bill.dto.LupUserDTO;
import com.mascj.lup.event.bill.entity.LupGridUnit;
import com.mascj.lup.event.bill.entity.LupGridUser;
import com.mascj.lup.event.bill.vo.LupGridUserVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * <p>
 * 网格数据表 服务类
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
public interface ILupGridUserService extends IService<LupGridUser> {

    List<LupGridUser> validAddGridUser(LupGridUserDTO gridUserDTO);
    /**
     * 新增用户关联关系
     * @param gridUserDTO 用户关联表格记录表
     * @return
     */
    List<LupUserDTO> addGridUser(List<LupGridUser> gridUserList,LupGridUserDTO gridUserDTO, List<LupGridUnit> gridUnitList);

    void saveUserInfo(Long userId,List<LupGridUnit> list);

    List<LupGridUnit> getUserLupGridUnitInfo(Long userId);

    List<LupGridUser> getGridUserByGridId(Long gridId);

    /**
     * 删除用户关联关系
     * @param gridUserDTO 用户关联表格记录表
     * @return
     */
    boolean delGridUser(LupGridUserDTO gridUserDTO);


    /**
     * 查询当前用户的网格权限id
     * @return
     */
    List<Long> dataScope(Long userId);

    LupGridUserVO getGridUserList(Long gridUnitId);
}
