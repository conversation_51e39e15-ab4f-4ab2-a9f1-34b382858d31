package com.mascj.lup.event.bill.open.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.EventDataQueryDTO;
import com.mascj.lup.event.bill.open.vo.OpenEventDataDetailVO;
import com.mascj.lup.event.bill.open.vo.OpenEventDataVO;

/**
 * <AUTHOR>
 * @date 2025/7/16 11:29
 * @describe
 */
public interface IOpenEventDataService {

    IPage<OpenEventDataVO> pagedEventData(EventDataQueryDTO eventDataQueryDTO);

    Result<OpenEventDataDetailVO> detailEventData(Long billId);
}
