package com.mascj.lup.event.bill.open.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.EventDataQueryDTO;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.mapper.open.OpenEventDataMapper;
import com.mascj.lup.event.bill.open.service.IOpenEventDataService;
import com.mascj.lup.event.bill.open.vo.OpenEventDataDetailVO;
import com.mascj.lup.event.bill.open.vo.OpenEventDataVO;
import lombok.AllArgsConstructor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/7/16 11:29
 * @describe
 */
@Service
@AllArgsConstructor
public class OpenEventDataServiceImpl extends ServiceImpl<OpenEventDataMapper, LupBill> implements IOpenEventDataService {

    @Override
    public IPage<OpenEventDataVO> pagedEventData(EventDataQueryDTO eventDataQueryDTO) {
        IPage<OpenEventDataVO> lupBillPage = new Page<>(eventDataQueryDTO.getCurrent(), eventDataQueryDTO.getSize());
        lupBillPage = baseMapper.pageOpenEventData(lupBillPage, eventDataQueryDTO);
        return lupBillPage;
    }

    @Override
    public Result<OpenEventDataDetailVO> detailEventData(Long billId) {
        if (billId == null) {
            return Result.fail("事件ID不能为空");
        }

        try {
            OpenEventDataDetailVO detailVO = baseMapper.detailEventData(billId);
            if (detailVO == null) {
                return Result.fail("未找到对应的事件数据");
            }
            return Result.data(detailVO);
        } catch (Exception e) {
            return Result.fail("查询事件详情失败：" + e.getMessage());
        }
    }
}
