package com.mascj.lup.event.bill.service.impl;

import cc.lyiot.framework.common.pojo.CommonResult;
import com.mascj.lup.cds.survey.feign.SurveyAchievementFeign;
import com.mascj.lup.datacenter.client.feign.DataCenterClientTaskFeign;
import com.mascj.lup.datacenter.client.vo.res.AchievementClientPreviewVO;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.entity.LupSource;
import com.mascj.lup.event.bill.mapper.LupSourceMapper;
import com.mascj.lup.event.bill.vo.SourceTileVO;
import com.mascj.kernel.common.api.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * LupSourceServiceImpl 测试类
 * 测试瓦片数据获取的优雅降级处理
 */
@ExtendWith(MockitoExtension.class)
class LupSourceServiceImplTest {

    @Mock
    private LupSourceMapper lupSourceMapper;

    @Mock
    private DataCenterClientTaskFeign streamProvider;

    @Mock
    private SurveyAchievementFeign surveyAchievementFeign;

    @InjectMocks
    private LupSourceServiceImpl lupSourceService;

    private BillDTO billDTO;
    private LupSource validSource;
    private LupSource invalidSource;

    @BeforeEach
    void setUp() {
        billDTO = new BillDTO();
        billDTO.setProjectId(1L);

        // 创建有效的数据源
        validSource = new LupSource();
        validSource.setId(1L);
        validSource.setOriginalTileSourceId(100L);
        validSource.setSourceName("有效瓦片");
        validSource.setFlyDate("2024-01-01");
        validSource.setType(2); // 非TileSourceType
        validSource.setSourceType(1); // 非CompareModuleSource

        // 创建无效的数据源
        invalidSource = new LupSource();
        invalidSource.setId(2L);
        invalidSource.setOriginalTileSourceId(200L);
        invalidSource.setSourceName("无效瓦片");
        invalidSource.setFlyDate("2024-01-01");
        invalidSource.setType(2);
        invalidSource.setSourceType(1);
    }

    @Test
    void testListSourceTile_WithValidAndInvalidData_ShouldSkipInvalidAndContinue() {
        // 准备测试数据
        List<LupSource> sourceList = Arrays.asList(validSource, invalidSource);
        when(lupSourceMapper.listSourceTile(any(BillDTO.class))).thenReturn(sourceList);

        // 模拟有效数据的返回
        CommonResult validResult = new CommonResult();
        validResult.setData(new AchievementClientPreviewVO());
        when(surveyAchievementFeign.preview(100L)).thenReturn(validResult);

        // 模拟无效数据的返回（返回null）
        when(surveyAchievementFeign.preview(200L)).thenReturn(null);

        // 执行测试
        Result<List<SourceTileVO>> result = lupSourceService.listSourceTile(billDTO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());

        // 验证方法被调用
        verify(surveyAchievementFeign, times(1)).thenReturn(100L);
        verify(surveyAchievementFeign, times(1)).thenReturn(200L);
    }

    @Test
    void testFetchTileOptions_WithNullId_ShouldReturnNull() {
        // 执行测试
        AchievementClientPreviewVO result = lupSourceService.fetchTileOptions(null);

        // 验证结果
        assertNull(result);
        
        // 验证没有调用外部服务
        verify(streamProvider, never()).preview(anyLong());
    }

    @Test
    void testFetchTileOptions_WithValidId_ShouldReturnData() {
        // 准备测试数据
        AchievementClientPreviewVO expectedData = new AchievementClientPreviewVO();
        expectedData.setPreviewUrl("http://example.com/tile");
        
        Result<AchievementClientPreviewVO> mockResult = Result.data(expectedData);
        when(streamProvider.preview(1L)).thenReturn(mockResult);

        // 执行测试
        AchievementClientPreviewVO result = lupSourceService.fetchTileOptions(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals("http://example.com/tile", result.getPreviewUrl());
        
        // 验证方法被调用
        verify(streamProvider, times(1)).preview(1L);
    }

    @Test
    void testFetchTileOptions_WithException_ShouldReturnNull() {
        // 模拟异常
        when(streamProvider.preview(1L)).thenThrow(new RuntimeException("服务异常"));

        // 执行测试
        AchievementClientPreviewVO result = lupSourceService.fetchTileOptions(1L);

        // 验证结果
        assertNull(result);
        
        // 验证方法被调用
        verify(streamProvider, times(1)).preview(1L);
    }
}
