<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>lup-event-workbill</artifactId>
        <groupId>com.mascj</groupId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.mascj</groupId>
    <artifactId>platform-modular-bill-logic</artifactId>
    <version>2.0.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>platform-modular-bill-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mascj</groupId>
                    <artifactId>support-config-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>lup-event-stream-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>

        <!--空间数据格式转换-->
        <dependency>
            <groupId>com.esri.geometry</groupId>
            <artifactId>esri-geometry-api</artifactId>
            <version>2.2.4</version>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>lup-event-workflow-service</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.mascj</groupId>
                    <artifactId>support-config-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mascj</groupId>
                    <artifactId>support-file-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mascj</groupId>
                    <artifactId>support-workflow-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>support-workflow-api</artifactId>
            <version>1.6.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>platform-system-api</artifactId>
            <version>2.9.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>support-file-api</artifactId>
            <version>1.6.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mascj</groupId>
                    <artifactId>support-config-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>support-config-api</artifactId>
            <version>1.6.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mascj</groupId>
                    <artifactId>support-workflow-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>lup-datacenter-api</artifactId>
            <version>1.6.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>lup-datacenter-client</artifactId>
            <version>1.6.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>lup-cds-survey-feign</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>lup-cds-djicloud-feign</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.redisson</groupId>
                    <artifactId>redisson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>platform-micro-api</artifactId>
            <version>1.6.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>platform-modular-dimension-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <!--rabbitmq-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.rabbitmq</groupId>
            <artifactId>amqp-client</artifactId>
            <version>5.9.0</version>
        </dependency>


        <!-- Apache Commons CSV -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.9.0</version>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>lup-land-parcel-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>lup-cds-ai-feign</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

    </dependencies>

</project>