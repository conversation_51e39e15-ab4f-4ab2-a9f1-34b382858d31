<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>liangma-cloud-parent</artifactId>
        <groupId>com.mascj</groupId>
        <version>1.6.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <version>2.0.0-SNAPSHOT</version>

    <properties>
        <java.version>11</java.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>

        <ucp.event.bill.version>1.6.0-SNAPSHOT</ucp.event.bill.version>
        <liangma.kernel.version>1.0.2-SNAPSHOT</liangma.kernel.version>
        <liangma.platform.version>1.6.0-SNAPSHOT</liangma.platform.version>
        <liangma.support.version>1.6.0-SNAPSHOT</liangma.support.version>
        <liangma.cloud.version>1.6.0-SNAPSHOT</liangma.cloud.version>
    </properties>

    <artifactId>lup-event-workbill</artifactId>

    <packaging>pom</packaging>

    <description>事件处置系统</description>

    <modules>
        <module>platform-modular-bill-spring-boot-starter</module>
        <module>platform-modular-bill-rest</module>
        <module>platform-modular-bill-logic</module>
        <module>platform-modular-bill-api</module>
        <module>lup-event-workflow</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.mascj</groupId>
                <artifactId>support-workflow-api</artifactId>
                <version>1.6.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.mascj</groupId>
                <artifactId>support-config-api</artifactId>
                <version>1.6.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.mascj</groupId>
                <artifactId>support-workflow-api</artifactId>
                <version>1.6.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.mascj</groupId>
                <artifactId>platform-system-api</artifactId>
                <version>2.9.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.mascj</groupId>
                <artifactId>support-file-api</artifactId>
                <version>1.6.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.mascj</groupId>
                        <artifactId>support-file-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.mascj</groupId>
                <artifactId>liangma-kernel-tools</artifactId>
                <version>${liangma.kernel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.mascj</groupId>
                        <artifactId>platform-grid-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.mascj</groupId>
                        <artifactId>platform-system-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>liangma-cloud-dependencies</artifactId>
            <version>1.6.0-SNAPSHOT</version>
            <type>pom</type>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>liangma-cloud-config</artifactId>
        </dependency>

    </dependencies>

    <repositories>
        <repository>
            <id>mascj-nexus</id>
            <name>dev</name>
            <url>https://nexus.mascj.com/repository/maven-public</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.4.1</version>
                <executions>
                    <execution>
                        <configuration>
                            <rules>
                                <bannedDependencies>
                                    <excludes>
                                        <exclude>com.mascj:support-config-api:1.5.0-SNAPSHOT</exclude>
                                        <exclude>com.mascj:support-file-api:1.5.0-SNAPSHOT</exclude>
                                    </excludes>
                                </bannedDependencies>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>