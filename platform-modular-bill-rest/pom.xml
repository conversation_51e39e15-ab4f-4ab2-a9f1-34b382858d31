<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>lup-event-workbill</artifactId>
        <groupId>com.mascj</groupId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.mascj</groupId>
    <artifactId>platform-modular-bill-rest</artifactId>

    <version>2.0.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>platform-modular-bill-logic</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mascj</groupId>
                    <artifactId>support-config-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>support-config-api</artifactId>
            <version>1.6.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <artifactId>liangma-kernel-health</artifactId>
            <groupId>com.mascj</groupId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
    </dependencies>

</project>