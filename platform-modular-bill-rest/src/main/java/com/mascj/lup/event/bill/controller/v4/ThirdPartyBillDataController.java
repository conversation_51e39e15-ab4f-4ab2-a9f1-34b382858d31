package com.mascj.lup.event.bill.controller.v4;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.BillSearchDTO;
import com.mascj.lup.event.bill.dto.QueryBillSearchDTO;
import com.mascj.lup.event.bill.service.ILupBillDataThirdPartyService;
import com.mascj.lup.event.bill.vo.BillSearchVO;
import com.mascj.lup.event.bill.vo.QueryBillDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/9/29 11:49
 * @describe
 */
@RestController
@RequestMapping("/third-party-lup-bill")
@AllArgsConstructor
@Api(value = "第三方事件数据查询功能",tags = "第三方事件数据查询功能")
public class ThirdPartyBillDataController {

    private final ILupBillDataThirdPartyService thirdPartyService;

    @PreAuth(hasPerm = "event:bill:list:third_party")
    @ApiOperation(value = "分页查询事件")
    @PostMapping(value = "/pagedBill")
    public Result<IPage<QueryBillDataVO>> pagedBill(@RequestBody @Validated QueryBillSearchDTO billSearchDTO){

        IPage<QueryBillDataVO> pages = thirdPartyService.pagedBill(new Page<>(billSearchDTO.getCurrent(),billSearchDTO.getSize()), billSearchDTO);

        return Result.data(pages);
    }
}
