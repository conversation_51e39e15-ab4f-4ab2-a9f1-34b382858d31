package com.mascj.lup.event.bill.controller;


import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.LabelListDTO;
import com.mascj.lup.event.bill.service.ILupLabelService;
import com.mascj.lup.event.bill.vo.LabelItemVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 事件标签 按名字去重 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Api(value = "事件标签管理",tags = "事件标签管理")
@RestController
@RequestMapping("/lupLabel")
@AllArgsConstructor
public class LupLabelController {

    private final ILupLabelService iLupLabelService;

    @PreAuth
    @GetMapping(value = "/listLabel")
    @ApiOperation(value = "查询列表", notes = "查询列表")
    public Result<List<LabelItemVO>> listLabel(LabelListDTO labelDTO){

        return iLupLabelService.listLabel(labelDTO);
    }

}

