package com.mascj.lup.event.bill.controller.v2;


import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.constant.Constants;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.feign.LupDataNotificationFeign;
import com.mascj.lup.event.bill.provider.EventDataQueryProvider;
import com.mascj.lup.event.bill.service.EventTjService;
import com.mascj.lup.event.bill.service.ILupFlowService;
import com.mascj.lup.event.bill.util.RedisOpsUtils;
import com.mascj.lup.event.bill.vo.FlyEventRankTwoVO;
import com.mascj.lup.event.bill.vo.FlyEventRankVO;
import com.mascj.lup.event.bill.vo.FlyEventTjQueryVO;
import com.mascj.lup.event.bill.vo.LupSubmitForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 工单数据表 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Api(value = "v2飞行事件统计",tags = "v2飞行事件统计")
@RestController
@AllArgsConstructor
@RequestMapping("v2/event")
public class FlyEventTjController {

    private final EventTjService eventTjService;
    @PreAuth
    @ApiOperation(value = "飞行事件top10  和 折线图")
    @GetMapping(value = "/eventRank")
    public Result<FlyEventRankTwoVO> eventRank(  FlyEventTjQueryVO flyEventTjQueryVO){

        FlyEventRankTwoVO flyEventRankTwoVO = eventTjService.eventRank(flyEventTjQueryVO);
        return Result.data(flyEventRankTwoVO);
    }


    @PreAuth
    @ApiOperation(value = "飞行核准事件top10  和 折线图")
    @GetMapping(value = "/eventRankEnd")
    public Result<FlyEventRankTwoVO> eventRankEnd(  FlyEventTjQueryVO flyEventTjQueryVO){
        flyEventTjQueryVO.setFlowState(VariableConstants.flowStateEnd);
        FlyEventRankTwoVO flyEventRankTwoVO = eventTjService.eventRank(flyEventTjQueryVO);
        return Result.data(flyEventRankTwoVO);
    }





}

