package com.mascj.lup.event.bill.controller.v10;

import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.lup.event.bill.dto.LupAppDTO;
import com.mascj.lup.event.bill.dto.LupAppOperateDTO;
import com.mascj.lup.event.bill.entity.LupApp;
import com.mascj.lup.event.bill.service.ILupAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13 15:50
 * @describe
 */
@RestController
@RequestMapping("/lup-app")
@AllArgsConstructor
@Api(value = "应用授权管理功能",tags = "应用授权管理功能")
public class LupAppController {

    private final ILupAppService appService;

    @PreAuth
    @ApiOperation(value = "保存应用信息")
    @PostMapping(value = "/saveAppInfo")
    public Result<LupApp> saveAppInfo(@RequestBody @Validated LupAppDTO appDTO){

        LmContextHolder.setTenantId("-1");

        return Result.data(appService.saveAppInfo(appDTO));
    }

    @PreAuth
    @ApiOperation(value = "根据KEY查询应用信息")
    @PostMapping(value = "/detailApp")
    public Result<LupApp> detailApp(@RequestBody LupAppOperateDTO appOperateDTO){
        return Result.data(appService.detailApp(appOperateDTO));
    }



}
