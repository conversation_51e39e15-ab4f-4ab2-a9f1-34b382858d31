package com.mascj.lup.event.bill.provider;

import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.EventDataDTO;
import com.mascj.lup.event.bill.dto.FlySystemDelDTO;
import com.mascj.lup.event.bill.dto.FlySystemEventDataSourceDTO;
import com.mascj.lup.event.bill.dto.LupGridUnitDTO;
import com.mascj.lup.event.bill.feign.IDataReceiveProvider;
import com.mascj.lup.event.bill.feign.IGridUnitDataProvider;
import com.mascj.lup.event.bill.feign.LupDataNotificationFeign;
import com.mascj.lup.event.bill.service.IDataReceiveService;
import com.mascj.lup.event.bill.service.ILupGridUnitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "工单服务Feign",tags = "工单服务Feign")
@RestController
@AllArgsConstructor
public class GridUnitDataProvider implements IGridUnitDataProvider {


    private final ILupGridUnitService gridUnitService;
    @Override
    public Result initProjectGridUnitData(LupGridUnitDTO gridUnitDTO) {
        gridUnitDTO.setName(EventTipKey.UnknownAreaName);
        gridUnitDTO.setCode(VariableConstants.UNKNOWN_AREA_CODE);
        return gridUnitService.addUnitData(gridUnitDTO);
    }
}
