package com.mascj.lup.event.bill.controller.v3;

import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.BillReadStateCounterDTO;
import com.mascj.lup.event.bill.dto.BillReadStateDTO;
import com.mascj.lup.event.bill.dto.ListUnitDTO;
import com.mascj.lup.event.bill.service.ILupBillReadService;
import com.mascj.lup.event.bill.vo.BillCountVO;
import com.mascj.lup.event.bill.vo.BillReadStateCounterVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/5 11:36
 * @describe
 */

@RestController
@RequestMapping("/lup-bill-read")
@AllArgsConstructor
@Api(value = "事件已读未读管理功能",tags = "事件已读未读管理功能")
public class LupBillReadController {

    private final ILupBillReadService billReadService;

    @PreAuth
    @ApiOperation(value = "统计未读的工单")
    @GetMapping(value = "/countBillUnReadCount")
    public Result<BillReadStateCounterVO> countBillUnReadCount(BillReadStateCounterDTO readStateCounterDTO){
        return Result.data( billReadService.countBillUnReadCount(readStateCounterDTO));
    }

    @PreAuth
    @ApiOperation(value = "保存工单读取状态")
    @PostMapping(value = "/saveBillReadState")
    public Result saveBillReadState(@RequestBody BillReadStateDTO billReadStateDTO){
        return Result.condition(billReadService.saveBillReadState(billReadStateDTO));
    }

}
