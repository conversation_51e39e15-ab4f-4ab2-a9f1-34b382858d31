package com.mascj.lup.event.bill.controller.v5;

import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.BillReadStateCounterDTO;
import com.mascj.lup.event.bill.dto.BillReadStateDTO;
import com.mascj.lup.event.bill.service.ILupBillNotificationService;
import com.mascj.lup.event.bill.service.ILupBillReadService;
import com.mascj.lup.event.bill.vo.BillReadStateCounterVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/9/5 11:36
 * @describe
 */

@RestController
@RequestMapping("/lup-bill-notification")
@AllArgsConstructor
@Api(value = "事件通知管理功能",tags = "事件通知管理功能")
public class LupBillNotificationController {

    private final ILupBillNotificationService billNotificationService;

}
