package com.mascj.lup.event.bill.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.database.entity.Search;
import com.mascj.lup.event.bill.entity.LupEventSmsGrade;
import com.mascj.lup.event.bill.service.ILupEventSmsGradeService;
import com.mascj.lup.event.bill.vo.LupEventLabelVo;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <p>
 * 处置分级短信 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2024-01-01
 */
@Api(tags = "处置分级短信管理")
@RestController
@AllArgsConstructor
@RequestMapping("/lupEventSmsGrade")
public class LupEventSmsGradeController {

    private final ILupEventSmsGradeService lupEventSmsGradeService;

    @PreAuth
    @ApiOperation(value = "分页查询处置分级短信", notes = "分页查询处置分级短信")
    @GetMapping("/labelPage")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "current", required = true, value = "当前页", paramType = "form"),
            @ApiImplicitParam(name = "size", required = true, value = "每页显示数据", paramType = "form"),
            @ApiImplicitParam(name = "codeValue", required = true, value = "字典项 eventOrigin", paramType = "form"),
    })
    public Result<IPage<LupEventLabelVo>> listPage(Integer codeValue , @ApiIgnore Search search) {
        return lupEventSmsGradeService.listPage(codeValue,search);
    }


    @PreAuth
    @PostMapping("/set")
    @ApiOperation(value = "新增处置分级短信", notes = "新增处置分级短信")
    public Result save(@Validated @RequestBody LupEventSmsGrade lupEventSmsGrade) {
        return Result.data(lupEventSmsGradeService.set(lupEventSmsGrade));
    }



//    @PreAuth
//    @GetMapping("/getByLabelId/{labelId}")
//    @ApiOperation(value = "根据标签ID查询处置分级短信", notes = "根据标签ID查询处置分级短信")
//    @ApiImplicitParam(name = "labelId", required = true, value = "标签ID", paramType = "path")
//    public Result<LupEventSmsGrade> getByLabelId(@PathVariable Long labelId) {
//        LupEventSmsGrade smsGrade = lupEventSmsGradeService.lambdaQuery()
//                .eq(LupEventSmsGrade::getLabelId, labelId)
//                .eq(LupEventSmsGrade::getDeleted, 0)
//                .one();
//        return Result.data(smsGrade);
//    }

}