package com.mascj.lup.event.bill.controller;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.dto.BillSearchDTO;
import com.mascj.lup.event.bill.dto.PushKQDTO;
import com.mascj.lup.event.bill.entity.*;
import com.mascj.lup.event.bill.mapper.LupLabelMapper;
import com.mascj.lup.event.bill.mapper.LupLabelYonganCategoryMapper;
import com.mascj.lup.event.bill.mapper.YonganCategoryMapper;
import com.mascj.lup.event.bill.service.IDataReceiveService;
import com.mascj.lup.event.bill.service.ILupBillService;
import com.mascj.lup.event.bill.service.ILupDataService;
import com.mascj.lup.event.bill.service.ILupFlowService;
import com.mascj.lup.event.bill.vo.*;
import com.mascj.support.config.feign.IConfigValueProvider;
import com.mascj.support.config.vo.ConfigInVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;


/**
 * <p>
 * 工单数据表 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Api(value = "测试流程",tags = "测试流程")
@RestController
@AllArgsConstructor
@RequestMapping("test/flow")
public class TestFlowController {

    private final IConfigValueProvider configValueProvider;

    private ILupBillService iLupBillService;
    private ILupFlowService lupFlowService;

    private ILupDataService iLupDataService;
    private YonganCategoryMapper yonganCategoryMapper;
    private LupLabelYonganCategoryMapper lupLabelYonganCategoryMapper;
    private LupLabelMapper lupLabelMapper;

    @PreAuth
    @ApiOperation(value = "添加工单")
    @GetMapping(value = "/addBill")
    public Result<BillVO> addBill(){

        String a = "{\"compareType\":1,\"currentFlyDate\":\"2023-08-09\",\"currentPictureLat\":31.38797669,\"currentPictureLng\":118.99923727,\"currentPictureUrl\":\"https://lywl-uav.oss-cn-shanghai.aliyuncs.com/2023/08/15/b0b8e9fe-da77-453b-ab08-08b10b3e0dd4.jpg\",\"eventName\":\"消防通道占用\",\"eventType\":2,\"projectId\":1690900061311012900}";
        LupData bill = JSONObject.parseObject(a, LupData.class);
        bill.setProjectId(Long.parseLong(LmContextHolder.getTenantId()));
        bill.setDataOrigin(2);
//        bill.setBillNumber(UUID.randomUUID().toString());
        bill.setLocationLat(new BigDecimal(31.38797669));
        bill.setLocationLng(new BigDecimal(118.99923727));
        Result<LupBillVO> lupBillResult = iLupBillService.addBill(bill);

//        lupFlowService.startupAepFlow(lupBillResult.getData().getId(),null);

        return null;
    }


    @ApiOperation(value = "queryConfig")
    @GetMapping(value = "/queryConfig")
    public  Result queryConfig(String code){
        ConfigInVo config = new ConfigInVo();
        config.setCode(code);
        config.setTenantId(Long.parseLong(LmContextHolder.getTenantId()));
        Result<com.alibaba.fastjson.JSONObject>configValResult =  configValueProvider.getConfigByCode(config);
        PushKQDTO pushKQDTO = JSONUtil.toBean(JSONUtil.toJsonStr(configValResult.getData()), PushKQDTO.class);
        return  Result.data(pushKQDTO);
    }

    @PreAuth
    @ApiOperation(value = "任务素材删除回收站模式 历史素材事件处理")
    @GetMapping(value = "/dealOldDataPic")
    public Result dealOldDataPic(String endDate){
        return Result.condition(iLupDataService.dealOldDataPic(endDate));
    }


    @PreAuth
    @ApiOperation(value = "增加永安脚本关联")
    @GetMapping(value = "/addRela")
    public Result<Boolean> addRela(String myName, String yonganName){

        YonganCategory yonganCategory = yonganCategoryMapper.selectOne(new LambdaQueryWrapper<>(new YonganCategory())
                .eq(YonganCategory::getCategoryLevel4Name, yonganName));
        if(yonganCategory == null){
            return Result.fail("yonganName不存在");
        }
        LupLabel lupLabel = lupLabelMapper.selectOne(new LambdaQueryWrapper<>(new LupLabel())
                .eq(LupLabel::getName, myName));
        if(lupLabel == null){
            return Result.fail("myName不存在");
        }
        LupLabelYonganCategory lupLabelYonganCategory = lupLabelYonganCategoryMapper.selectOne(new LambdaQueryWrapper<>(new LupLabelYonganCategory())
                .eq(LupLabelYonganCategory::getLabelId, lupLabel.getId())
                .eq(LupLabelYonganCategory::getYonganCategoryId, yonganCategory.getId()));
        if (lupLabelYonganCategory == null) {
            lupLabelYonganCategory = new LupLabelYonganCategory();
            lupLabelYonganCategory.setLabelId(lupLabel.getId());
            lupLabelYonganCategory.setYonganCategoryId(yonganCategory.getId());
            int insert = lupLabelYonganCategoryMapper.insert(lupLabelYonganCategory);
            return Result.success("添加成功"+insert);
        }

        return Result.success("");
    }
    @PreAuth
    @ApiOperation(value = "增加永安脚本关联All")
    @GetMapping(value = "/addRelaAll")
    public Result<Boolean> addRelaAll(){

        /**
         * 四级分类对应AI事件的映射关系
         */
        Map<String, String> CATEGORY_TO_AI_EVENT_MAP = new HashMap<>();

        /**
         * 四级分类对应人工上报事件的映射关系
         */
         Map<String, String> CATEGORY_TO_MANUAL_EVENT_MAP = new HashMap<>();

//        CATEGORY_TO_AI_EVENT_MAP.put("乱设或损坏户外设施", ""); // 无AI事件
//        CATEGORY_TO_AI_EVENT_MAP.put("跨门营业", ""); // 无AI事件
//        CATEGORY_TO_AI_EVENT_MAP.put("文明施工措施不落实", ""); // 无AI事件
        CATEGORY_TO_AI_EVENT_MAP.put("占道无证照经营", "占道经营,游街摆摊");
//        CATEGORY_TO_AI_EVENT_MAP.put("擅自占用道路堆物、施工", ""); // 无AI事件
        CATEGORY_TO_AI_EVENT_MAP.put("露天焚烧", "烟雾,火焰");
        CATEGORY_TO_AI_EVENT_MAP.put("机动车乱停放、非机动车乱停放", "非机动车道占用");
//        CATEGORY_TO_AI_EVENT_MAP.put("路面积水、污水冒溢、粪便冒溢", ""); // 无AI事件
        CATEGORY_TO_AI_EVENT_MAP.put("道路破损", "路面裂纹");
//        CATEGORY_TO_AI_EVENT_MAP.put("街头散发小广告", ""); // 无AI事件
        CATEGORY_TO_AI_EVENT_MAP.put("违规处置渣土", "砂土堆");
//        CATEGORY_TO_AI_EVENT_MAP.put("道路保洁", ""); // 无AI事件
//        CATEGORY_TO_AI_EVENT_MAP.put("行道数", ""); // 无AI事件
//        CATEGORY_TO_AI_EVENT_MAP.put("占用消防通道违法停车", ""); // 无AI事件
        CATEGORY_TO_AI_EVENT_MAP.put("违法搭建建筑物、构筑物", "违建");
//        CATEGORY_TO_AI_EVENT_MAP.put("小区存在违法搭建", ""); // 无AI事件
//        CATEGORY_TO_AI_EVENT_MAP.put("未在建筑垃圾堆放点设有临时停放牌和公示牌", ""); // 无AI事件
//        CATEGORY_TO_AI_EVENT_MAP.put("疑似违章施工", ""); // 无AI事件
        CATEGORY_TO_AI_EVENT_MAP.put("污水直排", "排污口");
        CATEGORY_TO_AI_EVENT_MAP.put("河道污染", "水面垃圾");
        CATEGORY_TO_AI_EVENT_MAP.put("焚烧秸秆", "焚烧痕迹");

        CATEGORY_TO_MANUAL_EVENT_MAP.put("乱设或损坏户外设施", "乱设或损坏户外设施");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("跨门营业", "跨门营业");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("文明施工措施不落实", "文明施工措施不落实");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("占道无证照经营", "占道经营");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("擅自占用道路堆物、施工", "占道堆物施工");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("露天焚烧", "露天焚烧");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("机动车乱停放、非机动车乱停放", "车辆乱停放");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("路面积水、污水冒溢、粪便冒溢", "路面污水");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("道路破损", "道路破损");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("街头散发小广告", "街头散发小广告");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("违规处置渣土", "违规处置渣土");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("道路保洁", "道路保洁");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("行道树", "行道树");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("占用消防通道违章停车", "消防车道占用");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("违法搭建建筑物、构筑物", "房屋违建");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("小区存在违法搭建", "违法搭建");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("未在建筑垃圾堆放点设有临时停放牌和公示牌", "未设立建筑垃圾公告牌");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("疑似违章施工", "河道违章施工");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("污水直排", "污水直排");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("河道污染", "河道污染");
        CATEGORY_TO_MANUAL_EVENT_MAP.put("焚烧秸秆", "焚烧秸秆");


        getBooleanResult(CATEGORY_TO_AI_EVENT_MAP, 1);
        getBooleanResult(CATEGORY_TO_MANUAL_EVENT_MAP, 2);

        return Result.success("");
    }

    @Nullable
    private Result<Boolean> getBooleanResult(Map<String, String> map, Integer codevalue) {
        for (Map.Entry<String, String> stringStringEntry : map.entrySet()) {
            String yonganName = stringStringEntry.getKey();
            YonganCategory yonganCategory = yonganCategoryMapper.selectOne(new LambdaQueryWrapper<>(new YonganCategory())
                    .eq(YonganCategory::getCategoryLevel4Name, yonganName));
            if(yonganCategory == null){
                System.out.println(yonganName + ": yonganName不存在");
                continue;
            }

            String myName2 = stringStringEntry.getValue();
            String[] split = myName2.split(",");
            for (String  myName : split) {
                LupLabel lupLabel = lupLabelMapper.selectOne(new LambdaQueryWrapper<>(new LupLabel())
                        .eq(LupLabel::getName, myName));
                if(lupLabel == null){
                    System.out.println( myName + " : myName不存在, 开始新增");

                    lupLabel = new LupLabel();
                    lupLabel.setName(myName);
                    lupLabel.setProjectId(Long.parseLong(LmContextHolder.getTenantId()));
                    lupLabel.setDeleted(0);
                    lupLabelMapper.insert(lupLabel);
                }

                LupLabelYonganCategory lupLabelYonganCategory = lupLabelYonganCategoryMapper.selectOne(new LambdaQueryWrapper<>(new LupLabelYonganCategory())
                        .eq(LupLabelYonganCategory::getLabelId, lupLabel.getId())
                        .eq(LupLabelYonganCategory::getCodeValue, codevalue)
                        .eq(LupLabelYonganCategory::getYonganCategoryId, yonganCategory.getId()));
                if (lupLabelYonganCategory == null) {
                    lupLabelYonganCategory = new LupLabelYonganCategory();
                    lupLabelYonganCategory.setLabelId(lupLabel.getId());
                    lupLabelYonganCategory.setCodeValue(codevalue);
                    lupLabelYonganCategory.setYonganCategoryId(yonganCategory.getId());
                    int insert = lupLabelYonganCategoryMapper.insert(lupLabelYonganCategory);
                    System.out.println("添加成功" + insert);
                }
            }



        }
        return null;
    }


}

