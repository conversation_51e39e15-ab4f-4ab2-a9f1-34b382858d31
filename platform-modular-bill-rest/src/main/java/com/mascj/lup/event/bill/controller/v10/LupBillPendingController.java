package com.mascj.lup.event.bill.controller.v10;

import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.LupBillPendingApplyDTO;
import com.mascj.lup.event.bill.dto.LupBillPendingApprovalDTO;
import com.mascj.lup.event.bill.dto.LupBillPendingOldDataDTO;
import com.mascj.lup.event.bill.service.ILupBillPendingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/12/30 11:36
 * @describe
 */

@RestController
@RequestMapping("/lup-bill-pending")
@AllArgsConstructor
@Api(value = "事件挂起管理功能",tags = "事件挂起管理功能")
public class LupBillPendingController {

    private final ILupBillPendingService billPendingService;

    @PreAuth
    @ApiOperation(value = "申请后期处理接口")
    @PostMapping(value = "/applyPending")
    public Result applyPending(@RequestBody @Validated LupBillPendingApplyDTO applyDTO){
        return Result.condition(billPendingService.applyPending(applyDTO));
    }

    @PreAuth
    @ApiOperation(value = "审核挂起")
    @PostMapping(value = "/approvalPending")
    public Result approvalPending(@RequestBody @Validated LupBillPendingApprovalDTO approvalDTO){
        return Result.condition(billPendingService.approval(approvalDTO));
    }

    @PreAuth
    @ApiOperation(value = "处理历史后期处理数据")
    @PostMapping(value = "/dealHistoryPendingBill")
    public Result dealHistoryPendingBill(@RequestBody @Validated LupBillPendingOldDataDTO billPendingOldDataDTO){
        return Result.condition(billPendingService.dealHistoryPendingBill(billPendingOldDataDTO));
    }


}
