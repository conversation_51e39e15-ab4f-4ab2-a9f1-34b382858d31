package com.mascj.lup.event.bill.controller.open;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.EventDataQueryDTO;
import com.mascj.lup.event.bill.feign.open.IEventDataOpenProvider;
import com.mascj.lup.event.bill.open.service.IOpenEventDataService;
import com.mascj.lup.event.bill.open.vo.OpenEventDataDetailVO;
import com.mascj.lup.event.bill.open.vo.OpenEventDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/7/16 10:18
 * @describe
 */

@RequestMapping("/provider/open-api")
@Api(value = "事件数据开放接口",tags = "事件数据开放接口")
@AllArgsConstructor
@RestController
public class EventDataController implements IEventDataOpenProvider {

    private final IOpenEventDataService openEventDataService;


    @ApiOperation(value = "分页查询事件数据")
    @PostMapping("/eventData/pagedEventData")
    public Result<IPage<OpenEventDataVO>> pagedEventData(@RequestBody EventDataQueryDTO eventDataQueryDTO) {
        IPage<OpenEventDataVO> pagedEventData = openEventDataService.pagedEventData(eventDataQueryDTO);
        return Result.data(pagedEventData);
    }
    @ApiOperation(value = "根据id查询事件数据详情")
    @GetMapping("/eventData/detail/{id}")
    public Result<OpenEventDataDetailVO> detailEventData(@PathVariable("id") Long billId) {
        return openEventDataService.detailEventData(billId);
    }
}
