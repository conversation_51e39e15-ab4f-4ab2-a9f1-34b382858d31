package com.mascj.lup.event.bill.provider;

import com.mascj.lup.event.bill.dto.EventDataDTO;
import com.mascj.lup.event.bill.dto.FixEventDataSourceDTO;
import com.mascj.lup.event.bill.dto.FlySystemDelDTO;
import com.mascj.lup.event.bill.dto.FlySystemEventDataSourceDTO;
import com.mascj.lup.event.bill.feign.IDataReceiveProvider;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.feign.LupDataNotificationFeign;
import com.mascj.lup.event.bill.service.IDataReceiveService;
import com.mascj.lup.land.parcel.provider.IClearDataCacheProvider;
import com.mascj.support.config.feign.ISysConfigProvider;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "工单服务Feign",tags = "工单服务Feign")
@RestController
@AllArgsConstructor
public class DataReceiveProvider implements IDataReceiveProvider {

    private final IDataReceiveService iDataReceiveService;

    private final LupDataNotificationFeign  dataNotificationFeign;
    private final IClearDataCacheProvider dataCacheProvider;



    @ApiOperation(value = "【无人机平台】提交事件数据")
    @PostMapping("/support/postFlyEventData")
    public Result postFlyEventData(@RequestBody FlySystemEventDataSourceDTO dataSourceDTO){

        dataCacheProvider.clearDataCache();

        Result result = iDataReceiveService.postCompareDataSystem(dataSourceDTO);
        dataNotificationFeign.eventDataCountOnChanged();
        return result;
    }

    @Override
    @ApiOperation(value = "【无人机平台】作废事件数据")
    @PostMapping("/support/delFlyEventData")
    public Result delFlyEventData(FlySystemDelDTO flySystemDelDTO) {
        dataCacheProvider.clearDataCache();
        Result result = iDataReceiveService.delFlyEventData(flySystemDelDTO);
        dataNotificationFeign.eventDataCountOnChanged();
        return result;
    }

    @Override
    @ApiOperation(value = "【比对系统】提交事件数据")
    @PostMapping("/support/postEventData")
    public Result postEventData(EventDataDTO eventData) {
        dataCacheProvider.clearDataCache();
        Result result = iDataReceiveService.postEventData(eventData);
        dataNotificationFeign.eventDataCountOnChanged();
        return result;
    }

    @Override
    @ApiOperation(value = "【比对系统】提交事件数据")
    @PostMapping("/support/fixEventData")
    public Result fixEventData(FixEventDataSourceDTO fixEventDataSourceDTO) {
        return iDataReceiveService.fixEventData(fixEventDataSourceDTO);
    }
}
