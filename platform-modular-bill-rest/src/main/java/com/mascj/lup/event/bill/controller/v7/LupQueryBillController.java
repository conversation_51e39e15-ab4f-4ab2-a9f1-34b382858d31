package com.mascj.lup.event.bill.controller.v7;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.BillSearchDTO;
import com.mascj.lup.event.bill.dto.QueryBillPictureInfoDTO;
import com.mascj.lup.event.bill.service.ILupQueryBillService;
import com.mascj.lup.event.bill.vo.BillSearchVO;
import com.mascj.lup.event.bill.vo.QueryBillPictureInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/26 15:15
 * @describe
 */

@RestController
@RequestMapping("/lup-bill")
@AllArgsConstructor
@Api(value = "事件管理功能",tags = "事件管理功能")
public class LupQueryBillController {


    private final ILupQueryBillService queryBillService;

    @PreAuth
    @ApiOperation(value = "查询事件图片信息")
    @ApiImplicitParam(value = "工单id",name = "billId")
    @PostMapping(value = "/queryBillPictureInfo")
    public Result<IPage<QueryBillPictureInfoVO>> queryBillPictureInfo(@RequestBody QueryBillPictureInfoDTO queryBillPictureInfoDTO){

        IPage<QueryBillPictureInfoVO> list  = queryBillService.queryBillPictureInfo(queryBillPictureInfoDTO);

        return Result.data(list);
    }
}
