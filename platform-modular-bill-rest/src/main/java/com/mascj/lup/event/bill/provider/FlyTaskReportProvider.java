package com.mascj.lup.event.bill.provider;

import cn.hutool.core.util.ObjectUtil;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.dto.FlyTaskReportDTO;
import com.mascj.lup.event.bill.feign.IFlyTaskReportProvider;
import com.mascj.lup.event.bill.service.ILupBillReportService;
import com.mascj.lup.event.bill.vo.FlyTaskReportVO;
import io.jsonwebtoken.lang.Assert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/11/25 15:27
 * @describe
 */
@Api(value = "工单服务Feign",tags = "工单服务Feign")
@RestController
@AllArgsConstructor
@RequestMapping(value = "/provider/report")
public class FlyTaskReportProvider implements IFlyTaskReportProvider {

    private final ILupBillReportService billReportService;


    @ApiOperation(value = "【无人机平台】查询飞行任务报告")
    @PostMapping("/queryFlyTaskReport")
    @Override
    public Result<FlyTaskReportVO> queryFlyTaskReport(@RequestBody @Validated FlyTaskReportDTO flyTaskReportDTO) {

        Assert.isTrue(ObjectUtil.isNotEmpty(LmContextHolder.getTenantId()), LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneQuery, Arrays.asList(EventTipKey.NoneTip)));

        return Result.data( billReportService.queryFlyTaskReport(flyTaskReportDTO));
    }
}
