package com.mascj.lup.event.bill.provider;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.feign.IEventDataQueryProvider;
import com.mascj.lup.event.bill.service.IEventDataQueryService;
import com.mascj.lup.event.bill.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:57
 * @describe
 */
@Api(value = "事件数据查询接口",tags = "事件数据查询接口")
@AllArgsConstructor
@RestController
public class EventDataQueryProvider implements IEventDataQueryProvider {

    private final IEventDataQueryService iEventDataQueryService;

    @Override
    @GetMapping("/support/eventData/labelList")
    public Result<List<EventDataLabelVO>> labelList() {
        return Result.data(iEventDataQueryService.labelList());
    }

    @Override
    @PostMapping("/support/eventData/listAll")
    public Result<List<EventDataBaseInfoVO>> listAll() {
        return Result.data(iEventDataQueryService.listAllEventData());
    }

    @Override
    @PostMapping("/support/eventData/pageByTask")
    public Result<Page<EventDataTaskVO>> pageByTask(@RequestBody EventDataTaskQueryDTO eventDataTaskQueryDTO){
        return Result.data(iEventDataQueryService.pageByTask(new Page<>(eventDataTaskQueryDTO.getCurrent(),eventDataTaskQueryDTO.getSize()),eventDataTaskQueryDTO));
    }
    @Override
    @PostMapping("/support/eventData/page")
    public Result<IPage<EventDataVO>> page(@RequestBody EventDataQueryDTO eventDataQueryDTO) {
        return Result.data(iEventDataQueryService.page(new Page<>(eventDataQueryDTO.getCurrent(),eventDataQueryDTO.getSize()),eventDataQueryDTO));
    }

    @Override
    @PostMapping("/support/eventData/pageForLand")
    @ApiOperation(value = "查询地块关联的事件")
    public Result<Page<EventDataVO>> pageForLand(@RequestBody EventDataForLandQueryDTO eventDataQueryDTO) {
        return Result.data(iEventDataQueryService.pageForLand(new Page<>(eventDataQueryDTO.getCurrent(),eventDataQueryDTO.getSize()),eventDataQueryDTO));
    }



    @Override
    @GetMapping("/support/eventData/detail/{id}")
    public Result<EventDataVO> detail(@PathVariable("id") Long billId) {
        return Result.data(iEventDataQueryService.detail(billId));
    }

    @Override
    @PostMapping("/support/eventData/countByArea")
    public Result<List<CountEventDataVO>> countByArea() {
        return Result.data(iEventDataQueryService.countByArea());
    }

    @Override
    @PostMapping("/support/eventData/countByLabel")
    public Result<List<CountEventDataVO>> countByLabel() {
        return Result.data(iEventDataQueryService.countByLabel());
    }
    @Override
    @PostMapping("/support/eventData/countByLabelOnDay")
    public Result<List<CountEventDataVO>> countByLabelOnDay() {
        return Result.data(iEventDataQueryService.countByLabelOnDay());
    }

    @Override
    @PostMapping("/support/eventData/countByOrigin")
    public Result<CountEventDataOriginGroupVO> countByOrigin(EventDataCountQueryDTO eventDataCountQueryDTO) {
        return Result.data(iEventDataQueryService.countByOrigin(eventDataCountQueryDTO));
    }

    @Override
    @PostMapping("/support/eventData/countByDealResult")
    public Result<CountEventDataDealResultVO> countByDealResult(EventDataCountQueryDTO eventDataCountQueryDTO) {
        return Result.data(iEventDataQueryService.countByDealResult(eventDataCountQueryDTO));
    }

    @Override
    @PostMapping("/support/eventData/billQueryCount")
    public Result<List<BillQueryCountVO>> billQueryCount(BillQueryCountDTO billQueryCountDTO) {
        return Result.data(iEventDataQueryService.billQueryCount(billQueryCountDTO));
    }

    @Override
    @ApiOperation(value = "查询事件列表")
    @PostMapping("/support/eventData/billQueryList")
    public Result<List<BillQueryListItemVO>> billQueryList(BillQueryCountDTO billQueryCountDTO) {
        return Result.data(iEventDataQueryService.billQueryList(billQueryCountDTO));
    }
}
