package com.mascj.lup.event.bill.controller.v6;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.lup.event.bill.dto.ExportDataBaseDTO;
import com.mascj.lup.event.bill.feign.IDataExportProvider;
import com.mascj.lup.event.bill.service.IDataTranslateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/13 16:09
 * @describe
 */
@PreAuth
@RestController
@Api(value = "数据传输服务",tags = "数据传输服务")
@RequestMapping("/provider")
@AllArgsConstructor
public class DataTranslateController implements IDataExportProvider {

    private final IDataTranslateService dataTranslateService;

    @ApiOperation(value = "导出数据")
    @PostMapping("/support/exportData")
    public Result exportData(@RequestBody Map<String,Object> exportDataBaseDTO){

        ExportDataBaseDTO dto =  JSONUtil.toBean(JSONUtil.toJsonStr(exportDataBaseDTO),ExportDataBaseDTO.class);

        Assert.isTrue(ObjectUtil.isNotEmpty(dto.getTenantId()),"租户ID必传");
        Assert.isTrue(ObjectUtil.isNotEmpty(dto.getModular()),"modular必传");

        List<String> list = new ArrayList<>();
        list.add("workBill");
        list.add("bizComparison");
        list.add("bizFlyer");
        Assert.isTrue(list.contains(dto.getModular()),"只能传 workBill、bizComparison、bizFlyer");

        new Thread(new Runnable() {
            @Override
            public void run() {

                LmContextHolder.setTenantId(dto.getTenantId());
                dataTranslateService.exportData(dto);
            }
        }).start();

        return Result.success("操作成功");
    }
}
