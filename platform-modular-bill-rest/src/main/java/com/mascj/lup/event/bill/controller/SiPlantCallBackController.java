package com.mascj.lup.event.bill.controller;

import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.EventDataDTO;
import com.mascj.lup.event.bill.dto.FlySystemEventDataSourceDTO;
import com.mascj.lup.event.bill.service.IDataReceiveService;
import com.mascj.lup.event.bill.service.impl.FourCallbakService;
import com.mascj.lup.event.bill.vo.FourCallbackVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@PreAuth
@RestController
@Api(value = "四平台反馈接口",tags = "四平台反馈接口")
@RequestMapping("/four/callback")
@AllArgsConstructor
public class SiPlantCallBackController {

    private final FourCallbakService fourCallbakService;

    @ApiOperation(value = "四平台处理旧的数据")
    @PostMapping("/dealOldData")
    public Result dealOldData(){
        return fourCallbakService.dealOldData();
    }



    @ApiOperation(value = "四平台反馈接口")
    @PostMapping("/addCallback")
    public Result postFlyEventData(@RequestBody FourCallbackVO fourCallbackVO){
        return fourCallbakService.addFourCallbak(fourCallbackVO);
    }

}
