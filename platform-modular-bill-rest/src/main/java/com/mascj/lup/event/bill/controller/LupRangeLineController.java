package com.mascj.lup.event.bill.controller;


import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.LabelListDTO;
import com.mascj.lup.event.bill.service.ILupLabelService;
import com.mascj.lup.event.bill.service.ILupRangeLineService;
import com.mascj.lup.event.bill.vo.LabelItemVO;
import com.mascj.lup.event.bill.vo.LupRangeLineVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 范围线  前端控制器
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Api(value = "范围线",tags = "范围线")
@RestController
@RequestMapping("/lupRangeLine")
@AllArgsConstructor
public class LupRangeLineController {

    private final ILupRangeLineService rangeLineService;

    @PreAuth
    @GetMapping(value = "/listByRangeCategoryId")
    @ApiOperation(value = "根据类目id 查询范围线数据list", notes = "根据类目id 查询范围线数据list")
    public Result<List<LupRangeLineVO>> listByRangeCategoryId(Long categoryId){
        return Result.data(rangeLineService.listByRangeCategoryId(categoryId));
    }

}

