package com.mascj.lup.event.bill.controller;

import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.util.$;
import com.mascj.lup.event.bill.dto.ExportFileDTO;
import com.mascj.lup.event.bill.service.IExportFileService;
import com.mascj.lup.event.bill.service.sync.ExportFileSyncContext;
import com.mascj.lup.event.bill.sync.ExportFileSyncState;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/12/18 16:50
 * @describe
 */
@RestController
@RequestMapping("/lup-file")
@AllArgsConstructor
@Api(value = "文件生成和导出管理功能",tags = "文件生成和导出管理功能")
public class LupFileController {

    private final IExportFileService exportFileService;

    private final ExportFileSyncContext exportFileSyncContext;

    /**
     * 导出文件
     *
     * @param query 查询参数
     * @return
     */
    @PreAuth
    @PostMapping("/export")
    @ApiOperation(value = "导出文件", notes = "根据不同的要求的模板导出文件")
    public Result<String> export(@RequestBody ExportFileDTO query) {

        return Result.data(exportFileService.startExport(query));
    }
    /**
     * 导出文件任务进度的状态
     *
     * @param syncId
     * @return
     */
    @PreAuth
    @GetMapping("/export/state")
    @ApiOperation(value = "导出文件任务进度的状态")
    @ApiImplicitParam(name = "syncId", required = true, value = "状态ID")
    public Result<ExportFileSyncState> syncState(String syncId) {
        return Result.data(exportFileSyncContext.getTaskState(syncId));
    }
}
