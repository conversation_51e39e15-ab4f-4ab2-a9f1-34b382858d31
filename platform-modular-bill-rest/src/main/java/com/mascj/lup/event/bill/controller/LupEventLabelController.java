package com.mascj.lup.event.bill.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.util.$;
import com.mascj.kernel.database.entity.Search;
import com.mascj.lup.event.bill.vo.LupCommonTreeVO;
import com.mascj.lup.event.bill.vo.LupEventLabelVo;
import io.swagger.annotations.*;
import lombok.extern.java.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.mascj.lup.event.bill.entity.LupEventLabel;
import com.mascj.lup.event.bill.service.LupEventLabelService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.GetMapping;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 *
 *
 * <AUTHOR> @since 2024-01-05 14:36:06
 */
@RestController
@RequestMapping("/api/lupEventLabel")
@Api(tags = "配置管理控制器")
public class LupEventLabelController {

    @Autowired
    private LupEventLabelService lupEventLabelService;

    @PreAuth
    @GetMapping("/labelPage")
    @ApiOperation(value = "标签配置列表", notes = "标签配置列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "current", required = true, value = "当前页", paramType = "form"),
            @ApiImplicitParam(name = "size", required = true, value = "每页显示数据", paramType = "form"),
            @ApiImplicitParam(name = "codeValue", required = true, value = "字典项 eventOrigin", paramType = "form"),
    })
    public Result<IPage<LupEventLabelVo>> listPage(Integer codeValue , @ApiIgnore Search search) {
        return lupEventLabelService.listPage(codeValue,search);
    }


    @PreAuth
    @PostMapping("/save")
    @ApiOperation(value = "标签配置保存", notes = "标签配置保存")
    public Result saveConfig(@RequestBody LupEventLabelVo lupEventLabelVo) {
        return lupEventLabelService.saveConfig(lupEventLabelVo);
    }

//    @PreAuth
//    @PostMapping("/premissionList")
//    @ApiOperation(value = "用户ids", notes = "用户ids")
//    public Result userIds(Long labelId) {
//        return Result.data(lupEventLabelService.premissionList(labelId));
//    }

    @PreAuth
    @PostMapping("/labelIdsByUserIds")
    @ApiOperation(value = "根据用户ids获取labelIds", notes = "根据用户ids获取labelIds")
    public Result<List<Long>> labelIdsByUserIds(String userId) {
        return Result.data(lupEventLabelService.labelIdsByUserIds($.toLongList(userId)));
    }

    @PreAuth
    @GetMapping("/listLabelTree")
    @ApiOperation(value = "标签树", notes = "标签树")
    public Result<List<LupCommonTreeVO>> listLabelTree(){
        return Result.data(lupEventLabelService.listLabelTree());
    }

}