package com.mascj.lup.event.bill.provider;

import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.LupAppAuthCodeDTO;
import com.mascj.lup.event.bill.dto.LupAppSignDTO;
import com.mascj.lup.event.bill.entity.LupApp;
import com.mascj.lup.event.bill.feign.ILupAppFeign;
import com.mascj.lup.event.bill.service.ILupAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/1/13 17:03
 * @describe
 */
@Api(value = "三方App应用管理",tags = "三方App应用管理")
@RestController
@AllArgsConstructor
public class LupAppFeignProvider  implements ILupAppFeign {

    private ILupAppService appService;


    @ApiOperation(value = "激活授权码")
    @PostMapping("/support/app/active/auth/code")
    @Override
    public Result<LupApp> activeAppAuthCode(LupAppAuthCodeDTO authCodeDTO) {
        return appService.activeAppAuthCode(authCodeDTO);
    }

    @ApiOperation(value = "验证APP签名")
    @PostMapping("/support/app/valid")
    @Override
    public Result validApp(LupAppSignDTO appSignDTO) {
        return appService.validApp(appSignDTO);
    }

    @ApiOperation(value = "创建应用签名")
    @GetMapping("/support/app/sign/create")
    @Override
    @PreAuth
    public Result<String> createSign(String appKey) {
        return Result.data(appService.createSign(appKey));
    }
}
