package com.mascj.lup.event.bill.controller;


import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.*;

import com.mascj.lup.event.bill.service.ILupBillService;
import com.mascj.lup.event.bill.util.SpringUtils;
import com.mascj.lup.event.bill.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * 工单数据表 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */

@RestController
@RequestMapping("/lup-bill")
@AllArgsConstructor
@Api(value = "事件管理功能",tags = "事件管理功能")
public class LupBillController {

    private final ILupBillService iLupBillService;

    @PreAuth
    @ApiOperation(value = "统计工单")
    @PostMapping(value = "/countBill")
    public Result<List<BillCountVO>> countBill(@RequestBody  ListUnitDTO billDTO){
        return iLupBillService.countBill(billDTO);
    }

    @PreAuth
    @ApiOperation(value = "根据网格查询工单列表")
    @ApiImplicitParam(value = "网格id",name = "unitId")
    @PostMapping(value = "/listBill")
    public Result<List<BillDataVO>> listBill(@RequestBody ListUnitDTO unitDTO){
        return Result.data(iLupBillService.listBill(unitDTO));
    }

    @PreAuth
    @ApiOperation(value = "根据工单id 查询工单详情")
    @ApiImplicitParam(value = "工单id",name = "billId")
    @GetMapping(value = "/detailByBillId")
    public Result<BillDetailVO> detailByBillId(@RequestParam(value = "billId") Long billId){
            return iLupBillService.detailByBillId(billId);
    }

    @PreAuth
    @ApiOperation(value = "分页查询事件")
    @ApiImplicitParam(value = "工单id",name = "billId")
    @PostMapping(value = "/pagedBill")
    public Result<IPage<BillSearchVO>> pagedBill(@RequestBody BillSearchDTO billSearchDTO){

        IPage<BillSearchVO> pages = iLupBillService.pagedBill(new Page<>(billSearchDTO.getCurrent(),billSearchDTO.getSize()), billSearchDTO);

        return Result.data(pages);
    }

    @PreAuth
    @ApiOperation(value = "默认展示近一个月的日期")
    @GetMapping(value = "/lastMonthDate")
    public Result<DateRangeVO> lastMonthDate(){
        DateRangeVO dateRangeVO = new DateRangeVO();
        dateRangeVO.setStartDate(DateUtil.format(DateUtil.lastMonth(),"yyyy-MM-dd"));
        dateRangeVO.setEndDate(DateUtil.format(new Date(),"yyyy-MM-dd"));

        return Result.data(dateRangeVO);
    }

    @PreAuth
    @ApiOperation(value = "按标签统计数据")
    @ApiImplicitParam(value = "工单id",name = "billId")
    @PostMapping(value = "/tagStateCount")
    public Result<List<TagStateCountVo>> tagMetadata(@RequestBody BillSearchDTO billSearchDTO){

        List<TagStateCountVo> billSearchVO  = iLupBillService.tagMetadata(billSearchDTO);

        return Result.data(billSearchVO);
    }

    /**
     * 工单状态数量统计
     *
     * @param search
     * @return
     */
    @PreAuth
    @PostMapping("/stateCount")
    @ApiOperation(value = "工单状态数量统计", notes = "工单状态数量统计")
    public Result<List<BillStateCountVo>> billStateCount(@RequestBody BillSearchDTO search) {
        return Result.data(iLupBillService.billStateCount(search));
    }

    @PreAuth
    @PostMapping("/switchGridUnit")
    @ApiOperation(value = "切换工单网格", notes = "切换工单网格")
    public Result switchGridUnit(@RequestBody BillSwitchGridDTO switchGridDTO) {
        return Result.condition(iLupBillService.switchGridUnit(switchGridDTO));
    }


    @PreAuth
    @GetMapping("/dimensionEvent")
    @ApiOperation(value = "事件中心点加载到标注", notes = "事件中心点加载到标注")
    public Result dimensionEvent(@RequestParam Long billId) {
        return Result.condition(iLupBillService.dimensionEvent(billId));
    }

    @PreAuth
    @GetMapping("/cancelDimensionEvent")
    @ApiOperation(value = "取消标记点", notes = "取消标记点")
    public Result cancelDimensionEvent(@RequestParam Long billId) {
        return Result.condition(iLupBillService.cancelDimensionEvent(billId));
    }

    @PreAuth
    @ApiOperation(value = "重新定位工单网格")
    @PostMapping(value = "/initUnLocateGridUnitBill")
    public Result initUnLocateGridUnitBill(){
        return iLupBillService.initUnLocateGridUnitBill();
    }

    @PreAuth
    @ApiOperation(value = "切换工单是否可用")
    @PostMapping(value = "/switchBillEnable")
    public Result switchBillEnable(@RequestBody @Validated SwitchBillEnableDTO switchBillEnableDTO){
        return Result.condition(iLupBillService.switchBillEnable(switchBillEnableDTO));
    }

    @PreAuth
    @ApiOperation(value = "修改工单事件类型")
    @PostMapping(value = "/changeBillEventType")
    public Result changeBillEventType(@RequestBody @Validated BillEventTypeChangeDTO billEventTypeChangeDTO){
        return Result.condition(iLupBillService.changeBillEventType(billEventTypeChangeDTO));
    }


}

