package com.mascj.lup.event.bill.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.common.util.StringUtil;
import com.mascj.kernel.web.tree.ForestNodeMerger;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.dto.GridUnitEditDTO;
import com.mascj.lup.event.bill.dto.ListUnitDTO;
import com.mascj.lup.event.bill.dto.UnitDTO;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupGridUnit;
import com.mascj.lup.event.bill.service.ILupBillService;
import com.mascj.lup.event.bill.service.ILupGridUnitService;
import com.mascj.lup.event.bill.util.ProjectUtil;
import com.mascj.lup.event.bill.vo.GriUnitVO;
import com.mascj.lup.event.bill.vo.GridUnitManageVO;
import com.mascj.lup.event.bill.vo.GridUnitShapeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 网格数据表 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Api(value = "网格管理",tags = "网格数据管理")
@RestController
@AllArgsConstructor
@RequestMapping("/lupGridUnit")
public class LupGridUnitController {

    private ILupGridUnitService iLupGridUnitService;

    private ILupBillService iLupBillService;

    /**
     * 网格文件上传
     *
     * @param file 对象
     * @return Result
     */
    @PreAuth
    @PostMapping("/upload")
    @ApiOperation(value = "网格文件上传", notes = "网格文件上传")
    public Result upload(@RequestPart("file") MultipartFile file) {
        iLupGridUnitService.importData(file);
        return Result.data(null);
    }

    @PreAuth
    @PostMapping("/modifyGridUnit")
    @ApiOperation(value = "修改网格数据", notes = "修改网格数据")
    public Result modifyGridUnit(@RequestBody GridUnitEditDTO editDTO ) {

        Assert.isTrue(editDTO.getGridName() != null && !"".equals(editDTO.getGridName()),LocaleMessageUtil.getMessage(EventTipKey.NoneGridUnitName));

        LupGridUnit gridUnit = new LupGridUnit();

        gridUnit.setId(editDTO.getId());
        gridUnit.setName(editDTO.getGridName());
        gridUnit.setSort(editDTO.getSort());
        iLupGridUnitService.updateById(gridUnit);

        return Result.data(null);
    }


    @PreAuth
    @PostMapping("/delGridUnit")
    @ApiOperation(value = "删除网格数据", notes = "删除网格数据")
    @ApiImplicitParam(value = "网格id",name = "unitId")
    public Result delGridUnit(
            @RequestBody UnitDTO unitDTO) {

        int c = iLupBillService.count(Wrappers.<LupBill>lambdaQuery().eq(LupBill::getDeleted,0).eq(LupBill::getUsable, 1)
                .eq(LupBill::getProjectId,ProjectUtil.getProjectId())
        .eq(LupBill::getGridUnitId,unitDTO.getUnitId())
        );

        System.out.println("工单数量:"+c+" boolean:"+(c==0));

        Assert.isTrue(c==0, LocaleMessageUtil.getMessage(EventTipKey.FailDelGrid));


        int childCount =  iLupGridUnitService.count(Wrappers.<LupGridUnit>lambdaQuery().eq(LupGridUnit::getIsDeleted,0)
                .eq(LupGridUnit::getProjectId,ProjectUtil.getProjectId())
        .eq(LupGridUnit::getPid,unitDTO.getUnitId()));

        System.out.println("子级网格数量:"+childCount+" boolean:"+(childCount==0));
        Assert.isTrue(childCount==0,LocaleMessageUtil.getMessage(EventTipKey.FailDelGridChild));


        LupGridUnit gridUnit = new LupGridUnit();

        gridUnit.setId(unitDTO.getUnitId());
        gridUnit.setStatus(false);
        gridUnit.setIsDeleted(1);

        iLupGridUnitService.updateById(gridUnit);

        return Result.data(LocaleMessageUtil.getMessage(EventTipKey.CommonDealComplete));
    }

    @PreAuth
    @GetMapping("/listGridUnit")
    @ApiOperation(value = "网格管理-查询网格数据列表", notes = "网格管理-查询网格数据列表")
    public  Result<List<GridUnitManageVO>> listGridUnit(

            @RequestParam(value = "flag",required = false) String flag,
            @RequestParam(value = "isSearch",required = false) Boolean isSearch
            ){
        List<GridUnitManageVO> finallyUit = new ArrayList<>();
        List<LupGridUnit> allUnit = null;

        allUnit = iLupGridUnitService.allUnit();

        if(flag!=null && "1".equals(flag) &&allUnit != null){

            //计算allUnit 的根节点
//            List<GridUnitManageVO> tmpFinallyUit = new ArrayList<>();
//
//
//            for (int i = 0; i < allUnit.size(); i++) {
//                LupGridUnit pUnit = iLupGridUnitService.getById(allUnit.get(i).getPid());
//                if(pUnit!=null)
//                    allUnit.add(pUnit);
//            }
        }





        Map<Long,LupGridUnit> unitMap = null;
        if(allUnit == null) {
            allUnit = iLupGridUnitService.list(Wrappers.<LupGridUnit>lambdaQuery()
                    .eq(ProjectUtil.getProjectId() != null, LupGridUnit::getProjectId, ProjectUtil.getProjectId())
                    .eq(LupGridUnit::getIsDeleted, 0)
                    .ne(isSearch!=null && !isSearch,LupGridUnit::getCode, VariableConstants.UNKNOWN_AREA_CODE)
                    .orderByAsc(LupGridUnit::getSort)
                    .select(LupGridUnit::getId, LupGridUnit::getPid, LupGridUnit::getName, LupGridUnit::getCode, LupGridUnit::getGridArea, LupGridUnit::getSort)
            );
        }else{
            List<Long> unitIdList = allUnit.stream().map(LupGridUnit::getId).collect(Collectors.toList());

            if(unitIdList.size()>0) {
                List<LupGridUnit> list = iLupGridUnitService.list(Wrappers.<LupGridUnit>lambdaQuery()
                        .eq(LupGridUnit::getIsDeleted, 0).in(LupGridUnit::getId,unitIdList).select(LupGridUnit::getId,LupGridUnit::getName));

                unitMap = list.stream().collect(Collectors.toMap(LupGridUnit::getId, e->e));

            }

        }


        for (LupGridUnit item : allUnit) {
            GridUnitManageVO gridUnitManageVO = new GridUnitManageVO();

            gridUnitManageVO.setId(item.getId());
            gridUnitManageVO.setParentId(item.getPid());
            gridUnitManageVO.setGridCode(item.getCode());

            gridUnitManageVO.setGridName(item.getName());

            if(unitMap!=null && unitMap.get(item.getId()) != null){

                LupGridUnit gridUnit = unitMap.get(item.getId());

                gridUnitManageVO.setGridName(gridUnit.getName());

            }

            gridUnitManageVO.setSort(item.getSort());
            gridUnitManageVO.setLocateArea(item.getGridArea());


            if(gridUnitManageVO.getGridName().equals(EventTipKey.UnknownAreaName)){
                gridUnitManageVO.setGridName(LocaleMessageUtil.getMessage(EventTipKey.UnknownAreaName));
            }

            finallyUit.add(gridUnitManageVO);
        }

        List<GridUnitManageVO> list = ForestNodeMerger.merge(finallyUit);


        return Result.data(list);
    }


    @PreAuth
    @PostMapping("/listUnit")
    @ApiOperation(value = "综合展示-懒加载查询网格数据列表", notes = "综合展示-懒加载查询网格数据列表")
    public  Result<List<GriUnitVO>> listUnit(@RequestBody ListUnitDTO unitDTO){
        return iLupGridUnitService.listUnit(unitDTO);
    }

    @PreAuth
    @PostMapping("/listChildUnit")
    @ApiOperation(value = "综合展示-懒加载查询子节点网格数据列表", notes = "综合展示-懒加载查询网格数据列表 图形数据")
    public  Result<GridUnitShapeVO> listChildUnit(@RequestBody ListUnitDTO unitDTO){
        return iLupGridUnitService.listChildUnit(unitDTO);
    }

}

