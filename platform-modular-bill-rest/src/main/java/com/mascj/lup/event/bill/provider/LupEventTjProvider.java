package com.mascj.lup.event.bill.provider;

import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.feign.ILupEventTjFeign;
import com.mascj.lup.event.bill.service.EventTjService;
import com.mascj.lup.event.bill.vo.FlyEventTjQueryVO;
import com.mascj.lup.event.bill.vo.FlyEventTjResVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "查询事件的统计Feign",tags = "查询事件的统计Feign")
@AllArgsConstructor
@RestController
public class LupEventTjProvider implements ILupEventTjFeign {

    @Autowired
    private EventTjService eventTjService;

    @ApiOperation(value = "项目的事件统计")
    @Override
    @PostMapping(value = "/provider/v3/lupEventFeign/eventTj")
    public Result<FlyEventTjResVO> eventTj(FlyEventTjQueryVO flyEventTjQueryVO) {


        return eventTjService.eventTj(flyEventTjQueryVO);
    }
}
