package com.mascj.lup.event.bill.controller.v10;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.LupDataPushLogPagedDTO;
import com.mascj.lup.event.bill.dto.PushDataDefaultDTO;
import com.mascj.lup.event.bill.service.ILupDataPushLogService;
import com.mascj.lup.event.bill.util.SignatureUtils;
import com.mascj.lup.event.bill.vo.LupDataPushLogPagedItemVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2025/1/9 15:23
 * @describe
 */
@RestController
@RequestMapping("/lup-data-push-log")
@AllArgsConstructor
@Api(value = "事件数据推送日志",tags = "事件数据推送日志")
public class LupDataPushLogController {

    @Autowired
    private HttpServletRequest request;

    private final ILupDataPushLogService dataPushLogService;

    @PreAuth
    @ApiOperation(value = "分页查询推送记录")
    @PostMapping(value = "/pagedDataPushLog")
    public Result<Page<LupDataPushLogPagedItemVO>> pagedDataPushLog(@RequestBody LupDataPushLogPagedDTO dataPushLogPagedDTO){

        return Result.data(dataPushLogService.pagedDataPushLog(dataPushLogPagedDTO));

    }

    @ApiOperation(value = "【成功】接收数据")
    @PostMapping(value = "/receiveData")
    public Result receiveData(@RequestBody PushDataDefaultDTO defaultDTO){

        System.out.println("JSONUtil.toJsonStr(defaultDTO):"+JSONUtil.toJsonStr(defaultDTO));

        return Result.success("数据接收成功");
    }

    @ApiOperation(value = "【失败】接收数据 【签名正确的话 返回 数据接收成功】")
    @PostMapping(value = "/pagedDataPushLogForFailTest")
    public Result pagedDataPushLogForFailTest(@RequestBody PushDataDefaultDTO defaultDTO){

        String Signature = request.getHeader("Signature");

        String requestApi = request.getHeader("Request-Api");
        Long timestamp = Long.parseLong( request.getHeader("Timestamp"));
        String nonce = request.getHeader("Nonce");
        String appKey = request.getHeader("appId");
        String appSecret = "bbcf86ff6f3f47be8ba58320c10a28d4";


        try{
            String signature = SignatureUtils.methodSignature(appKey, appKey, appSecret, timestamp, nonce, requestApi, JSONObject.toJSONString(defaultDTO));
            if(signature.equals(Signature)){
                return Result.success("[签名正确]数据接收成功");
            }
        }catch (Exception exception){
            exception.printStackTrace();
        }

        System.out.println("JSONUtil.toJsonStr(defaultDTO):"+JSONUtil.toJsonStr(defaultDTO));

        return Result.fail("测试接收数据失败");
    }

}
