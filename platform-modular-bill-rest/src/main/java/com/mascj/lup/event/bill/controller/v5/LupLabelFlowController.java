package com.mascj.lup.event.bill.controller.v5;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.database.entity.Search;
import com.mascj.lup.event.bill.dto.LabelFlowBatchSaveDTO;
import com.mascj.lup.event.bill.dto.LabelFlowSaveDTO;
import com.mascj.lup.event.bill.service.ILupLabelFlowService;
import com.mascj.lup.event.bill.vo.LupLabelFlowTimeLimitVO;
import com.mascj.lup.event.bill.vo.LupLabelFlowVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/5 11:36
 * @describe
 */

@RestController
@RequestMapping("/lup-bill-notification")
@AllArgsConstructor
@Api(value = "事件标签流程期限配置",tags = "事件标签流程期限配置")
public class LupLabelFlowController {

    private final ILupLabelFlowService labelFlowService;

    @PreAuth
    @GetMapping("/labelFlowPage")
    @ApiOperation(value = "标签配置列表", notes = "标签配置列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "current", required = true, value = "当前页", paramType = "form"),
            @ApiImplicitParam(name = "size", required = true, value = "每页显示数据", paramType = "form"),
            @ApiImplicitParam(name = "codeValue", required = true, value = "字典项 eventOrigin", paramType = "form"),
    })
    public Result<IPage<LupLabelFlowVO>> labelFlowPage(Integer codeValue , @ApiIgnore Search search) {
        return labelFlowService.labelFlowPage(codeValue,search);
    }


    @PreAuth
    @GetMapping("/labelFlowDetail")
    @ApiOperation(value = "标签配置详情", notes = "标签配置详情")
    public Result<LupLabelFlowTimeLimitVO> labelFlowDetail(Long id) {
        return Result.data( labelFlowService.labelFlowDetail(id));
    }



    @PreAuth
    @PostMapping("/batchSaveLabelFlow")
    @ApiOperation(value = "批量保存标签数据", notes = "批量保存标签数据")
    public Result batchSaveLabelFlow(@RequestBody LabelFlowBatchSaveDTO labelFlowBatchSaveDTO) {
        return Result.condition( labelFlowService.batchSaveLabelFlow(labelFlowBatchSaveDTO));
    }

    @PreAuth
    @PostMapping("/saveLabelFlow")
    @ApiOperation(value = "保存标签数据", notes = "保存标签数据")
    public Result saveLabelFlow(@RequestBody LabelFlowSaveDTO labelFlowSaveDTO) {
        return Result.condition( labelFlowService.saveLabelFlow(labelFlowSaveDTO));
    }

}
