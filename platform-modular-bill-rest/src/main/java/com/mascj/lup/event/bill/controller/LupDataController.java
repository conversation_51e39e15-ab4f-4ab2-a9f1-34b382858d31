package com.mascj.lup.event.bill.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.EventDataTaskQueryDTO;
import com.mascj.lup.event.bill.service.IEventDataQueryService;
import com.mascj.lup.event.bill.vo.EventDataTaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 事件数据 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Api(tags = "事件数据 前端控制器")
@RestController
@AllArgsConstructor
@RequestMapping("/lupData")
public class LupDataController {

    private final IEventDataQueryService iEventDataQueryService;
    @ApiOperation(value = "根据任务查询事件数据")
    @PreAuth
    @PostMapping("/pageByTask")
    public Result<IPage<EventDataTaskVO>> pageByTask(@RequestBody EventDataTaskQueryDTO eventDataTaskQueryDTO){
        return Result.data(iEventDataQueryService.pageByTask(new Page<>(eventDataTaskQueryDTO.getCurrent(),eventDataTaskQueryDTO.getSize()),eventDataTaskQueryDTO));
    }
}

