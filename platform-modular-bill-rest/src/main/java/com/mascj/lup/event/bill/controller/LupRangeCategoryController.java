package com.mascj.lup.event.bill.controller;


import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.LupRangeCategoryAddDTO;
import com.mascj.lup.event.bill.service.ILupRangeCategoryService;
import com.mascj.lup.event.bill.service.ILupRangeLineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 范围线类目  前端控制器
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Api(tags = "范围线类目管理")
@RestController
@RequestMapping("/lupRangeCategory")
@AllArgsConstructor
public class LupRangeCategoryController {

    private final ILupRangeCategoryService rangeCategoryService;

    @PreAuth
    @PostMapping(value = "/add")
    @ApiOperation(value = "新增范围线类型", notes = "新增范围线类型")
    public Result add(LupRangeCategoryAddDTO rangeCategoryAddDTO){
        return Result.data(rangeCategoryService.add(rangeCategoryAddDTO));
    }

    @PreAuth
    @GetMapping(value = "/listAll")
    @ApiOperation(value = "查询类型tree", notes = "查询类型tree")
    public Result listAll(){
        return Result.data(rangeCategoryService.listAll());
    }

}

