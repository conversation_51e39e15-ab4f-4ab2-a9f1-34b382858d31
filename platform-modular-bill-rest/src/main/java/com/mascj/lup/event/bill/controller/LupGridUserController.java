package com.mascj.lup.event.bill.controller;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.web.tree.ForestNodeMerger;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.dto.*;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupGridUnit;
import com.mascj.lup.event.bill.entity.LupGridUser;
import com.mascj.lup.event.bill.service.ILupBillService;
import com.mascj.lup.event.bill.service.ILupGridUnitService;
import com.mascj.lup.event.bill.service.ILupGridUserService;
import com.mascj.lup.event.bill.util.ProjectUtil;
import com.mascj.lup.event.bill.vo.GriUnitVO;
import com.mascj.lup.event.bill.vo.GridUnitManageVO;
import com.mascj.lup.event.bill.vo.GridUnitShapeVO;
import com.mascj.lup.event.bill.vo.LupGridUserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;

/**
 * <p>
 * 网格-用户数据表 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Api(value = "网格-用户数据管理",tags = "网格-用户数据管理")
@RestController
@AllArgsConstructor
@RequestMapping("/lupGridUser")
public class LupGridUserController {

    @Qualifier("syncProduceImageExecutorPool")  //指定某个bean
    private final Executor syncProduceImageExecutorPool;

    private final ILupGridUserService gridUserService;
    private final ILupGridUnitService gridUnitService;
    @PreAuth
    @PostMapping("/bindUserGrid")
    @ApiOperation(value = "给网格绑定用户", notes = "给网格绑定用户")
    public Result bindUserGrid(@RequestBody LupGridUserDTO gridUserDTO) {

        List<LupGridUser> gridUserList = gridUserService.validAddGridUser(gridUserDTO);

        List<LupGridUnit> gridUnitList = gridUnitService.listByUnitId(gridUserDTO.getGridUnitId());

        List<LupUserDTO> addRes = gridUserService.addGridUser(gridUserList,gridUserDTO,gridUnitList);

        String tenantId = LmContextHolder.getTenantId();
        Long userId = LmContextHolder.getUserId();



                    //存储redis数据
                    addRes.forEach(item -> {
                        syncProduceImageExecutorPool.execute(new Runnable() {
                            @Override
                            public void run() {
                                LmContextHolder.setUserId(userId);
                                LmContextHolder.setTenantId(tenantId);
                                try {
                            gridUnitService.allUnit(item.getId());
                            Thread.sleep(200);
                                }catch (Exception exception ){
                                    exception.printStackTrace();
                                }
                            }
                        });
                    });



        return Result.success(LocaleMessageUtil.getMessage(EventTipKey.ConfigGridUser));
    }

    @PreAuth
    @GetMapping("/getGridUserList")
    @ApiOperation(value = "根据网格获取网格用户", notes = "根据网格获取网格用户")
    public Result<LupGridUserVO> getGridUserList(@RequestParam(value = "gridUnitId") Long gridUnitId){
        return Result.data(gridUserService.getGridUserList(gridUnitId));
    }

}

