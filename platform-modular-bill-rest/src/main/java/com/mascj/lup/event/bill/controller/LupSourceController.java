package com.mascj.lup.event.bill.controller;


import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.service.ILupSourceService;
import com.mascj.lup.event.bill.vo.SourceTileVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 事件资源记录表 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@RestController
@RequestMapping("/lupSource")
@AllArgsConstructor
@Api(value = "图层资源",tags = "图层资源")
public class LupSourceController {

    private final ILupSourceService iLupSourceService;

    @PreAuth
    @ApiOperation(value = "查询资源列表")
    @PostMapping(value = "/listSourceTile")
    public Result<List<SourceTileVO>> listSourceTile(@RequestBody BillDTO billDTO){

        return iLupSourceService.listSourceTile(billDTO);
    }
}

