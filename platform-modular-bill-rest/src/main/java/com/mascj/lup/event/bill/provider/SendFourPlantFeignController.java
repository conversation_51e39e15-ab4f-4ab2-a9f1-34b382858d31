package com.mascj.lup.event.bill.provider;

import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.feign.SendFourPlantFeign;
import com.mascj.lup.event.bill.service.IDataReceiveService;
import com.mascj.lup.event.bill.vo.SendFourDoVo;
import com.mascj.lup.event.bill.vo.SendFourResutVo;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "推送四平台",tags = "推送四平台")
@AllArgsConstructor
@RestController
public class SendFourPlantFeignController implements SendFourPlantFeign {

    @Autowired
    private IDataReceiveService dataReceiveService;

    /**
     * 根据流程发送数据到第三方
     * @param sendFourDoVo
     * @return
     */
    @Override
    @PostMapping("/provider/support/doSendFour")
    public Result<SendFourResutVo> doSendFour(SendFourDoVo sendFourDoVo) {
        return dataReceiveService.pushEventDataFour(sendFourDoVo);
    }
}
