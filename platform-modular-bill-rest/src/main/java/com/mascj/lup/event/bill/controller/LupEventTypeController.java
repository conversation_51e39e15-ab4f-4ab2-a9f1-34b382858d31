package com.mascj.lup.event.bill.controller;


import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.dto.ListUnitDTO;
import com.mascj.lup.event.bill.service.ILupLabelService;
import com.mascj.lup.event.bill.vo.BillCountVO;
import com.mascj.lup.event.bill.vo.LupLabelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 事件数据标签关联记录表 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@RestController
@AllArgsConstructor
@Api(value = "事件类型管理功能",tags = "事件管事件类型管理功能理功能")
@RequestMapping("/lupEventType")
public class LupEventTypeController {

    private final ILupLabelService labelService;


    @PreAuth
    @ApiOperation(value = "查询所有的事件类型")
    @PostMapping(value = "/allEventType")
    public Result<List<LupLabelVO>> allEventType(){
        return Result.data(labelService.allEventType());
    }

}

