package com.mascj.lup.event.bill.controller;

import cn.hutool.json.JSONUtil;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.lup.event.bill.dto.EventDataDTO;
import com.mascj.lup.event.bill.dto.FlySystemEventDataSourceDTO;
import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.service.IDataReceiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@PreAuth
@RestController
@Api(value = "数据服务",tags = "数据服务")
@RequestMapping("/dataReceive")
@AllArgsConstructor
public class DataReceiveController {

    private final IDataReceiveService iDataReceiveService;


//    /**
//     * 方法废弃不适用了 张保国  2024-01-10
//     * @param billId
//     * @return
//     */
//    @Deprecated
//    @PreAuth
//    @ApiOperation(value = "推送数据")
//    @PostMapping("/support/pushData/{billId}")
//    public Result pushData(@PathVariable("billId") Long billId){
//        return iDataReceiveService.pushEventData(billId);
//    }

    @ApiOperation(value = "【无人机】提交事件数据")
    @PostMapping("/support/postFlyEventData")
    public Result postFlyEventData(@RequestBody FlySystemEventDataSourceDTO dataSourceDTO){
        return iDataReceiveService.postCompareDataSystem(dataSourceDTO);
    }

    @ApiOperation(value = "【比对系统】提交事件数据")
    @PostMapping("/support/postEventData")
    public Result postEventData(@RequestBody EventDataDTO eventData){
        return iDataReceiveService.postEventData(eventData);
    }

}
