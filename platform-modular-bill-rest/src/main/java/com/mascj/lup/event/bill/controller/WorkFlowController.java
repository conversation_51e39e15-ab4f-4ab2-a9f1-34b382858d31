package com.mascj.lup.event.bill.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.kernel.auth.annotation.PreAuth;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.kernel.redis.core.RedisService;
import com.mascj.kernel.tools.RedisKey;
import com.mascj.kernel.tools.RedisPlane;
import com.mascj.lup.event.bill.constant.Constants;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.VariableConstants;
import com.mascj.lup.event.bill.dto.BillDTO;
import com.mascj.lup.event.bill.dto.BillSearchDTO;
import com.mascj.lup.event.bill.dto.PushKQDTO;
import com.mascj.lup.event.bill.entity.LupBill;
import com.mascj.lup.event.bill.entity.LupData;
import com.mascj.lup.event.bill.enums.PushEventFlowEnum;
import com.mascj.lup.event.bill.feign.IEventDataQueryProvider;
import com.mascj.lup.event.bill.feign.LupDataNotificationFeign;
import com.mascj.lup.event.bill.provider.EventDataQueryProvider;
import com.mascj.lup.event.bill.service.*;
import com.mascj.lup.event.bill.service.impl.LupBillServiceImpl;
import com.mascj.lup.event.bill.util.ReadConfigUtil;
import com.mascj.lup.event.bill.util.RedisOpsUtils;
import com.mascj.lup.event.bill.vo.*;
import com.mascj.lup.event.bill.vo.config.HandleModuleConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static com.mascj.kernel.common.util.LocaleMessageUtil.getMessage;


/**
 * <p>
 * 工单数据表 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2023-08-11
 */
@Api(value = "测试流程",tags = "测试流程")
@RestController
@AllArgsConstructor
@RequestMapping("lup/work")
public class WorkFlowController {

    private final EventDataQueryProvider iEventDataQueryProvider;
    private ILupFlowService lupFlowService;
    private final LupDataNotificationFeign dataNotificationFeign;
    private final ILupBillPendingService billPendingService;
    private final ReadConfigUtil readConfigUtil;

    private final IDataReceiveService pushDataService;

    private final ILupDataPushLogService dataPushLogService;

    private final ILupBillService billService;

    @PreAuth
    @ApiOperation(value = "完成任务的方法")
    @PostMapping(value = "/submitTask")
    public Result submitTask(  @RequestBody LupSubmitForm submitTaskDto){

        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();
        PushKQDTO pushKQDTO = handleModuleConfigVO.getPushKQDTO();
        PushEventFlowEnum pushEventFlowEnum = PushEventFlowEnum.parse(pushKQDTO.getPushEventFlow());

        billPendingService.clearBillCache(submitTaskDto.getWorkBillId());
        //配置是推送流程的情况 或者  不是推送按钮提交的
        if(pushEventFlowEnum == PushEventFlowEnum.PushEventFlow || !submitTaskDto.getPushBtn())
        {
            //推送流程进入  非推送按钮的提交也直接进入
            Long flowId = submitTaskDto.getFlowId();
            String key = Constants.LOCAL_PROJECT_NAME + ":submitTask:" + flowId;
            Boolean lockLua = RedisOpsUtils.getLockLua(key, 60L);
            try {
                if (lockLua) {
                    lupFlowService.submitTask(submitTaskDto);

                    //根据工单ID 取消事件工单的挂起状态  还原 流程期限状态
                    billPendingService.resetBillPending(submitTaskDto.getWorkBillId());

                    Result<EventDataVO> data = iEventDataQueryProvider.detail(submitTaskDto.getWorkBillId());


                    dataNotificationFeign.eventDataOnChanged(data);
                }
            } finally {
                if (lockLua) {
                    RedisOpsUtils.unLockLua(key);
                }
            }
        }else{
            //代码记录推送数据

            //查到dataID：
            Long dataId=0L;

            if(submitTaskDto.getPushLogId()!=null && submitTaskDto.getPushLogId() > 0){
                dataId = dataPushLogService.getById(submitTaskDto.getPushLogId()).getDataId();
            }else{
                dataId = billService.getById(submitTaskDto.getWorkBillId()).getDataId();
            }


            pushDataService.pushEventDataToOtherServer(dataId);
        }
        return Result.success(getMessage(EventTipKey.CommonSuccess));
    }

    @PreAuth
    @ApiOperation(value = "结束流程的方法, 传flowId就行")
    @PostMapping(value = "/endTask")
    public Result endTask(  @RequestBody LupSubmitForm submitTaskDto){
        HandleModuleConfigVO handleModuleConfigVO = readConfigUtil.readHandleModuleConfig();
        PushKQDTO pushKQDTO = handleModuleConfigVO.getPushKQDTO();
        PushEventFlowEnum pushEventFlowEnum = PushEventFlowEnum.parse(pushKQDTO.getPushEventFlow());
        billPendingService.clearBillCache(submitTaskDto.getWorkBillId());
        if(pushEventFlowEnum == PushEventFlowEnum.PushEventFlow || !submitTaskDto.getPushBtn()) {
            Long flowId = submitTaskDto.getFlowId();
            String key = Constants.LOCAL_PROJECT_NAME + ":submitTask:" + flowId;
            Boolean lockLua = RedisOpsUtils.getLockLua(key, 60L);
            try {
                if (lockLua) {
                    lupFlowService.endTask(submitTaskDto);
                } else {
                    return Result.fail("Processed");
                }
            } finally {
                if (lockLua) {
                    RedisOpsUtils.unLockLua(key);
                }
            }
        }else{
            //查到dataID：
            Long dataId = billService.getById(submitTaskDto.getWorkBillId()).getDataId();
            pushDataService.doesNotPushEventData(dataId);

        }
        return Result.success("处理成功");
    }
}

