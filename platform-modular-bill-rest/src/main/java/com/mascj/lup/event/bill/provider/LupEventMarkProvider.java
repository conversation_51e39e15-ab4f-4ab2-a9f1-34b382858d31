package com.mascj.lup.event.bill.provider;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.context.LmContextHolder;
import com.mascj.lup.event.bill.entity.LupEventMark;
import com.mascj.lup.event.bill.feign.LupEventMarkFeign;
import com.mascj.lup.event.bill.service.LupEventMarkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Classname LupEventMarkProvider
 * @Description TODO
 * @Version 1.0.0
 * @Date 2024/1/9 17:45
 * @Created by lionel
 */
@Api(value = "标注服务Feign",tags = "标注服务Feign")
@RestController
@AllArgsConstructor
public class LupEventMarkProvider implements LupEventMarkFeign {
    @Autowired
    private LupEventMarkService lupEventMarkService;

    @Override
    @ApiOperation(value = "根据标注点删除")
    @DeleteMapping("/provider/v1/lupEventMarkFeign/deleteByMarkId")
    public Result deleteByMarkId(Long markId) {
        boolean remove = lupEventMarkService.remove(new LambdaQueryWrapper<LupEventMark>().eq(LupEventMark::getMarkId, markId));
        return Result.condition(remove);
    }

    @Override
    @DeleteMapping(value = "/provider/v1/lupEventMarkFeign/deleteByMarkIds")
    public Result deleteByMarkIds(@RequestParam("markIds") List<Long> markIds , @RequestParam("tenantId") Long tenantId) {
        LmContextHolder.setTenantId(tenantId.toString());
        boolean remove = lupEventMarkService.remove(new LambdaQueryWrapper<LupEventMark>().in(LupEventMark::getMarkId, markIds));
        return Result.condition(remove);
    }
}
