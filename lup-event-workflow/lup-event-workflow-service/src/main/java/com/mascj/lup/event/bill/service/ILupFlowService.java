package com.mascj.lup.event.bill.service;

import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.vo.LupSubmitForm;

import java.util.Map;

public interface ILupFlowService {


    /**
     * @param billId       工单Id
     * @param formData 启动表单
     */
    Result<String> startupAepFlow(Long billId, Map<String, Object> formData);
    void submitTask(LupSubmitForm submitTaskDto);


    void submitTaskByProcessId(LupSubmitForm submitTaskDto);


    void endTask(LupSubmitForm submitTaskDto);
}
