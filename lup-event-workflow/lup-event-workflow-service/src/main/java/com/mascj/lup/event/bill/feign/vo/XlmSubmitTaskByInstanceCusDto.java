package com.mascj.lup.event.bill.feign.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 任务提交
 */
@EqualsAndHashCode
@Data
@ApiModel(value = "任务提交")
public class XlmSubmitTaskByInstanceCusDto {
    /**
     *
     */
//    @NotBlank
    @ApiModelProperty(value = "processInstanceId")
    private String processInstanceId;

    /**
     * 全局变量
     */
    @ApiModelProperty(value = "运行时的全局变量", notes = "运行时变量影响网关路由的选择，参考https://tkjohn.github.io/flowable-userguide/#apiVariables")
    private Map<String, Object> variables;

    /**
     * 局部变量
     */
    @ApiModelProperty(value = "运行时的局部变量", notes = "运行时变量影响网关路由的选择，参考https://tkjohn.github.io/flowable-userguide/#apiVariables")
    private Map<String, Object> localVariables;

    /**
     * 任务变量
     */
    @ApiModelProperty(value = "任务变量")
    private Map<String, Object> taskVariables;

    /**
     * 表单数据
     */
    @ApiModelProperty(value = "表单数据")
    private Map<String, Object> formData;

    /**
     * 评论
     */
    @ApiModelProperty(value = "评论")
    private String comment;

    /**
     * 办理人
     */
    @Min(1)
    @ApiModelProperty("办理人")
    private long handleUserId;

    /**
     * 办理人姓名
     */
    @NotBlank
    @ApiModelProperty("办理人")
    private String handleUserName;
}
