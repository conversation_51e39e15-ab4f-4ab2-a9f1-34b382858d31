package com.mascj.lup.event.bill.util;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/19
 */
@Component
public class RedisOpsUtils {

    private static RedisTemplate<String, Object> redisTemplate;

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        RedisOpsUtils.redisTemplate = redisTemplate;
    }


    //分布式锁过期时间 s  可以根据业务自己调节
    private static final Long LOCK_REDIS_TIMEOUT = 20L;
    //分布式锁休眠 至 再次尝试获取 的等待时间 ms 可以根据业务自己调节
    public static final Long LOCK_REDIS_WAIT = 500L;

    //
//    //Keys1 是id  keys2为1   不为1 表示删除key
//    //返回0 表示存在key  返回1表示不存在key 返回2表示删除成功
    private static final String SCRIPT =
            " local saveKey = 'dan_' .. KEYS[1]; \n"+
                    " if tonumber(KEYS[2]) == 1 then  \n" +
                    // 不存在 存入
                    " if redis.call('EXISTS',saveKey) == 0 then \n" + "    redis.call('SET',saveKey,1, 'ex',  tonumber(KEYS[3]));   return 1; \n"
                    + "    else  \n" + "	return 0; end \n"
                    +"  else  redis.call('DEL',saveKey) return 2; end \n " ;


    public static  Object execute(String script, List<String> keyList) {
        DefaultRedisScript redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(script);
        redisScript.setResultType(Long.class);
        return redisTemplate.execute(redisScript, keyList, Lists.newArrayList());
    }



    public  static   Boolean getLockLua(String key , Long second){
        List<String> keyList = new ArrayList<>();
        keyList.add(key);
        keyList.add("1");
        keyList.add( second == null ? LOCK_REDIS_TIMEOUT.toString() : second.toString());
        Object execute = execute(SCRIPT, keyList);
        //0表示正在执行中 不能重复执行定时任务
        if (  Integer.parseInt(execute.toString()) == 1){
            return  true;
        }else {
            return  false;
        }
    }
    public  static   Boolean getLockLua(String key ){
        return getLockLua(key,LOCK_REDIS_TIMEOUT);
    }


    public static   void unLockLua(String key ){
        List<String> keyList = new ArrayList<>();
        keyList.add(key);
        keyList.add("2");
        execute(SCRIPT, keyList);
    }

    /**
     *  加锁
     **/
//    public static Boolean getLock(String key,String value){
//        Boolean lockStatus =  redisTemplate.opsForValue().setIfAbsent(key,value, Duration.ofSeconds(LOCK_REDIS_TIMEOUT));
//        return lockStatus;
//    }

    /**
     *  释放锁
     **/
    public static  Long releaseLock(String key,String value){
        String luaScript = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        RedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript,Long.class);
        Long releaseStatus = (Long) redisTemplate.execute(redisScript, Collections.singletonList(key),value);
        return releaseStatus;
    }

    /**
     * HSET
     * @param key
     * @param field
     * @param value
     */
    public static void hashSet(String key, String field, Object value) {
        redisTemplate.opsForHash().put(key, field, value);
    }

    /**
     * HGET
     * @param key
     * @param field
     * @return
     */
    public static Object hashGet(String key, String field) {
        return redisTemplate.opsForHash().get(key, field);
    }

    /**
     * HKEYS
     * @param key
     * @return
     */
    public static Set<Object> hashKeys(String key) {
        return redisTemplate.opsForHash().keys(key);
    }

    /**
     * HEXISTS
     * @param key
     * @param field
     * @return
     */
    public static boolean hashCheck(String key, String field) {
        return redisTemplate.opsForHash().hasKey(key, field);
    }

    /**
     * HDEL
     * @param key
     * @param fields
     * @return
     */
    public static boolean hashDel(String key, Object[] fields) {
        return redisTemplate.opsForHash().delete(key, fields) > 0;
    }

    /**
     * HLEN
     * @param key
     * @return
     */
    public static long hashLen(String key) {
        return redisTemplate.opsForHash().size(key);
    }

    /**
     * EXPIRE
     * @param key
     * @param timeout
     * @return
     */
    public static boolean expireKey(String key, long timeout) {
        return redisTemplate.expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * SET
     * @param key
     * @param value
     */
    public static void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * GET
     * @param key
     * @return
     */
    public static Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * SETEX
     * @param key
     * @param value
     * @param expire
     */
    public static void setWithExpire(String key, Object value, long expire) {
        redisTemplate.opsForValue().set(key, value, expire, TimeUnit.SECONDS);
    }

    /**
     * TTL
     * @param key
     * @return
     */
    public static long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * EXISTS
     * @param key
     * @return
     */
    public static boolean checkExist(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * DEL
     * @param key
     * @return
     */
    public static boolean del(String key) {
        return RedisOpsUtils.checkExist(key) && redisTemplate.delete(key);
    }

    /**
     * KEYS
     * @param pattern
     * @return
     */
    public static Set<String> getAllKeys(String pattern) {
        return redisTemplate.keys(pattern);
    }

    /**
     * RPUSH
     * @param key
     * @param value
     */
    public static void listRPush(String key, Object... value) {
        if (value.length == 0) {
            return;
        }
        for (Object val : value) {
            redisTemplate.opsForList().rightPush(key, val);
        }
    }

    /**
     * LRANGE
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static List<Object> listGet(String key, long start, long end) {
        return redisTemplate.opsForList().range(key, start, end);
    }

    /**
     * LRANGE
     * @param key
     * @return
     */
    public static List<Object> listGetAll(String key) {
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    /**
     * LLen
     * @param key
     * @return
     */
    public static Long listLen(String key) {
        return redisTemplate.opsForList().size(key);
    }

    /**
     * ZADD
     * @param key
     * @param value
     * @param score
     */
    public static Boolean zAdd(String key, Object value, double score) {
        return redisTemplate.opsForZSet().add(key, value, score);
    }

    /**
     * ZREM
     * @param key
     * @param value
     */
    public static Boolean zRemove(String key, Object... value) {
        return redisTemplate.opsForZSet().remove(key, value) > 0;
    }
    /**
     * ZRANGE
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static Set<Object> zRange(String key, long start, long end) {
        return redisTemplate.opsForZSet().range(key, start, end);
    }

    /**
     * ZRANGE
     * @param key
     * @return
     */
    public static Object zGetMin(String key) {
        Set<Object> objects = zRange(key, 0, 0);
        if (CollectionUtils.isEmpty(objects)) {
            return null;
        }
        return objects.iterator().next();
    }

    /**
     * ZSCORE
     * @param key
     * @param value
     * @return
     */
    public static Double zScore(String key, Object value) {
        return redisTemplate.opsForZSet().score(key, value);
    }

    /**
     * ZINCRBY
     * @param key
     * @param value
     * @param delta
     */
    public static Double zIncrement(String key, Object value, double delta) {
        return redisTemplate.opsForZSet().incrementScore(key, value, delta);
    }
}
