package com.mascj.lup.event.bill.feign;

import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.feign.vo.XlmSubmitTaskByInstanceCusDto;
import com.mascj.support.workflow.constant.WorkflowConstants;
import com.mascj.support.workflow.enums.HandleTaskEnum;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(value = WorkflowConstants.WORKFLOW_SERVICE)
public interface IXlmCusTaskProvider {


    /**
     * 完成Task
     *
     * @return
     */
    @PostMapping("/provider/task/submit/instanceId")
    Result<HandleTaskEnum> submitInId(@RequestBody @Valid XlmSubmitTaskByInstanceCusDto xlmSubmitTaskByInstanceDto);


}
