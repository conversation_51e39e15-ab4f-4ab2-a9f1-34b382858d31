/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.mascj.lup.event.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

import com.mascj.lup.event.bill.entity.LupFlowActivity;
import com.mascj.lup.event.bill.vo.LupFlowActivityVO;
import org.apache.ibatis.annotations.Param;

/**
 * 流程活动节点 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
public interface LupFlowActivityMapper extends BaseMapper<LupFlowActivity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param lupFlowActivity
	 * @return
	 */
	List<LupFlowActivityVO> selectLupFlowActivityPage(IPage page,@Param("data") LupFlowActivityVO lupFlowActivity);

}
