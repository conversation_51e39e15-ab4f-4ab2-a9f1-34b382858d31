package com.mascj.lup.event.bill.provider;

import com.mascj.kernel.common.api.Result;
import com.mascj.lup.event.bill.feign.FlowMessageHandler;
import com.mascj.lup.event.bill.service.ILupFlowActivityService;
import com.mascj.lup.event.bill.vo.FlowMessageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "工作流回调业务",tags = "工作流回调业务")
@RestController
@AllArgsConstructor
public class FlowMessageHandlerProvider implements FlowMessageHandler {

    @Autowired
    private ILupFlowActivityService lupFlowActivityService;

    @ApiOperation(value = "任务启动")
    @PostMapping("/support/taskStarted")
    @Override
    public Result taskStarted(@RequestBody FlowMessageVO message) {
        return lupFlowActivityService.taskStarted(message);
    }

    @ApiOperation(value = "任务结束")
    @PostMapping("/support/taskEnded")
    @Override
    public Result taskEnded(@RequestBody FlowMessageVO message) {
        return lupFlowActivityService.taskEnded(message);
    }


    @ApiOperation(value = "流程结束")
    @PostMapping("/support/processEnd")
    @Override
    public Result processEnd(@RequestBody FlowMessageVO message) {
        return lupFlowActivityService.processEnd(message);
    }
}
