package com.mascj.lup.event.bill.support;

import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.util.$;
import com.mascj.kernel.common.util.LocaleMessageUtil;
import com.mascj.kernel.common.util.beans.BeanUtil;
import com.mascj.kernel.tools.RedisKey;
import com.mascj.kernel.tools.RedisPlane;
import com.mascj.kernel.tools.linq.Linqs;
import com.mascj.lup.event.bill.constant.EventTipKey;
import com.mascj.lup.event.bill.constant.LupVariableConstants;
import com.mascj.lup.event.bill.entity.LupFlowEntity;
import com.mascj.lup.event.bill.exceptions.OperationFailedException;
import com.mascj.lup.event.bill.tools.ResultOptional;
import com.mascj.platform.system.entity.Dict;
import com.mascj.platform.system.feign.ISysDictProvider;
import com.mascj.support.config.dto.*;
import com.mascj.support.config.feign.IAepConfigProvider;
import com.mascj.support.workflow.feign.IXlmVariableProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationException;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParseException;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * AEP执行上下文
 */
@Component
public class LupExecutionContext {

    public static final String Default_Variable_Resolver_Class_Name = "com.mascj.aep.provider.AepVariableResolver";

    @Autowired
    private   IXlmVariableProvider xlmVariableProvider;
    @Autowired
    private   RedisPlane redisPlane;
    @Autowired
    private   IAepConfigProvider configProvider;
    @Autowired
    private   ISysDictProvider dictProvider;


    /**
     * 计算变量值
     *
     * @param flow     流程对象
     * @param formData 表单数据
     * @return
     */
    public Map<String, Object> resolveVariable(LupFlowEntity flow, Map<String, Object> formData) {
        Assert.notNull(flow, LocaleMessageUtil.getMessage(EventTipKey.NoneFlowTipKey));//"请指定流程对象flow");
        Assert.notNull(formData, LocaleMessageUtil.getMessage(EventTipKey.NoneFormData));//"请指定表单数据formData");

        Map<String, Object> result = new HashMap<>();

        Assert.hasText(flow.getProcessDefinitionId(), LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneProcessDefinitionId,Arrays.asList(EventTipKey.NoneTip))+ flow.getId());//"当前工单的流程定义processDefinitionId为空，id=" + flow.getId());

        Collection<XlmVariableInfo> variableInfoList = getVariableInfo(flow.getBizCode(), flow.getProcessDefinitionId()).values();

        /**
         * 从工单中计算变量值
         */
//        List<XlmVariableInfo> billVariables = variableInfoList.stream().filter(XlmVariableInfo::isFromBill).collect(Collectors.toList());

//        Map<String, Object> billValues = repositoryProvider.resolveFlowField(flow, billVariables.stream().map(XlmVariableInfo::getName).collect(Collectors.toList()));
//        if ($.isNull(billValues)) {
//            billValues = new HashMap<>();
//        }
//
//        for (XlmVariableInfo info : billVariables) {
//            if (!info.isAllowNull() && (!billValues.containsKey(info.getName()) || $.isNull(billValues.get(info.getName())))) {
//
//                throw OperationFailedException.format("变量{}的计算结果不能为null", info.getName());
//            }
//        }
//        result.putAll(billValues);

        for (XlmVariableInfo variableInfo : variableInfoList) {
            String variableName = variableInfo.getName();
            Object varValue = null;

            if (variableInfo.isFromBill()) {
                continue;
            } else if (variableInfo.isFromForm()) {
                if (formData.containsKey(variableName)) {
                    varValue = formData.get(variableName);
                }
            }
//            else if (variableInfo.isFromBean()) {
//                String beanClassName = $.isNotBlank(variableInfo.getBeanClassFullName()) ?
//                        variableInfo.getBeanClassFullName() :
//                        Default_Variable_Resolver_Class_Name;
//
//                IVariableResolver variableBean = getVariableResolver(beanClassName);
//                varValue = variableBean.resolveVariableValue(flow, variableName);
//            }
//            if (!variableInfo.isAllowNull() && $.isNull(varValue)) {
//                throw OperationFailedException.format("变量{}的计算结果不能为null", variableName);
//            }

            if ($.isNotNull(varValue)) {
                result.put(variableName, varValue);
            }
        }

        return result;
    }


    /**
     * 计算工单的变量值
     *
     * @param flow
     * @param variables
     * @return
     */
//    public Map<String, Object> resolveFlowField(LupFlowEntity flow, List<String> variables) {
//        Assert.notNull(flow, "请指定参数flow");
//        Assert.notNull(variables, "请指定参数variables");
//
//        AepWorkBill workBill = lupBillMapper.se
//        Assert.notNull(workBill, "获取工单失败，flowId=" + flow.getId());
//
//        Map<String, Object> result = new HashMap<>();
//        Map<String, Object> flowMap = BeanUtil.toMap(flow);
//        Map<String, Object> workMap = BeanUtil.toMap(workBill);
//
//        for (String key : variables) {
//            if (flowMap.containsKey(key)) {
//                result.put(key, flowMap.get(key));
//            } else if (workMap.containsKey(key)) {
//                result.put(key, workMap.get(key));
//            }
//        }
//
//        return result;
//
//    }


    /**
     * 计算网关条件值
     *
     * @param processDefinitionId 流程定义
     * @param baseVariables       基础变量
     * @return
     */
    public Map<String, Object> resolveGatewayCondition(String bizCode, String processDefinitionId, Map<String, Object> baseVariables) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Assert.hasText(processDefinitionId, LocaleMessageUtil.getMessage(EventTipKey.NoneParameterProcessDefinitionId));
        Assert.notEmpty(baseVariables, LocaleMessageUtil.getMessageByKeyList(EventTipKey.EmptyVariables, Arrays.asList(EventTipKey.NoneTip)));

        Map<String, Object> result = new HashMap<>();
        Map<String, XlmGatewayCondition> gtConditionMap = getGatewayCondition(bizCode, processDefinitionId);

        ExpressionParser parser = new SpelExpressionParser();
        for (String variableName : gtConditionMap.keySet()) {
            XlmGatewayCondition gtCondition = gtConditionMap.get(variableName);

            try {
                Boolean varValue = parser.parseExpression(gtCondition.getGtExpression()).getValue(baseVariables, Boolean.class);

                if ($.isNull(varValue)) {
                    throw OperationFailedException.format(LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneVar,Arrays.asList(EventTipKey.NoneTip)), variableName);
                }
                result.put(variableName, varValue);
            } catch (ParseException parseException) {
                throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.FailAnalysisVar),
                        variableName,
                        gtCondition.getGtExpression(),
                        parseException.getMessage());
            } catch (EvaluationException evaluationException) {
                throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.ExpAnalysisVar),
                        variableName,
                        gtCondition.getGtExpression(),
                        baseVariables,
                        evaluationException.getMessage());
            }
        }

        return result;
    }

    /**
     * 计算全局变量
     *
     * @param flow
     * @param formData
     * @return
     */
    public Map<String, Object> computeGlobalVariables(LupFlowEntity flow, Map<String, Object> formData) {

        Assert.notNull(flow, LocaleMessageUtil.getMessage(EventTipKey.NoneFlowTipKey));
        Assert.notNull(formData, LocaleMessageUtil.getMessage(EventTipKey.NoneFormData));

        String bizCode = flow.getBizCode();
        String processDefinitionId = flow.getProcessDefinitionId();
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Assert.hasText(processDefinitionId, LocaleMessageUtil.getMessage(EventTipKey.NoneParameterProcessDefinitionId));

        Map<String, Object> variables = new HashMap<>();

        //流程标识符
        variables.put(LupVariableConstants.FLOW_IDENTIFIER, flow.getId());

        // 计算全局变量
        variables.putAll(resolveVariable(flow, formData));


        // 查询历史变量
        Map<String, Object> hisVariables = new HashMap<>();

        // 流程启动阶段，processTaskId 为空
        if ($.isNotBlank(flow.getProcessTaskId())) {
            hisVariables = xlmVariableProvider.getVariables(flow.getProcessInstanceId()).getData();
        }

        // 基础变量值
        Map<String, Object> baseVariables = new HashMap<>();
        if ($.isNotEmpty(hisVariables)) {
            baseVariables.putAll(hisVariables);
        }
        if ($.isNotEmpty(variables)) {
            baseVariables.putAll(variables);
        }
        if ($.isNotEmpty(formData)) {
            baseVariables.putAll(formData);
        }

        //计算网关变量
        variables.putAll(resolveGatewayCondition(bizCode, processDefinitionId, baseVariables));

        return variables;
    }

    /**
     * 计算全局变量
     *
     * @param flow
     * @param formData
     * @return
     */
    public Map<String, Object> computeGlobalVariablesStart(LupFlowEntity flow, Map<String, Object> formData, Long billId) {

        Assert.notNull(flow, LocaleMessageUtil.getMessage(EventTipKey.NoneFlowTipKey));
        Assert.notNull(formData, LocaleMessageUtil.getMessage(EventTipKey.NoneFormData));

        String bizCode = flow.getBizCode();
        String processDefinitionId = flow.getProcessDefinitionId();
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Assert.hasText(processDefinitionId, LocaleMessageUtil.getMessage(EventTipKey.NoneParameterProcessDefinitionId));

        Map<String, Object> variables = new HashMap<>();

        //流程标识符
        variables.put(LupVariableConstants.FLOW_BILLID, billId);

        // 计算全局变量
        variables.putAll(resolveVariable(flow, formData));


        // 查询历史变量
        Map<String, Object> hisVariables = new HashMap<>();

        // 流程启动阶段，processTaskId 为空
        if ($.isNotBlank(flow.getProcessTaskId())) {
            hisVariables = xlmVariableProvider.getVariables(flow.getProcessInstanceId()).getData();
        }

        // 基础变量值
        Map<String, Object> baseVariables = new HashMap<>();
        if ($.isNotEmpty(hisVariables)) {
            baseVariables.putAll(hisVariables);
        }
        if ($.isNotEmpty(variables)) {
            baseVariables.putAll(variables);
        }
        if ($.isNotEmpty(formData)) {
            baseVariables.putAll(formData);
        }

        //计算网关变量
        variables.putAll(resolveGatewayCondition(bizCode, processDefinitionId, baseVariables));

        return variables;
    }

    /**
     * TODO 查询任务节点配置参数
     * @param processDefinitionId
     * @param activityId
     * @return
     */
    public XlmActivityState resolveActivityState(String bizCode, String processDefinitionId, String activityId) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Assert.hasText(processDefinitionId, LocaleMessageUtil.getMessage(EventTipKey.NoneParameterProcessDefinitionId));
        Assert.hasText(activityId, LocaleMessageUtil.getMessage(EventTipKey.NoneActivity));


        Map<String, XlmActivityState> activityStateMap = getActivityState(bizCode, processDefinitionId);

        if (!activityStateMap.containsKey(activityId)) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.NoneActivityDetail), activityId);
        }

        return activityStateMap.get(activityId);
    }

    public XlmActivityState resolveActivityStateV2(String bizCode, String processDefinitionId, String activityId) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Assert.hasText(processDefinitionId, LocaleMessageUtil.getMessage(EventTipKey.NoneParameterProcessDefinitionId));
        Assert.hasText(activityId, LocaleMessageUtil.getMessage(EventTipKey.NoneActivity));

        Map<String, XlmActivityState> activityStateMap = getActivityStateV2(bizCode, processDefinitionId);

        if (!activityStateMap.containsKey(activityId)) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.NoneActivityDetail), activityId);
        }

        return activityStateMap.get(activityId);
    }


    public Map<String, XlmActivityState> getActivityStateV2(String bizCode, String processDefinitionId) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Assert.hasText(processDefinitionId, LocaleMessageUtil.getMessage(EventTipKey.NoneParameterProcessDefinitionId));

        Result<List<XlmActivityState>> activityList = configProvider.getActivityList(bizCode);

        List<XlmActivityState> activityStateList = activityList.getData();

        List<XlmActivityState> targetList = activityStateList.stream()
                .filter(x -> processDefinitionId.equals(x.getProcessDefinitionId()))
                .collect(Collectors.toList());

        if ($.isEmpty(targetList)) {
            targetList = activityStateList.stream()
                    .filter(x -> $.isBlank(x.getProcessDefinitionId()))
                    .collect(Collectors.toList());
        }

        return targetList.stream().collect(Collectors.toMap(XlmActivityState::getActivityId, y -> y));
    }

    /**
     * 获取归档状态
     *
     * @param processDefinitionId
     * @return
     */
    public XlmActivityState resolveArchivedState(String bizCode, String processDefinitionId) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Assert.hasText(processDefinitionId, LocaleMessageUtil.getMessage(EventTipKey.NoneParameterProcessDefinitionId));

        XlmActivityState activityState = getActivityState(bizCode, processDefinitionId)
                .values()
                .stream()
                .filter(x -> x.getActivityId().equals("archived") && x.isArchived()  )
                .findFirst()
                .orElse(null);

        if ($.isNull(activityState)) {
            throw OperationFailedException.format(LocaleMessageUtil.getMessage(EventTipKey.NoneActivityName), processDefinitionId);
        }

        return activityState;
    }


    protected Map<String, XlmActivityState> getActivityState(String bizCode, String processDefinitionId) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Assert.hasText(processDefinitionId, LocaleMessageUtil.getMessage(EventTipKey.NoneParameterProcessDefinitionId));

        XlmConfigEnvironment configEnvironment = getConfigEnvironment(bizCode, processDefinitionId);
        Assert.notNull(configEnvironment.getActivityStates(), LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneActivityState,Arrays.asList(EventTipKey.XCETip,EventTipKey.NoneTip)));

        return Linqs.of(configEnvironment.getActivityStates()).toMap(XlmActivityState::getActivityId);
    }

    protected Map<String, XlmGatewayCondition> getGatewayCondition(String bizCode, String processDefinitionId) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Assert.hasText(processDefinitionId, LocaleMessageUtil.getMessage(EventTipKey.NoneParameterProcessDefinitionId));

        XlmConfigEnvironment configEnvironment = getConfigEnvironment(bizCode, processDefinitionId);
        Assert.notNull(configEnvironment.getGatewayConditions(), LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneGatewayCondition,Arrays.asList(EventTipKey.XCETip,EventTipKey.NoneTip)));

        return Linqs.of(configEnvironment.getGatewayConditions()).toMap(XlmGatewayCondition::getName);
    }

    /**
     * 获取XlmConfigEnvironment
     *
     * @param processDefinitionId 流程定义
     * @return
     */
    protected XlmConfigEnvironment getConfigEnvironment(String bizCode, String processDefinitionId) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Assert.hasText(processDefinitionId, LocaleMessageUtil.getMessage(EventTipKey.NoneParameterProcessDefinitionId));

        Assert.notNull(configProvider, LocaleMessageUtil.getMessage(EventTipKey.NoneConfigProvider));

        return ResultOptional.of(configProvider.getEnvironmentOrDefault(bizCode, processDefinitionId)).orElseThrow().getData();
    }

    public Map<String, XlmVariableInfo> getVariableInfo(String bizCode, String processDefinitionId) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Assert.hasText(processDefinitionId, LocaleMessageUtil.getMessage(EventTipKey.NoneParameterProcessDefinitionId));

        XlmConfigEnvironment configEnvironment = getConfigEnvironment(bizCode, processDefinitionId);
        Assert.notNull(configEnvironment.getVariableInfo(), LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneVariableInfo,Arrays.asList(EventTipKey.XCETip,EventTipKey.NoneTip)));

        return Linqs.of(configEnvironment.getVariableInfo()).toMap(XlmVariableInfo::getName);
    }

    public List<XlmBizEntity> getHandleList(String bizCode) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        return configProvider.getHandleList(bizCode).getData();
    }

    public Map<String, String> getHandleMap(String bizCode) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        return Linqs.of(getHandleList(bizCode)).toMap(XlmBizEntity::getCode, XlmBizEntity::getName);
    }

    public Map<String, String> getBillStateMap(String bizCode) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        Result<List<XlmBizEntity>> billStateList = configProvider.getBillStateList(bizCode);
        return Linqs.of(billStateList.getData()).toMap(XlmBizEntity::getCode, XlmBizEntity::getName);
    }

    public Map<String, String> getEventMap(String bizCode) {
        Assert.hasText(bizCode, LocaleMessageUtil.getMessage(EventTipKey.NoneBizCode));
        return Linqs.of(configProvider.getEventList(bizCode).getData()).toMap(XlmBizEntity::getCode, XlmBizEntity::getName);
    }

    public Map<String, String> getDictMap(String code) {
        Assert.hasText(code, LocaleMessageUtil.getMessageByKeyList(EventTipKey.NoneCode, Arrays.asList(EventTipKey.RequiredTip)));

//        RedisKey redisKey = RedisKey.forEntity("liangma-aep", Dict.class).forItem(code);

        List<Dict> list =  dictProvider.getList(code).getData()
                .stream().filter(e->e.getParentId() > 0L).collect(Collectors.toList());

        return Linqs.of(list).toMap(Dict::getDictKey, Dict::getDictValue);
    }
}
