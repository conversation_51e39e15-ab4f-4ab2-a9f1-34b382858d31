<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mascj.lup.event.bill.mapper.LupFlowActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="lupFlowActivityResultMap" type="com.mascj.lup.event.bill.entity.LupFlowActivity">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="flow_id" property="flowId"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="process_task_id" property="processTaskId"/>
        <result column="activity_id" property="activityId"/>
        <result column="state" property="state"/>
        <result column="name" property="name"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="duration_in_millis" property="durationInMillis"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="assignee" property="assignee"/>
        <result column="from_str" property="fromStr"/>
        <result column="assignee_name" property="assigneeName"/>
    </resultMap>



 <resultMap id="lupFlowActivityResultMapVO" extends="lupFlowActivityResultMap"
  type="com.mascj.lup.event.bill.vo.LupFlowActivityVO">

  </resultMap>


    <select id="selectLupFlowActivityPage" resultMap="lupFlowActivityResultMapVO">
        select * from lup_flow_activity d  where 1=1 and d.is_deleted =0
      <if test="data.flowId != null and data.flowId != '' "> and d.flow_id = #{data.flowId} </if>
        <if test="data.processInstanceId != null and data.processInstanceId != '' "> and d.process_instance_id = #{data.processInstanceId} </if>
        <if test="data.processTaskId != null and data.processTaskId != '' "> and d.process_task_id = #{data.processTaskId} </if>
        <if test="data.activityId != null and data.activityId != '' "> and d.activity_id = #{data.activityId} </if>
        <if test="data.state != null and data.state != '' "> and d.state = #{data.state} </if>
        <if test="data.name != null and data.name != '' "> and d.name = #{data.name} </if>
        <if test="data.startTime != null and data.startTime != '' "> and d.start_time = #{data.startTime} </if>
        <if test="data.endTime != null and data.endTime != '' "> and d.end_time = #{data.endTime} </if>
        <if test="data.durationInMillis != null and data.durationInMillis != '' "> and d.duration_in_millis = #{data.durationInMillis} </if>
        <if test="data.tenantId != null and data.tenantId != '' "> and d.tenant_id = #{data.tenantId} </if>
        <if test="data.assignee != null and data.assignee != '' "> and d.assignee = #{data.assignee} </if>
        <if test="data.fromStr != null and data.fromStr != '' "> and d.from_str = #{data.fromStr} </if>
        <if test="data.assigneeName != null and data.assigneeName != '' "> and d.assignee_name = #{data.assigneeName} </if>
    
            <if test="data.startDate != null">
             and date(d.create_time) >= date(#{data.startDate})
          </if>
            <if test="data.endDate != null">
           and date(d.create_time) &lt;= date(#{data.endDate})
        </if>

      order by id desc
    </select>

</mapper>