/**
 * Copyright (c) 2018-2028, Chill <PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.mascj.lup.event.bill.entity;

import java.time.LocalDateTime;
import java.util.Date;

import com.mascj.kernel.database.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 流程活动节点实体类
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LupFlowActivity对象", description = "流程活动节点")
public class LupFlowActivity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 流程Id
     */
    @ApiModelProperty(value = "流程Id")
    private Long flowId;
    /**
     * 流程实例
     */
    @ApiModelProperty(value = "流程实例")
    private String processInstanceId;
    /**
     * 流程当前任务
     */
    @ApiModelProperty(value = "流程当前任务")
    private String processTaskId;
    /**
     * 活动节点Id
     */
    @ApiModelProperty(value = "活动节点Id")
    private String activityId;
    /**
     * 状态数字
     */
    @ApiModelProperty(value = "状态数字")
    private Integer state;
    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称")
    private String name;
    /**
     * 启动时间
     */
    @ApiModelProperty(value = "启动时间")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
     * 流转时长
     */
    @ApiModelProperty(value = "流转时长")
    private Long durationInMillis;
    /**
     * 租户Id
     */
    @ApiModelProperty(value = "租户Id")
    private Long tenantId;
    /**
     * 处置人Id
     */
    @ApiModelProperty(value = "处置人Id")
    private Long assignee;
    /**
     * 此表单信息
     */
    @ApiModelProperty(value = "此表单信息")
    private String fromStr;
    /**
     * 处置人Name
     */
    @ApiModelProperty(value = "处置人Name")
    private String assigneeName;


}
