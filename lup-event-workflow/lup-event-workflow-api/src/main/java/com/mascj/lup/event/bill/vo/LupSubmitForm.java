package com.mascj.lup.event.bill.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 提交任务
 */
@Data
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LupSubmitForm {

    /**
     * 流程Id
     */
    @ApiModelProperty(value = "流程Id")
    private Long flowId;
    @ApiModelProperty(value = "流程Id")
    private Long workBillId;
    @ApiModelProperty(value = "processInstanceId")
    private String processInstanceId;

    /**
     *
     */
    @NotBlank
    @ApiModelProperty(value = "任务Id")
    private String taskId;

    /**
     * 表单数据
     */
    @ApiModelProperty(value = "表单数据")
    private Map<String, Object> formData;

    /**
     * 评论
     */
    @ApiModelProperty(value = "评论")
    private String comment;

    @ApiModelProperty(value = "推送记录的主键ID")
    private Long pushLogId;
    @ApiModelProperty(value = "是否推送按钮 默认false  点击推送相关按钮的时候 传true")
    private Boolean pushBtn = Boolean.FALSE;
}
