package com.mascj.lup.event.bill.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString
public class FlowMessageVO implements Serializable {

    private String eventName;

    private String processDefinitionId;

    private String processInstanceId;

    private String currentActivityId;
    private String flowId;

    private String tenantId;

    private String currentTaskId;
    private String formInfo;

    private Date createTaskTime;

    //结束任务使用
    private String assignee;
//    private Long durationInMillis;
//    private Date endTaskTime;

    private boolean active;

    private boolean ended;
}
