package com.mascj.lup.event.bill.feign;

import com.alibaba.fastjson.JSON;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.util.$;
import com.mascj.lup.event.bill.constant.Constants;
import com.mascj.lup.event.bill.vo.FlowMessageVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@FeignClient(value = Constants.LOCAL_PROJECT_NAME)
public interface FlowMessageHandler {

    /**
     * 任务启动
     *
     * @param message
     */
    @ApiOperation(value = "任务启动回调监听")
    @PostMapping("/support/taskStarted")
    public Result taskStarted(@RequestBody FlowMessageVO message);

    /**
     * 任务结束
     *
     * @param message
     */
    @ApiOperation(value = "任务结束回调监听")
    @PostMapping("/support/taskEnded")
    public Result taskEnded(@RequestBody FlowMessageVO message) ;

    /**
     * 流程结束
     *
     * @param message
     */
    @ApiOperation(value = "流程结束回调监听")
    @PostMapping("/support/processEnd")
    public Result processEnd(@RequestBody FlowMessageVO message)  ;

//    /**
//     * 流程取消
//     *
//     * @param message
//     */
//    public void processCancel(FlowMessageVO message) {
//
//
//    }


}
