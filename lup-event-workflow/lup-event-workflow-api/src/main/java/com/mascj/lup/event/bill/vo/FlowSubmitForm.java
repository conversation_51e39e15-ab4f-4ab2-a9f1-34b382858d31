package com.mascj.lup.event.bill.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 表单提交
 */
@Data
@ApiModel("表单提交")
public class FlowSubmitForm {

    /**
     * 工单Id
     */
    @Min(1)
    @ApiModelProperty(value = "工单Id")
    private Long workBillId;

//    /**
//     * 督办标记
//     */
//    @ApiModelProperty(value = "督办标记（0正常，1交办，2催办, 3问责）")
//    private int markSupervise;

//    /**
//     * 告警标记
//     */
//    @ApiModelProperty(value = "告警标记", notes = "0正常，1紧急")
//    private int markAlert;


    /**
     *
     */
    @NotBlank
    @ApiModelProperty(value = "任务Id")
    private String taskId;

    /**
     * 表单数据
     */
    @ApiModelProperty(value = "表单数据")
    private Map<String, Object> formData;

    /**
     * 评论
     */
    @ApiModelProperty(value = "评论")
    private String comment;
}
