package com.mascj.lup.event.bill.tools;

import com.alibaba.fastjson.JSON;
import com.mascj.kernel.common.api.Result;
import com.mascj.kernel.common.util.$;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ResultOptional<T> {

    private final Result<T> result;

    private T other;
    private RuntimeException exception;

    public ResultOptional(Result<T> result) {
        this.result = result;
    }

    public static <T> ResultOptional<T> of(Result<T> result) {
        return new ResultOptional<>(result);
    }

    public ResultOptional<T> orElse(T other) {
        if (!result.isSuccess()) {
            this.other = other;
        }
        return this;
    }

    public ResultOptional<T> orElseThrow() {
        if (!result.isSuccess()) {
            FeignError feignError = parseMsg(result.getMsg());

            String errDetail = feignError.matched() ?
                    $.format("Feign Request Error: [{}] during [{}] to [{}]", feignError.getErrMessage(), feignError.getMethod(), feignError.getHttpUrl()) :
                    result.getMsg();
            exception = new RuntimeException(errDetail);
        }
        return this;
    }

    public T getData() {
        if (!result.isSuccess()) {
            if ($.isNotNull(exception)) {
                throw exception;
            }
            if ($.isNotNull(other)) {
                return other;
            }
        }
        return result.getData();
    }

    private FeignError parseMsg(String msg) {
        //String msg = "[400 Bad Request] during [POST] to [http://platform-fly-admin-server/varyPage] [IFlyProvider#varyPage(FlyVarySearch)]: [{\"code\":500,\"msg\":\"未找到当前登录用户的行政主体，userId=0\",\"time\":1669693977141,\"success\":false}]";
        String regex = "\\[(.+?)\\]";

        List<String> list = new ArrayList<>();

        Matcher matcher = Pattern.compile(regex).matcher(msg);

        while (matcher.find()) {
            list.add(matcher.group().replace("[", "").replace("]", ""));
        }

        FeignError result = new FeignError();

        if (list.size() >= 1) {
            result.setHttpStatus(list.get(0));// [400 Bad Request]
        }
        if (list.size() >= 2) {
            result.setMethod(list.get(1)); //[POST]
        }
        if (list.size() >= 3) {
            result.setHttpUrl(list.get(2)); //[http://platform-fly-admin-server/varyPage]
        }
        if (list.size() >= 4) {
            result.setInterfaceDeclare(list.get(3)); //[IFlyProvider#varyPage(FlyVarySearch)]
        }
        if (list.size() >= 5) {
            // 未找到当前登录用户的行政主体，userId=0
            result.setErrMessage(JSON.parseObject(list.get(4)).getString("msg"));
        }

        return result;
    }

    @Data
    public static class FeignError {
        private String httpStatus = Strings.EMPTY;
        private String method = Strings.EMPTY;
        private String httpUrl = Strings.EMPTY;
        private String interfaceDeclare = Strings.EMPTY;
        private String errMessage = Strings.EMPTY;

        public boolean matched() {
            return $.isNotBlank(httpStatus) &&
                    $.isNotBlank(method) &&
                    $.isNotBlank(httpUrl) &&
                    $.isNotBlank(interfaceDeclare) &&
                    $.isNotBlank(errMessage);
        }
    }
}
