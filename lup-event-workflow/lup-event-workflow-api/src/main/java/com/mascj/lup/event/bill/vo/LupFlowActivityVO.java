/**
 * Copyright (c) 2018-2028, Chill <PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.mascj.lup.event.bill.vo;

import com.mascj.lup.event.bill.entity.LupFlowActivity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 流程活动节点视图实体类
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LupFlowActivityVO对象", description = "流程活动节点")
public class LupFlowActivityVO extends LupFlowActivity {
	private static final long serialVersionUID = 1L;

	private String startDate;
	private String endDate;
}
