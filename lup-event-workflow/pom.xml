<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>lup-event-workbill</artifactId>
        <groupId>com.mascj</groupId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>2.0.0-SNAPSHOT</version>
    <artifactId>lup-event-workflow</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>lup-event-workflow-api</module>
        <module>lup-event-workflow-service</module>
    </modules>


    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.mascj</groupId>
                <artifactId>support-config-api</artifactId>
                <version>1.6.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.mascj</groupId>
                <artifactId>support-workflow-api</artifactId>
                <version>1.6.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.mascj</groupId>
                <artifactId>platform-system-api</artifactId>
                <version>2.9.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.mascj</groupId>
                <artifactId>support-file-api</artifactId>
                <version>1.6.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.mascj</groupId>
                        <artifactId>support-file-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>