package com.mascj.lup.event.bill;

import com.mascj.kernel.feign.annotation.EnableLiangmaFeign;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAsync
@EnableLiangmaFeign
@SpringBootApplication(scanBasePackages = {"com.mascj.lup.event"})
public class LupEventBillServer {
    public static void main(String[] args) {
        SpringApplication.run(LupEventBillServer.class, args);
    }
}
