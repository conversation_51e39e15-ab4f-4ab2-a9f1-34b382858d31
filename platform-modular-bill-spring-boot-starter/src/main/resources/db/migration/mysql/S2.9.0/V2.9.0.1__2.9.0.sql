CREATE TABLE if not EXISTS  yongan_category (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_level4_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '四级分类名称',
  `category_level4` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '四级分类ID',
  `category_level1` int NOT NULL COMMENT '一级分类ID',
  `category_level1_name` varchar(50) NOT NULL COMMENT '一级分类名称',
  `category_level2` varchar(10) NOT NULL COMMENT '二级分类ID',
  `category_level2_name` varchar(50) NOT NULL COMMENT '二级分类名称',
  `category_level3` varchar(10) NOT NULL COMMENT '三级分类ID',
  `category_level3_name` varchar(50) NOT NULL COMMENT '三级分类名称',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `is_deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标识',
  `tenant_id` bigint DEFAULT '0' COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='永安对接分类表';

CREATE TABLE if not EXISTS `lup_label_yongan_category` (
      `id` bigint NOT NULL AUTO_INCREMENT,
      `label_id` bigint DEFAULT NULL,
      `yongan_category_id` bigint NOT NULL  COMMENT '永安分类ID',
      `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户Id',
      code_value   int NOT NULL DEFAULT '0' COMMENT '类型  1 ai识别  2人工提报',
      `create_by` bigint DEFAULT '0' COMMENT '创建人',
      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `update_by` bigint DEFAULT '0' COMMENT '更新人',
      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
      `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1932397583073058819 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='标签关联永安分类表';
