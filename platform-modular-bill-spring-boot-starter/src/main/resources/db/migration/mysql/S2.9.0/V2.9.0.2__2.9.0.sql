delete from yongan_category  ;

INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (1, 2, '建设交通类', '05', '绿化市容', '03', '城市管理', '06', '乱设或损坏户外设施', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (2, 2, '建设交通类', '05', '绿化市容', '03', '城市管理', '07', '跨门营业', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (3, 2, '建设交通类', '05', '绿化市容', '03', '城市管理', '08', '文明施工措施不落实', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (4, 2, '建设交通类', '05', '绿化市容', '03', '城市管理', '15', '占道无证照经营', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (5, 2, '建设交通类', '05', '绿化市容', '03', '城市管理', '17', '擅自占用道路堆物、施工', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (6, 2, '建设交通类', '05', '绿化市容', '03', '城市管理', '18', '露天焚烧', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (7, 2, '建设交通类', '10', '住房保障', '05', '房屋管理', '07', '占用消防通道违章停车', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (8, 2, '建设交通类', '10', '住房保障', '05', '房屋管理', '08', '违法搭建建筑物、构筑物', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (9, 3, '经济综合类', '06', '农业', '03', '农业相关执法', '05', '焚烧秸秆', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (10, 2, '建设交通类', '05', '绿化市容', '04', '市容市貌', '13', '机动车乱停放、非机动车乱停放', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (11, 2, '建设交通类', '05', '绿化市容', '04', '市容市貌', '12', '路面积水、污水冒溢、粪便冒溢', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (12, 2, '建设交通类', '05', '绿化市容', '04', '市容市貌', '10', '道路破损', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (13, 2, '建设交通类', '05', '绿化市容', '04', '市容市貌', '09', '街头散发小广告', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (14, 2, '建设交通类', '05', '绿化市容', '02', '环境卫生', '09', '违规处置渣土', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (15, 2, '建设交通类', '05', '绿化市容', '02', '环境卫生', '08', '道路保洁', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (16, 2, '建设交通类', '05', '绿化市容', '05', '绿地绿化', '08', '行道树', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (17, 2, '建设交通类', '08', '水务', '03', '河道流域管理', '08', '河道污染', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (18, 2, '建设交通类', '10', '住房保障', '20', '住宅小区违法违规', '07', '小区存在违法搭建', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (19, 2, '建设交通类', '10', '住房保障', '20', '住宅小区违法违规', '17', '未在建筑垃圾堆放点设有临时停放牌和公示牌', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (20, 2, '建设交通类', '08', '水务', '19', '涉河事项监管', '02', '疑似违章施工', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);
INSERT INTO `lup_biz_aep`.`yongan_category` (`id`, `category_level1`, `category_level1_name`, `category_level2`, `category_level2_name`, `category_level3`, `category_level3_name`, `category_level4`, `category_level4_name`, `create_by`, `create_time`, `update_time`, `update_by`, `is_deleted`, `tenant_id`) VALUES (21, 2, '建设交通类', '08', '水务', '19', '涉河事项监管', '08', '污水直排', NULL, '2025-06-12 15:59:56', '2025-06-12 15:59:56', NULL, '0', 0);



CREATE TABLE if not EXISTS `lup_event_sms_grade` (
       `id` bigint NOT NULL AUTO_INCREMENT,
       `label_id` bigint DEFAULT NULL,
       `type` int DEFAULT NULL COMMENT '类型 1：一般(待办，要求事件计划执行，会发送事件处置的汇总通知短信；) 2紧急(速办，要求事件尽快处理，会实时发送巡查和处置短信。)',
       `patrol_sms` int DEFAULT NULL COMMENT '类型 1：巡查短信是否开  1开 0不开',
       `handle_sms` int DEFAULT NULL COMMENT '类型 处置短信是否开   1开 0不开',
       code_value   int NOT NULL DEFAULT '0' COMMENT '类型  1 ai识别  2人工提报 3比对识别  4飞行素材 5设备 11三方数据',
       `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户Id',
       `create_by` bigint DEFAULT '0' COMMENT '创建人',
       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       `update_by` bigint DEFAULT '0' COMMENT '更新人',
       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
       `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='处置分级短信';