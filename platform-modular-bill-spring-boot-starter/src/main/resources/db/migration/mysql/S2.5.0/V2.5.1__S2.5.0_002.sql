CREATE TABLE if not exists `lup_app` (
                           `id` bigint NOT NULL COMMENT '主键id',
                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                           `create_by` bigint DEFAULT NULL COMMENT '创建人',
                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                           `update_by` bigint DEFAULT NULL COMMENT '修改人',
                           `deleted` int DEFAULT '0' COMMENT '删除状态：0未删除 1已删除',
                           `tenant_id` bigint DEFAULT NULL COMMENT '租户id ',
                           `name` varchar(255) COLLATE utf8mb4_german2_ci DEFAULT NULL COMMENT '授权应用名称',
                           `app_key` varchar(255) COLLATE utf8mb4_german2_ci DEFAULT NULL COMMENT '应用key',
                           `app_secret` varchar(255) COLLATE utf8mb4_german2_ci DEFAULT NULL COMMENT '应用密钥',
                           `valid_date_time` datetime DEFAULT NULL COMMENT '有效日期 为null的情况下 不受期限',
                           `app_desc` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci DEFAULT NULL COMMENT '备注描述',
                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_german2_ci ROW_FORMAT=DYNAMIC COMMENT='应用记录';