

ALTER TABLE `lup_bill` ADD COLUMN `pending_state` int NULL DEFAULT 0 COMMENT '后期处理（挂起）状态：0  1已申请后期处理 待审批 2 已审批 3已驳回' ;

CREATE TABLE if not EXISTS `lup_bill_pending`  (
                                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                                   `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除',
                                                   `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
                                                   `create_by` bigint NULL DEFAULT 0 COMMENT '创建人',
                                                   `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                   `update_by` bigint NULL DEFAULT 0 COMMENT '更新人',
                                                   `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                                   `bill_id` bigint NULL DEFAULT NULL COMMENT 'lup_bill表主键id',
                                                   `process_task_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程当前任务',
    `apply_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请原因',
    `approval_user_id` bigint NULL DEFAULT NULL COMMENT '审核人员ID',
    `approval_state` int NULL DEFAULT 0 COMMENT '审核状态 默认0   通过 2 已驳回 3\n',
    `approval_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
    `reject_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '驳回原因',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE = InnoDB AUTO_INCREMENT = 1877552102020272131 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工单挂起记录表' ROW_FORMAT = DYNAMIC;

CREATE TABLE if not EXISTS `lup_bill_pending_file`  (
                                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                                        `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除',
                                                        `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
                                                        `create_by` bigint NULL DEFAULT 0 COMMENT '创建人',
                                                        `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                        `update_by` bigint NULL DEFAULT 0 COMMENT '更新人',
                                                        `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                                        `pending_id` bigint NULL DEFAULT NULL COMMENT '挂起申请记录ID',
                                                        `file_id` bigint NULL DEFAULT NULL COMMENT '流程当前任务',
                                                        PRIMARY KEY (`id`) USING BTREE
    ) ENGINE = InnoDB AUTO_INCREMENT = 1876887438693842946 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工单挂起记录表 文件关联记录表' ROW_FORMAT = DYNAMIC;


ALTER TABLE `lup_data` ADD COLUMN `generate_bill` int NULL DEFAULT 0 COMMENT '是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）' ;

CREATE TABLE if not EXISTS `lup_data_push_log`  (
                                                    `id` bigint NOT NULL COMMENT '主键id',
                                                    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                    `create_by` bigint NULL DEFAULT NULL COMMENT '创建人',
                                                    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                                    `update_by` bigint NULL DEFAULT NULL COMMENT '修改人',
                                                    `deleted` int NULL DEFAULT 0 COMMENT '删除状态：0未删除 1已删除',
                                                    `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id ',
                                                    `data_id` bigint NULL DEFAULT NULL COMMENT 'lup_data主键ID',
                                                    `push_state` int NULL DEFAULT 0 COMMENT '推送状态 0未推送   1已推送   -1推送失败',
                                                    `push_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL COMMENT '推送结果信息',
                                                    `push_stage` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '推送策略',
    `push_mode` int NULL DEFAULT 1 COMMENT '推送模式1   已甄别 2全量 ',
    `push_auto` int NULL DEFAULT 0 COMMENT '是否自动推 0否 1是',
    `push_event_flow` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '推送事件的流程 默认通用流程 DefaultFlow、推送关联流程（杨汛桥项目的事件流程）PushEventFlow',
    `data_origin` int NULL DEFAULT NULL COMMENT '数据来源类型：1 飞控AI提取   2 飞控人工提报 3 比对系统',
    `bill_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '工单编号',
    `bill_id` bigint NULL DEFAULT NULL COMMENT '工单ID',
    `generate_bill` int NULL DEFAULT 0 COMMENT '是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）',
    `push_time` datetime NULL DEFAULT NULL COMMENT '推送时间',
    `pushable` int NULL DEFAULT 1 COMMENT '是否可以重新推送 0否 1是',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_german2_ci COMMENT = '事件数据推送记录' ROW_FORMAT = DYNAMIC;

CREATE TABLE if not EXISTS `lup_data_translate`  (
                                                     `id` bigint NOT NULL COMMENT '主键id',
                                                     `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                     `create_by` bigint NULL DEFAULT NULL COMMENT '创建人',
                                                     `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                                     `update_by` bigint NULL DEFAULT NULL COMMENT '修改人',
                                                     `deleted` int NULL DEFAULT 0 COMMENT '删除状态：0未删除 1已删除',
                                                     `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id ',
                                                     `data_id` bigint NULL DEFAULT NULL COMMENT '数据ID',
                                                     `data_origin` int NULL DEFAULT NULL COMMENT '数据来源类型：1 飞控AI提取   2 飞控人工提报 3 比对系统',
                                                     `bill_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '工单编号',
    `bill_id` bigint NULL DEFAULT NULL COMMENT '工单ID',
    `generate_bill` int NULL DEFAULT 0 COMMENT '是否已甄别 生成工单，默认0：0否（不生成工单） 1是（生成工单）',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_german2_ci COMMENT = '事件数据传输记录表' ROW_FORMAT = DYNAMIC;



ALTER TABLE `lup_biz_aep`.`lup_flow_entity` MODIFY COLUMN `four_success` int NULL DEFAULT NULL COMMENT '四平台处理结果 1处理成功  0 退回' AFTER `handle_type`;



ALTER TABLE `lup_biz_aep`.`lup_label` ADD COLUMN `event_type_id` bigint NULL DEFAULT NULL COMMENT '事件类型id' AFTER `name`;

ALTER TABLE `lup_biz_aep`.`lup_label` ADD COLUMN `event_type_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '事件类型编码' AFTER `event_type_id`;

