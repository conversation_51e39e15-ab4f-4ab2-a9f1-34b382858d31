DROP VIEW v_bill_data;
CREATE VIEW   v_bill_data as
select year(`bill`.`create_time`) AS `bill_year`,lpad(month(`bill`.`create_time`),2,'0') AS `bill_month`,cast(`bill`.`create_time` as date) AS `bill_date`,`bill`.`id` AS `id`,`bill`.`data_id` AS `data_id`,`bill`.`area_name` AS `area_name`,`bill`.`area_code` AS `area_code`,`bill`.`grid_name` AS `grid_name`,`bill`.`grid_code` AS `grid_code`,`bill`.`flow_entity_id` AS `flow_entity_id`,`bill`.`data_origin` AS `data_origin`,`bill`.`bill_number` AS `bill_number`,`bill`.`create_time` AS `create_time`,`ldata`.`extra_data` AS `extra_data`,`ldata`.`location_lat` AS `location_lat`,`ldata`.`location_lng` AS `location_lng`,`ldata`.`happened_time` AS `happened_time`,`ldata`.`fly_task_id` AS `fly_task_id`,`ldata`.`fly_task_number` AS `fly_task_number`,`ldata`.`fly_task_name` AS `fly_task_name`,`ldata`.`air_line_name` AS `air_line_name`,`ldata`.`device_id` AS `device_id`,`ldata`.`device_name` AS `device_name`,`ldata`.`device_sn` AS `device_sn`,`bill`.`tenant_id` AS `tenant_id`,`flow`.`process_definition_id` AS `process_definition_id`,`flow`.`process_task_id` AS `process_task_id`,`activity`.`name` AS `name`,`flow`.`flow_state` AS `flow_state`,`activity`.`state` AS `flow_code`,(case `flow`.`flow_state` when '4' then '1' else '0' end) AS `bill_deal_result` from (((`lup_bill` `bill` join `lup_data` `ldata` on((`bill`.`data_id` = `ldata`.`id`))) join `lup_flow_entity` `flow` on((`flow`.`id` = `bill`.`flow_entity_id`))) left join `lup_flow_activity` `activity` on((`activity`.`process_task_id` = `flow`.`process_task_id`))) where ((`bill`.`deleted` = '0') and (`bill`.`usable` = '1') and (`ldata`.`deleted` = 0) and (`flow`.`flow_state` > 0)) order by `bill`.`id` desc