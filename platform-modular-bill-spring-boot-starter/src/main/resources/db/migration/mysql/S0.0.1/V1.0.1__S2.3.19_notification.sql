
CREATE TABLE if not EXISTS   `lup_bill_notification`  (
                                                                      `id` bigint NOT NULL COMMENT '主键id',
                                                                      `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                                      `create_by` bigint NULL DEFAULT NULL COMMENT '创建人',
                                                                      `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                                                      `update_by` bigint NULL DEFAULT NULL COMMENT '修改人',
                                                                      `deleted` int NULL DEFAULT 0 COMMENT '删除状态：0未删除 1已删除',
                                                                      `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id ',
                                                                      `bill_id` bigint NULL DEFAULT NULL COMMENT '事件数据id',
                                                                      `flow_id` bigint NULL DEFAULT NULL COMMENT '流程id',
                                                                      `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
                                                                      `status` int NULL DEFAULT 0 COMMENT '状态：0 未推给中心 1已经推给中心',
                                                                      `process_task_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '流程当前任务',
    `flow_state` int NULL DEFAULT NULL COMMENT '流程状态',
    `timer_sms` int NULL DEFAULT 0 COMMENT '定时类短信  0 否  1 是',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_german2_ci COMMENT = '事件通知表' ROW_FORMAT = DYNAMIC;



ALTER TABLE   `lup_data` ADD COLUMN  `fly_video_start_time` datetime NULL DEFAULT NULL COMMENT '飞行视频录制的开始时间' AFTER `push_msg`;

ALTER TABLE   `lup_data` ADD COLUMN `org_id` bigint NULL DEFAULT NULL COMMENT '组织机构ID' AFTER `fly_video_start_time`;

CREATE TABLE if not EXISTS   `lup_flow_entity_time_out`  (
                                                                         `id` bigint NOT NULL COMMENT '主键id',
                                                                         `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
                                                                         `create_by` bigint NULL DEFAULT 0 COMMENT '创建人',
                                                                         `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                                         `update_by` bigint NULL DEFAULT 0 COMMENT '更新人',
                                                                         `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                                                         `flow_entity_id` bigint NULL DEFAULT NULL COMMENT '流程id数据',
                                                                         `process_task_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程当前任务',
    `process_start_time` datetime NULL DEFAULT NULL COMMENT '阶段开始时间',
    `dead_line` datetime NULL DEFAULT NULL COMMENT '时间到期时间',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '流程阶段关联超时时间记录表' ROW_FORMAT = DYNAMIC;

CREATE TABLE if not EXISTS   `lup_flow_sms`  (
                                                             `id` bigint NOT NULL COMMENT '主键id',
                                                             `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                             `create_by` bigint NULL DEFAULT NULL COMMENT '创建人',
                                                             `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                                             `update_by` bigint NULL DEFAULT NULL COMMENT '修改人',
                                                             `deleted` int NULL DEFAULT 0 COMMENT '删除状态：0未删除 1已删除',
                                                             `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户id ',
                                                             `user_id` bigint NULL DEFAULT NULL COMMENT '短信接收用户ID',
                                                             `type` int NULL DEFAULT NULL COMMENT '类型：1 事件处理；2 事件驳回；',
                                                             `timer` int NULL DEFAULT NULL COMMENT '是否实时发送：0否 1是',
                                                             `state` int NULL DEFAULT NULL COMMENT '是否提交给短信发送 0否 1是',
                                                             `sms_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '短信类型编码',
    `content_uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '短信内容唯一标识 用于定时发数据统一调度分组的',
    `sms_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '短信内容',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_german2_ci COMMENT = '记录流程动作发送出去的短信' ROW_FORMAT = DYNAMIC;



CREATE TABLE if not EXISTS   `lup_label_flow`  (
                                                               `id` bigint NOT NULL AUTO_INCREMENT,
                                                               `label_id` bigint NULL DEFAULT NULL,
                                                               `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
                                                               `create_by` bigint NULL DEFAULT 0 COMMENT '创建人',
                                                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                               `update_by` bigint NULL DEFAULT 0 COMMENT '更新人',
                                                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                               `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除',
                                                               `flow_state` int NULL DEFAULT NULL COMMENT '流程节点',
                                                               `flow_activity_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '流程节点ID',
    `time_limit` decimal(10, 1) NULL DEFAULT NULL COMMENT '时间期限 单位小时',
    `time_switch` int NULL DEFAULT NULL COMMENT '开关 默认 0 未开启  1 已开启 ',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE = InnoDB AUTO_INCREMENT = 1846131360512327682 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '标签关联流程数据' ROW_FORMAT = DYNAMIC;



ALTER TABLE   `lup_source` ADD COLUMN `source_type` int NOT NULL DEFAULT 1 COMMENT '瓦片来源类型：1 自定义瓦片 2机场瓦片' AFTER `source_name`;

ALTER TABLE   `lup_source` ADD COLUMN `fly_achievement_id` bigint NULL DEFAULT NULL COMMENT '机场飞行成果id' AFTER `source_type`;

ALTER TABLE   `lup_source` ADD COLUMN `original_tile_source_id` bigint NULL DEFAULT NULL COMMENT '原始的瓦片资源id 可以是机场端的成果id  可以是比对里自定义瓦片的id' AFTER `fly_achievement_id`;

ALTER TABLE   `lup_source` ADD COLUMN `org_id` bigint NULL DEFAULT NULL COMMENT '组织机构id' AFTER `original_tile_source_id`;

