spring:
  profiles:
    active: local
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      config:
        namespace: e356ffde-10a8-4789-95e6-bf1392ce1927
        server-addr: 192.168.100.190:8848
        group: DEFAULT_GROUP
        file-extension: yaml
        username: nacos
        password: mascj@Lm177
        shared-configs:
          - data-id: liangma.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: liangma-core-local.yaml
            group: DEFAULT_GROUP
            refresh: true
      discovery:
        namespace: e356ffde-10a8-4789-95e6-bf1392ce1927
        server-addr: 192.168.100.190:8848
        group: DEFAULT_GROUP
        username: nacos
        password: mascj@Lm177
        watch:
          enabled: false
    sentinel:
      # 是否开启sentinel
      enabled: true
      filter:
        enabled: true
      # 取消Sentinel控制台懒加载
      eager: true
#      transport:
#        port: @sentinel.port@
#        dashboard: 127.0.0.1:9201
feign:
  sentinel:
    enabled: true