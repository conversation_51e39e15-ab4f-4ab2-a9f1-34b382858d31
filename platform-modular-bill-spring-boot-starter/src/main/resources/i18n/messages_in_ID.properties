event.lec.flow.none.flow=<PERSON>lakan spesifika<PERSON>an aliran objek proses
event.lec.flow.none.formData=Silakan spesifikasikan formulir data formData
event.lec.flow.none.processDefinitionId=Definisi proses untuk urutan kerja saat ini adalah prosesDefinitionId %s, id
event.common.tip.none.bizCode=Parameter bizCode tidak dinyatakan
event.common.tip.none.processDefinitionId=Proses parameterDefinitionId tidak dinyatakan
event.tip.empty.variables=Variabel variabel dasar tidak dapat %s
event.lec.gate.none.var=Hasil kalkulasi dari kondisi gateway {} tidak dapat %s
event.lec.gate.none.analysis.var=Analisasi ekspresi SpEL gagal untuk kondisi gateway {}, ekspresi
event.lec.gate.none.analysis.exp.var=Kecualian terjadi saat menghitung kondisi gateway {}, ekspresi
event.lec.none.activityPointer=Silakan nyatakan aktivitas nama nodeId
event.lec.none.activity.detail=Node tidak ada dalam konfigurasi proses: {}
event.lec.flow.none.activity.name=Nod arsip tidak dinyatakan dalam konfigurasi proses: processDefinitionId
event.lec.flow.none.activityState=%s. AktivitasStates tidak dapat %s
event.lec.flow.none.gatewayCondition=%s. Kondisi Gateway tidak dapat %s
event.lec.flow.configProvider=Gagal mendapatkan instance IXlmConfigService
event.lec.flow.variableInfo=%s. VariableInfo tidak dapat %s
event.common.xce=XlmConfigEnvironment
event.common.tip.none.code=Kode %s
event.common.tip.fail.export.excel=Ekspor meja Excel gagal!
event.common.tip.none.path=Laluan %s
event.common.tip.none.file.exist=Berkas tidak ada, path
event.common.tip.fail.file.upload=Pemuat berkas gagal
event.tag.none.metadata=Metadata %s
event.tag.none.tagName=FlowStates tidak dapat %s, tagName
event.common.none.comparePictureUrl=Lapangan hilang membandingkanPictureUrl, jika dan hanya jika membandingkanType
event.common.none.originalDataLogo=Parameter hilang, identifikator unik dari data asli tidak dapat %s
event.common.none.exist.originalDataLogo=Data %s:
event.common.none.del.originalDataLogo=Peristiwa telah tidak sah:
event.common.none.compareAirlineTaskId=dibandingkan dengan misi penerbangan %s,
event.common.none.compareTile=Compare tiles %s
event.common.none.reportTemplate=Pertanyaan Templat %s
event.common.none.app=Pertanyaan Aplikasi %s
event.common.none.org=Pertanyaan institusi %s
event.common.none.query=Pertanyaan rekaman %s
event.common.none.translateObj=Melewati objek %s
event.common.fail.optAnalysis=Operasi penghuraian gagal, waktu
event.common.none.key=Kunci %s
event.common.none.data.export=Tidak ada data yang tersedia untuk eksport
event.flow.fail.activityState=Gagal mendapatkan XlmActivityState, processDefinitionId
event.flow.fail.billState=Gagal mendapatkan WorkBillActivity, workBillId
event.common.fail.update=Pemutakhiran database gagal, silakan coba lagi nanti
event.flow.tip.starting=Inisiasi perintah kerja dalam proses
event.flow.fail.bizCategory=Gagal memperoleh XlmBizCategory
event.flow.fail.start=Proses memulai gagal, id
event.flow.fail.task.commit=Pengiriman tugas gagal: processInstanceId
event.flow.fail.task.exp=Pengiriman tugas gagal, pengecualian internal yang dapat mengalir: processInstanceId
event.flow.none.submit.task=Silakan nyatakan submitTaskDto
event.flow.none.taskId=Silakan spesifikasikan taskId
event.flow.none.flowId=FlowEntity tidak ada, Id
event.flow.task.none.query=Tugas saat ini tidak ditemukan: taskId
event.flow.task.none.commit.repeat=Tugas saat ini telah berakhir dan tidak dapat dikirim kembali: taskId
event.common.none.user=Informasi Pengguna %s
event.common.none.role=Informasi peran pengguna %s
event.common.none.flowVal=Nilai parameter %s
event.common.none.flow.variable=Variabel parameter %s
event.flow.fail.update=Gagal memperbarui nilai properti FlowEntity, flowId
event.bill.fail.update=Gagal memperbaharui nilai properti WorkBill, Id
event.bill.gis.format.error=Koordinat latitud dan longitud tidak sesuai dengan aturan input: format standar adalah: [31.426894118.070765]
event.bill.grid.locate.error=Tidak ditemukan jaringan posisi, tidak dapat menemukan
event.bill.grid.none.change=Jaringan perintah kerja belum berubah
event.bill.none.exist=Peristiwa tidak ada
event.annotation.point.exist=%S sudah ada
event.annotation.point.none.exist=%S tidak ada
event.annotation.point.fail.exist=Penghapusan %S gagal
event.label.none.type=Jenis %s
event.grid.none.change.right=Otorisasi pengguna jaringan belum berubah, silakan konfirmasi lagi
event.rang.name.duplication=Duplikasi Nama
event.range.empty.file=Berkas %s
event.range.error.file.format=Format berkas tidak benar
event.bill.file.check=Tidak ada properti atau geometri ditetapkan
event.sync.task.repeat.exe=Tugas saat ini sedang berlangsung, silakan coba lagi nanti
event.sync.none.id=Kunci utama tidak dapat %s
event.sync.none.state=SyncState %s
event.sync.none.context=Konteks Aplikasi %s
event.fail.generate.signature=Gagal menghasilkan tanda tangan
event.fail.grid.delNoneSupport=Ada perintah kerja penghapusan di bawah jaringan ini, dan penghapusan jaringan ini tidak didukung
event.fail.grid.del.child=Jaringan ini memiliki jaringan subordinat dan penghapusan jaringan ini tidak didukung
event.grid.config.user=Konfigurasi personal jaringan berhasil
event.grid.none.unit.name=Silakan isi nama grid
event.grid.empty.original.logo=Identifikasi unik dari data asli tidak dapat menjadi string %s
event.common.empty=Kosong
event.common.required=Diperlukan
event.common.marker.point=Titik anotasi
event.flow.patrol=Untuk diperiksa
event.flow.pushing=Untuk didorong
event.flow.handing=Memproses
event.flow.handle=disposisi
event.flow.judging=Audit
event.flow.archived=berkas
event.flow.person=personal
event.flow.handleType=Metode pembuangan
event.flow.check=penemuan dari audit
event.flow.handleqk=Situasi pembuangan
event.flow.event_has=Apakah peristiwa itu ada
event.flow.yes=Kewujudan
event.flow.no=Tidak ada
event.flow.inspection_situation=Situasi inspeksi
event.flow.reason_rejection=Alasan untuk menolak
event.flow.extend_date=Tambah tanggal
event.flow.not_disposed=Tidak dibuang
event.flow.no_need_disposal=Tidak perlu dibuang
event.flow.has_disposal=Dibuang
event.flow.later_disposal=Penghapusan pos
event.flow.reject=Ditolak
event.flow.pass=adopsi
event.flow.deal_result=Memproses hasil
event.flow.deal_complete=Proses selesai
event.flow.no_push=Jangan mendorong
event.common.distance.unit.meter=Meter
event.common.distance.unit.kilometer=kilometer
event.common.date.monday=Senin
event.common.date.tuesday=Selasa
event.common.date.wednesday=Rabu
event.common.date.thursday=Kamis
event.common.date.friday=Jumat
event.common.date.saturday=Sabtu
event.common.date.sunday=Minggu
event.common.date.january=Januari
event.common.date.february=Februari
event.common.date.march=Maret
event.common.date.april=April
event.common.date.may=Mei
event.common.date.june=Juni
event.common.date.july=Juli
event.common.date.august=Agustus
event.common.date.september=September
event.common.date.october=Oktober
event.common.date.november=November
event.common.date.december=Desember
event.flow.comment.client=Pengoperasian
event.flow.comment.remark=Keterangan
event.flow.comment.source=Deskripsi materi
event.common.report.field.eventName=Nama Peristiwa
event.common.report.field.createTime=Waktu penciptaan
event.common.report.field.eventTag=Tag peristiwa
event.common.report.field.area=daerah
event.common.report.field.gridUnit=grid
event.common.report.field.eventOrigin=Sumber peristiwa
event.common.report.field.square=area
event.common.report.field.eventStatus=Status Perintah Kerja
event.common.report.field.eventLng=longitude
event.common.report.field.eventLat=latitude
event.common.report.field.locationAddress=Lokasi Kejadian
event.common.report.field.handleTyeName=Metode Penanganan
event.common.report.filename=peristiwa
event.common.unknownAreaName=Daerah tidak diketahui
event.common.return.success=sukses
event.common.report.field.hasEvent=Apakah itu ada
event.common.report.field.patrolContent=Konten patroli
event.common.report.field.eventUrl=Foto Inspeksi
event.common.report.field.eventUrlDisplayName=Tautan (Pratinjau Foto Patroli)
event.data.commit.repeat=Pelarangan Pengiriman Ulang Acara
event.bill.pending.tip=Pengolahan pascaproduksi
event.bill.pending.approval=Peninjauan
event.bill.pending.approval.access=Disetujui
event.bill.pending.approval.viewing=Sedang Diperiksa
event.bill.pending.approval.reject=Ditolak
event.bill.pending.approval.apply=Permohonan
event.bill.common.desc=Deskripsi
event.bill.pending.auto.del=Dibatalkan Secara Otomatis

event.bill.generateBill=Telah Divalidasi
event.bill.generateBillNothing=Belum Divalidasi
event.bill.push.success=Pengiriman Berhasil
event.bill.push.fail=Pengiriman Gagal
event.bill.push.filename=Rekam Jejak Pengiriman
event.bill.push.log.excel.dataId=Nomor Kejadian
event.bill.push.log.excel.billNumber=Nomor Tiket Kerja
event.bill.push.log.excel.generateBillStatus=Status Validasi
event.bill.push.log.excel.pushState=Status Pengiriman
event.bill.push.log.excel.pushMsg=Alasan Kegagalan
event.bill.push.log.excel.pushTime=Waktu Pengiriman