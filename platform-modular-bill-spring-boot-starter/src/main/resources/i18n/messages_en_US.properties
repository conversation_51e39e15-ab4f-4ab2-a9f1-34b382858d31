event.lec.flow.none.flow=Please specify the process object.Flow
event.lec.flow.none.formData=Please specify the form data.formData
event.lec.flow.none.processDefinitionId=Process definition of the current work orderprocessDefinitionId%s, id
event.common.tip.none.bizCode=No parameters specifiedbizCode
event.common.tip.none.processDefinitionId=No parameters specifiedprocessDefinitionId
event.tip.empty.variables=Basic variablesVariables cannot %s
event.lec.gate.none.var=Gateway conditionsThe calculation result of {} cannot be %s
event.lec.gate.none.analysis.var=Gateway conditions{}'SSpELExpression parsing failed, expression{}, {}
event.lec.gate.none.analysis.exp.var=Calculate gateway conditionsAn exception occurs at {}, expression{},baseVariables{} ,{}
event.lec.none.activityPointer=Please specify the node name.activityId
event.lec.none.activity.detail=There are no nodes in the process configuration:{}
event.lec.flow.none.activity.name=The archive node is not specified in the process configuration:processDefinitionId{}
event.lec.flow.none.activityState=%s.activityStatesCan't %s
event.lec.flow.none.gatewayCondition=%s.gatewayConditionsCan't %s
event.lec.flow.configProvider=ObtainIXlmConfigServiceInstance failed
event.lec.flow.variableInfo=%s.variableInfoCan't %s
event.common.xce=XlmConfigEnvironment
event.common.tip.none.code=Code%small size
event.common.tip.fail.export.excel=DeriveThe excel form failed!
event.common.tip.none.path=Path%small size
event.common.tip.none.file.exist=The file does not exist., path
event.common.tip.fail.file.upload=File upload failed
event.tag.none.metadata=Metadata%s
event.tag.none.tagName=flowStatesCan't %s,tagName
event.common.none.comparePictureUrl=Missing fieldscomparePictureUrl, if and only ifcompareType2 o'clock
event.common.none.originalDataLogo=Without parameters, the unique identification of the original data cannot be %s
event.common.none.exist.originalDataLogo=Data %s:
event.common.none.del.originalDataLogo=The incident has been invalidated:
event.common.none.compareAirlineTaskId=Comparison of aviation missions %s,
event.common.none.compareTile=Contrast tiles %s
event.common.none.reportTemplate=Template inquiry %s
event.common.none.app=Application inquiry %s
event.common.none.org=Institutional inquiry %s
event.common.none.query=Enquiry record %s
event.common.none.translateObj=Transfer object %s
event.common.fail.optAnalysis=Operation analysis failed,Time
event.common.none.key=Key %s mall size
event.common.none.data.export=There is no data that can be exported.
event.flow.fail.activityState=ObtainXlmActivityStateFailure,processDefinitionId{},activityId{}
event.flow.fail.billState=ObtainWorkBillActivityFailure,workBillId{},activityId{}
event.common.fail.update=Failed to update the databasePlease try again later.
event.flow.tip.starting=WorkerSingle startCentre
event.flow.fail.bizCategory=ObtainXlmBizCategoryBe defeated
event.flow.fail.start=Process start failed, id{},failReason{}
event.flow.fail.task.commit=Failed task submission:processInstanceId{}, message:{}
event.flow.fail.task.exp=Failed to submit the task,Flowable internal exception:processInstanceId{}, message:{}
event.flow.none.submit.task=Please specifysubmitTaskDto
event.flow.none.taskId=Please specifytaskId
event.flow.none.flowId=It doesn't existFlowEntity, Id
event.flow.task.none.query=The current task has not been queried:taskId{}
event.flow.task.none.commit.repeat=The current task is over and cannot be submitted repeatedly:taskId{}
event.common.none.user=User information %s
event.common.none.role=User role information %s
event.common.none.flowVal=ParameterValues%s
event.common.none.flow.variable=ParameterVariables%s
event.flow.fail.update=RenewFlowEntityThe attribute value failed,flowId{}
event.bill.fail.update=RenewWorkBillAttribute value failed, Id{}
event.bill.gis.format.error=Longitude and latitude coordinates do not meet the input rules=the standard format is=[31.426894,118.070765]
event.bill.grid.locate.error=The positioning grid has not been found, and it cannot be located.
event.bill.grid.none.change=There is no change in the work order grid.
event.bill.none.exist=The event does not exist.
event.annotation.point.exist=%s already exists
event.annotation.point.none.exist=%s does not exist
event.annotation.point.fail.exist=%s deletion failed
event.label.none.type=Type %s
event.grid.none.change.right=There is no change in the grid user authorization, please reconfirm.
event.rang.name.duplication=Duplicate names
event.range.empty.file=Document %s
event.range.error.file.format=The file format is wrong.
event.bill.file.check=Not set upProperties, geometry
event.sync.task.repeat.exe=The current task is in progress, please try again later.
event.sync.none.id=The primary key cannot %s
event.sync.none.state=syncState%s
event.sync.none.context=applicationContext%s
event.fail.generate.signature=Failed to generate signature
event.fail.grid.delNoneSupport=There is a disposal order under this grid, and deleting the grid is not supported.
event.fail.grid.del.child=The grid also has a lower grid, and the deletion of the grid is not supported.
event.grid.config.user=Successful grid staffing
event.grid.none.unit.name=Please fill in the grid name.
event.grid.empty.original.logo=The unique identification of the original data cannot %s string
event.common.empty=It is empty
event.common.required=Required
event.common.marker.point=Annotation points
event.flow.patrol=Pending inspection
event.flow.handle=Processing
event.flow.judging=Reviewing
event.flow.archived=Archived
event.flow.person=personal
event.flow.handleType=Disposal method
event.flow.check=Review result
event.flow.handleqk=Disposal situation
event.flow.event_has=Event exists
event.flow.yes=Yes
event.flow.no=No
event.flow.inspection_situation=Inspection situation
event.flow.reason_rejection=Rejection reason
event.flow.extend_date=Extended date
event.flow.not_disposed=Not disposed
event.flow.no_need_disposal=No need for disposal
event.flow.has_disposal=Already disposed
event.flow.later_disposal=Later disposal
event.flow.reject=reject
event.flow.pass=pass
event.common.distance.unit.meter=m
event.common.distance.unit.kilometer=km
event.common.date.monday=Mon
event.common.date.tuesday=Tue
event.common.date.wednesday=Wed
event.common.date.thursday=Thu
event.common.date.friday=Fri
event.common.date.saturday=Sat
event.common.date.sunday=Sun
event.common.date.january=Jan
event.common.date.february=Feb
event.common.date.march=Mar
event.common.date.april=Apr
event.common.date.may=May
event.common.date.june=Jun
event.common.date.july=Jul
event.common.date.august=Aug
event.common.date.september=Sep
event.common.date.october=Oct
event.common.date.november=Nov
event.common.date.december=Dec
event.flow.comment.client=Operational
event.flow.comment.remark=Notes
event.flow.comment.source=Material Specs
event.common.report.field.eventName=EventName
event.common.report.field.createTime=CreateTime
event.common.report.field.eventTag=Tag
event.common.report.field.area=Area
event.common.report.field.gridUnit=GridUnit
event.common.report.field.eventOrigin=EventOrigin
event.common.report.field.square=Square
event.common.report.field.eventStatus=EventStatus
event.common.report.field.eventLng=Longitude
event.common.report.field.eventLat=Latitude
event.common.report.field.locationAddress=Event Location
event.common.report.field.handleTyeName=Disposal Method
event.common.report.filename=event
event.common.return.success=success
event.flow.pushing=pushing
event.flow.handing=handing
event.common.unknownAreaName=Unknown Area Name
event.flow.deal_result=Processing Result
event.flow.deal_complete=Processing Complete
event.flow.no_push=No Push
event.common.report.field.hasEvent=Does it exist
event.common.report.field.patrolContent=Patrol content
event.common.report.field.eventUrl=Inspection Photos
event.common.report.field.eventUrlDisplayName=Link (Preview Patrol Photos)
event.data.commit.repeat=Event Prohibition of Resubmission
event.bill.pending.tip=Post-processing
event.bill.pending.approval=Review
event.bill.pending.approval.access=Approved
event.bill.pending.approval.viewing=In Review
event.bill.pending.approval.reject=Rejected
event.bill.pending.approval.apply=Apply
event.bill.common.desc=Description
event.bill.pending.auto.del=Automatically Invalidated

event.bill.generateBill=Verified
event.bill.generateBillNothing=Unverified
event.bill.push.success=Push Successful
event.bill.push.fail=Push Failed
event.bill.push.filename=Push Record
event.bill.push.log.excel.dataId=Event ID
event.bill.push.log.excel.billNumber=Work Order ID
event.bill.push.log.excel.generateBillStatus=Verification Status
event.bill.push.log.excel.pushState=Push Status
event.bill.push.log.excel.pushMsg=Reason for Failure
event.bill.push.log.excel.pushTime=Push Time