event.lec.flow.none.flow=è¯·æå®æµç¨å¯¹è±¡flow
event.lec.flow.none.formData=è¯·æå®è¡¨åæ°æ®formData
event.lec.flow.none.processDefinitionId=å½åå·¥åçæµç¨å®ä¹processDefinitionId%sï¼id=
event.common.tip.none.bizCode=æªæå®åæ°bizCode
event.common.tip.none.processDefinitionId=æªæå®åæ°processDefinitionId
event.tip.empty.variables=åºç¡åévariablesä¸è½%s
event.lec.gate.none.var=ç½å³æ¡ä»¶{}çè®¡ç®ç»æä¸è½ä¸º%s
event.lec.gate.none.analysis.var=ç½å³æ¡ä»¶{}çSpELè¡¨è¾¾å¼è§£æå¤±è´¥ï¼expression={}, {}
event.lec.gate.none.analysis.exp.var=è®¡ç®ç½å³æ¡ä»¶{}æ¶åçå¼å¸¸ï¼expression={}, baseVariables={} ,{}
event.lec.none.activityPointer=è¯·æå®èç¹åç§°activityId
event.lec.none.activity.detail=æµç¨éç½®ä¸­ä¸å­å¨èç¹ï¼{}
event.lec.flow.none.activity.name=æµç¨éç½®ä¸­æªæå®å½æ¡£èç¹ï¼processDefinitionId={}
event.lec.flow.none.activityState=%s.activityStatesä¸è½%s
event.lec.flow.none.gatewayCondition=%s.gatewayConditionsä¸è½%s
event.lec.flow.configProvider=è·åIXlmConfigServiceå®ä¾å¤±è´¥
event.lec.flow.variableInfo=%s.variableInfoä¸è½%s
event.common.xce=XlmConfigEnvironment
event.common.tip.none.code=code%s
event.common.tip.fail.export.excel=å¯¼åºexcelè¡¨æ ¼å¤±è´¥!
event.common.tip.none.path=path%s
event.common.tip.none.file.exist=æä»¶ä¸å­å¨, path =  
event.common.tip.fail.file.upload=æä»¶ä¸ä¼ å¤±è´¥
event.tag.none.metadata=metadata%s
event.tag.none.tagName=flowStatesä¸è½%sï¼tagName =
event.common.none.comparePictureUrl=ç¼ºå°å­æ®µcomparePictureUrl ï¼å½ä¸ä»å½ compareType = 2 æ¶
event.common.none.originalDataLogo=ç¼ºå°åæ°ï¼åå§æ°æ®çå¯ä¸æ è¯ä¸è½%s
event.common.none.exist.originalDataLogo=æ°æ®%sï¼
event.common.none.del.originalDataLogo=äºä»¶å·²ä½åºï¼
event.common.none.compareAirlineTaskId=å¯¹æ¯èªç©ºä»»å¡%s ï¼
event.common.none.compareTile=å¯¹æ¯ç¦ç%s 
event.common.none.reportTemplate=æ¨¡æ¿æ¥è¯¢%s
event.common.none.app=åºç¨æ¥è¯¢%s
event.common.none.org=æºææ¥è¯¢%s
event.common.none.query=æ¥è¯¢è®°å½%s
event.common.none.translateObj=ä¼ éå¯¹è±¡%s
event.common.fail.optAnalysis=æä½è§£æå¤±è´¥ï¼time=
event.common.none.key=key%s
event.common.none.data.export=æ²¡æå¯ä»¥å¯¼åºçæ°æ®
event.flow.fail.activityState=è·åXlmActivityStateå¤±è´¥ï¼processDefinitionId={},activityId={}
event.flow.fail.billState=è·åWorkBillActivityå¤±è´¥ï¼workBillId={},activityId={}
event.common.fail.update=æ´æ°æ°æ®åºå¤±è´¥, è¯·ç¨åéè¯
event.flow.tip.starting=å·¥åå¯å¨ä¸­
event.flow.fail.bizCategory=è·åXlmBizCategoryå¤±è´¥
event.flow.fail.start=æµç¨å¯å¨å¤±è´¥, id={}ï¼failReason={}
event.flow.fail.task.commit=ä»»å¡æäº¤å¤±è´¥ï¼processInstanceId={}, message:{}
event.flow.fail.task.exp=ä»»å¡æäº¤å¤±è´¥,flowableåé¨å¼å¸¸ï¼processInstanceId={}, message:{}
event.flow.none.submit.task=è¯·æå®submitTaskDto
event.flow.none.taskId=è¯·æå®taskId
event.flow.none.flowId=ä¸å­å¨çFlowEntityï¼ Id=
event.flow.task.none.query=æªæ¥è¯¢å°å½åä»»å¡ï¼taskId={}
event.flow.task.none.commit.repeat=å½åä»»å¡å·²ç»æï¼æ æ³éå¤æäº¤ï¼taskId={}
event.common.none.user = ç¨æ·ä¿¡æ¯%s
event.common.none.role = ç¨æ·è§è²ä¿¡æ¯%s
event.common.none.flowVal=åæ°values%s
event.common.none.flow.variable=åæ°variables%s
event.flow.fail.update=æ´æ°FlowEntityå±æ§å¼å¤±è´¥ï¼flowId={}
event.bill.fail.update=æ´æ°WorkBillå±æ§å¼å¤±è´¥ï¼Id={}
event.bill.gis.format.error=ç»çº¬åº¦åæ ä¸ç¬¦åè¾å¥è§åï¼æ åæ ¼å¼ä¸º:  [31.426894,118.070765]
event.bill.grid.locate.error=æ²¡ææ¾å°å®ä½ç½æ ¼ï¼æ æ³å®ä½
event.bill.grid.none.change=å·¥åç½æ ¼æ²¡æåå
event.bill.none.exist=äºä»¶ä¸å­å¨
event.annotation.point.exist=%så·²å­å¨
event.annotation.point.none.exist=%sä¸å­å¨
event.annotation.point.fail.exist=%så é¤å¤±è´¥
event.label.none.type=ç±»å%s
event.grid.none.change.right=ç½æ ¼ç¨æ·æææ²¡æåå¨ï¼è¯·éæ°ç¡®è®¤
event.rang.name.duplication=åç§°éå¤
event.range.empty.file=æä»¶%s
event.range.error.file.format=æä»¶æ ¼å¼æè¯¯
event.bill.file.check=æªè®¾ç½®propertiesãgeometry
event.sync.task.repeat.exe=å½åä»»å¡æ­£å¨è¿è¡ä¸­ï¼è¯·ç¨ååè¯
event.sync.none.id=ä¸»é®ä¸è½%s
event.sync.none.state=syncState%s
event.sync.none.context=applicationContext%s
event.fail.generate.signature=çæç­¾åå¤±è´¥
event.fail.grid.delNoneSupport=è¯¥ç½æ ¼ä¸æå¤ç½®å·¥åï¼ä¸æ¯æå é¤è¯¥ç½æ ¼
event.fail.grid.del.child=è¯¥ç½æ ¼è¿æä¸çº§ç½æ ¼ï¼ä¸æ¯æå é¤è¯¥ç½æ ¼
event.grid.config.user=ç½æ ¼äººåéç½®æå
event.grid.none.unit.name=è¯·å¡«åç½æ ¼åç§°
event.grid.empty.original.logo=åå§æ°æ®çå¯ä¸æ è¯ä¸è½%så­ç¬¦ä¸²
event.common.empty=ä¸ºç©º
event.common.required=å¿å¡«
event.common.marker.point=æ æ³¨ç¹
event.flow.patrol=å¾å·¡æ¥
event.flow.pushing=å¾æ¨é
event.flow.handing=å¤çä¸­
event.flow.handle=å¤ç½®
event.flow.judging=å®¡æ ¸
event.flow.archived=å½æ¡£
event.flow.person=äººå
event.flow.handleType=å¤ç½®æ¹å¼
event.flow.check=å®¡æ ¸ç»æ
event.flow.handleqk=å¤ç½®æåµ
event.flow.event_has=äºä»¶æ¯å¦å­å¨
event.flow.yes=å­å¨
event.flow.no=ä¸å­å¨
event.flow.inspection_situation=å·¡æ¥æåµ
event.flow.reason_rejection=é©³ååå 
event.flow.extend_date=å»¶é¿æ¥æ
event.flow.not_disposed=æªå¤ç½®
event.flow.no_need_disposal=æ éå¤ç½®
event.flow.has_disposal=å·²å¤ç½®
event.flow.later_disposal=åæå¤ç½®
event.flow.reject=é©³å
event.flow.pass=éè¿
event.flow.deal_result=å¤çç»æ
event.flow.deal_complete=å¤çå®æ
event.flow.no_push=ä¸æ¨é
event.common.distance.unit.meter=ç±³
event.common.distance.unit.kilometer=å¬é
event.common.date.monday=å¨ä¸
event.common.date.tuesday=å¨äº
event.common.date.wednesday=å¨ä¸
event.common.date.thursday=å¨å
event.common.date.friday=å¨äº
event.common.date.saturday=å¨å­
event.common.date.sunday=å¨æ¥
event.common.date.january=01æ
event.common.date.february=02æ
event.common.date.march=03æ
event.common.date.april=04æ
event.common.date.may=05æ
event.common.date.june=06æ
event.common.date.july=07æ
event.common.date.august=08æ
event.common.date.september=09æ
event.common.date.october=10æ
event.common.date.november=11æ
event.common.date.december=12æ
event.flow.comment.client=æä½ç«¯
event.flow.comment.remark=å¤æ³¨
event.flow.comment.source=æææè¿°
event.common.report.field.eventName=äºä»¶åç§°
event.common.report.field.createTime=åå»ºæ¶é´
event.common.report.field.eventTag=äºä»¶æ ç­¾
event.common.report.field.area=åºå
event.common.report.field.gridUnit=ç½æ ¼
event.common.report.field.eventOrigin=äºä»¶æ¥æº
event.common.report.field.square=é¢ç§¯
event.common.report.field.eventStatus=å·¥åç¶æ
event.common.report.field.eventLng=ç»åº¦
event.common.report.field.eventLat=çº¬åº¦
event.common.report.field.locationAddress=äºä»¶ä½ç½®
event.common.report.field.handleTyeName=å¤ç½®æ¹å¼

event.common.report.field.hasEvent=æ¯å¦å­å¨
event.common.report.field.patrolContent=å·¡æ¥åå®¹
event.common.report.field.eventUrl=å·¡æ¥ç§ç
event.common.report.field.eventUrlDisplayName=é¾æ¥ï¼é¢è§å·¡æ¥ç§çï¼

event.common.report.filename=äºä»¶
event.common.unknownAreaName=æªç¥åºå
event.common.return.success=æå
event.data.commit.repeat=äºä»¶ç¦æ­¢éå¤æäº¤
event.bill.pending.tip=åæå¤ç
event.bill.pending.approval=å®¡æ ¸
event.bill.pending.approval.access=å®¡æ ¸éè¿
event.bill.pending.approval.viewing=å®¡æ ¸ä¸­
event.bill.pending.approval.reject=å®¡æ ¸é©³å
event.bill.pending.approval.apply=ç³è¯·
event.bill.common.desc=æè¿°
event.bill.pending.auto.del=èªå¨ä½åº

event.bill.generateBill=å·²çå«
event.bill.generateBillNothing=æªçå«
event.bill.push.success=æ¨éæå
event.bill.push.fail=æ¨éå¤±è´¥
event.bill.push.filename=æ¨éè®°å½
event.bill.push.log.excel.dataId=äºä»¶ç¼å·
event.bill.push.log.excel.billNumber=å·¥åç¼å·
event.bill.push.log.excel.generateBillStatus=çå«ç¶æ
event.bill.push.log.excel.pushState=æ¨éç¶æ
event.bill.push.log.excel.pushMsg=å¤±è´¥åå 
event.bill.push.log.excel.pushTime=æ¨éæ¶é´

