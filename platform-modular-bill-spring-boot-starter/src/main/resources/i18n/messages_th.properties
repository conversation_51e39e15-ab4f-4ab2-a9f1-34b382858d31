event.lec.flow.none.flow=à¸à¸£à¸¸à¸à¸²à¸£à¸°à¸à¸¸à¸­à¸­à¸à¹à¸à¹à¸à¸à¹à¸à¸£à¸°à¸à¸§à¸à¸à¸²à¸£ Flow
event.lec.flow.none.formData=à¸à¸£à¸¸à¸à¸²à¸£à¸°à¸à¸¸à¸à¹à¸­à¸¡à¸¹à¸¥à¹à¸à¸à¸à¸­à¸£à¹à¸¡ formData
event.lec.flow.none.processDefinitionId=à¸à¸²à¸£à¸à¸´à¸¢à¸²à¸¡à¸à¸±à¹à¸à¸à¸­à¸à¸à¸­à¸à¹à¸à¸ªà¸±à¹à¸à¸à¸²à¸à¸à¸±à¸à¸à¸¸à¸à¸±à¸ processDefinitionId%s, id
event.common.tip.none.bizCode=à¹à¸¡à¹à¹à¸à¹à¸£à¸°à¸à¸¸à¸à¸²à¸£à¸²à¸¡à¸´à¹à¸à¸­à¸£à¹ bizCode
event.common.tip.none.processDefinitionId=à¹à¸¡à¹à¹à¸à¹à¸£à¸°à¸¢à¸¸à¸à¸²à¸£à¸²à¸¡à¸´à¹à¸à¸­à¸£à¹ processDefinitionId
event.tip.empty.variables=variables à¸à¸±à¸§à¹à¸à¸£à¸à¸·à¹à¸à¸à¸²à¸à¹à¸¡à¹à¸ªà¸²à¸¡à¸²à¸£à¸ %s
event.lec.gate.none.var=à¸à¸¥à¸à¸²à¸£à¸à¸³à¸à¸§à¸{} à¹à¸à¸·à¹à¸­à¸à¹à¸à¹à¸à¸à¹à¸§à¸¢à¹à¸à¹à¸­à¸à¹à¸¡à¹à¹à¸à¹à¸ %s
event.lec.gate.none.analysis.var=à¸à¸²à¸£à¸§à¸´à¹à¸à¸£à¸²à¸°à¸«à¹à¹à¸à¸´à¸à¹à¸ªà¸à¸ SpEL à¸à¸­à¸{} à¹à¸à¸·à¹à¸­à¸à¹à¸à¹à¸à¸à¹à¸§à¸¢à¹à¸¥à¹à¸¡à¹à¸«à¸¥à¸§ expression{}, {}
event.lec.gate.none.analysis.exp.var=à¹à¸à¸´à¸à¸à¹à¸­à¸à¸´à¸à¸à¸¥à¸²à¸à¸à¸à¸°à¸à¸³à¸à¸§à¸ {} à¹à¸à¸·à¹à¸­à¸à¹à¸à¹à¸à¸à¹à¸§à¸¢à¹ expression{}, baseVariables{} ,{}
event.lec.none.activityPointer=à¸à¸£à¸¸à¸à¸²à¸£à¸°à¸à¸¸à¸à¸·à¹à¸­à¹à¸«à¸à¸ activityId
event.lec.none.activity.detail=à¹à¸¡à¹à¸¡à¸µà¹à¸«à¸à¸à¸­à¸¢à¸¹à¹à¹à¸à¸à¸²à¸£à¸à¸³à¸«à¸à¸à¸à¹à¸²à¸à¸±à¹à¸à¸à¸­à¸={}
event.lec.flow.none.activity.name=à¹à¸¡à¹à¹à¸à¹à¸£à¸°à¸à¸¸à¹à¸«à¸à¸à¸à¸²à¸£à¹à¸à¹à¸à¹à¸à¹à¸²à¹à¸à¹à¸¡à¹à¸à¸à¸²à¸£à¸à¸³à¸«à¸à¸à¸à¹à¸²à¸à¸±à¹à¸à¸à¸­à¸=processDefinitionId{}
event.lec.flow.none.activityState=%s.activityStates à¸à¹à¸­à¸à¹à¸¡à¹à¹à¸à¹à¸ %s
event.lec.flow.none.gatewayCondition=%s.gatewayConditionsà¸à¹à¸­à¸à¹à¸¡à¹à¹à¸à¹à¸ %s
event.lec.flow.configProvider=à¸à¸²à¸£à¸£à¸±à¸à¸à¸±à¸§à¸­à¸¢à¹à¸²à¸IXlmConfigServiceà¸¥à¹à¸¡à¹à¸«à¸¥à¸§
event.lec.flow.variableInfo=%s.variableInfoà¸à¹à¸­à¸à¹à¸¡à¹à¹à¸à¹à¸ %s
event.common.xce=XlmConfigEnvironment
event.common.tip.none.code=code%s
event.common.tip.fail.export.excel=à¸à¸²à¸£à¸ªà¹à¸à¸­à¸­à¸à¸à¸²à¸£à¸²à¸ excel à¸¥à¹à¸¡à¹à¸«à¸¥à¸§!
event.common.tip.none.path=path%s
event.common.tip.none.file.exist=à¹à¸¡à¹à¸¡à¸µà¹à¸à¸¥à¹, path
event.common.tip.fail.file.upload=à¸à¸²à¸£à¸­à¸±à¸à¹à¸«à¸¥à¸à¹à¸à¸¥à¹à¸¥à¹à¸¡à¹à¸«à¸¥à¸§
event.tag.none.metadata=metadata%s
event.tag.none.tagName=flowStatesà¸à¹à¸­à¸à¹à¸¡à¹à¹à¸à¹à¸ %sï¼tagName
event.common.none.comparePictureUrl=à¸à¹à¸­à¸à¸à¸µà¹à¸à¸²à¸à¸«à¸²à¸¢ comparePictureUrl à¹à¸¡à¸·à¹à¸­à¹à¸à¹à¸ compareTypeÂ Â 2
event.common.none.originalDataLogo=à¸à¸²à¸£à¸²à¸¡à¸´à¹à¸à¸­à¸£à¹à¸à¸µà¹à¸à¸²à¸à¸«à¸²à¸¢ à¹à¸à¸£à¸·à¹à¸­à¸à¸«à¸¡à¸²à¸¢à¹à¸à¸µà¸¢à¸à¸«à¸à¸¶à¹à¸à¹à¸à¸µà¸¢à¸§à¸à¸­à¸à¸à¹à¸­à¸¡à¸¹à¸¥à¸à¹à¸à¸à¸à¸±à¸à¸à¹à¸­à¸à¹à¸¡à¹à¹à¸à¹à¸ %s
event.common.none.exist.originalDataLogo=à¸à¹à¸­à¸¡à¸¹à¸¥ %sï¼
event.common.none.del.originalDataLogo=à¸à¸´à¸à¸à¸£à¸£à¸¡à¹à¸à¹à¸à¹à¸¡à¸à¸°à¹à¸¥à¹à¸§:
event.common.none.compareAirlineTaskId=à¹à¸à¸£à¸µà¸¢à¸à¹à¸à¸µà¸¢à¸à¸ à¸²à¸£à¸à¸´à¸à¸à¸²à¸£à¸à¸´à¸ %s
event.common.none.compareTile=à¹à¸à¸£à¸µà¸¢à¸à¹à¸à¸µà¸¢à¸à¹à¸à¸¥à¹ %s
event.common.none.reportTemplate=à¸à¸²à¸£à¸à¹à¸à¸«à¸²à¹à¸à¸¡à¹à¸à¸¥à¸ %s
event.common.none.app=à¸à¸²à¸£à¸à¹à¸à¸«à¸²à¹à¸­à¸à¸à¸¥à¸´à¹à¸à¸à¸±à¸%s
event.common.none.org=à¸à¸²à¸£à¸à¹à¸à¸«à¸²à¸­à¸à¸à¹à¸à¸£%s
event.common.none.query=à¸à¸±à¸à¸à¸¶à¸à¸à¸²à¸£à¸à¹à¸à¸«à¸²%s
event.common.none.translateObj=à¸­à¸­à¸à¹à¸à¹à¸à¸à¹à¸à¸µà¹à¸ªà¹à¸à¸à¹à¸²à¸%s
event.common.fail.optAnalysis=à¸à¸²à¸£à¸§à¸´à¹à¸à¸£à¸²à¸°à¸«à¹à¸à¸²à¸£à¸à¸à¸´à¸à¸±à¸à¸´à¸à¸²à¸£à¸¥à¹à¸¡à¹à¸«à¸¥à¸§, time
event.common.none.key=key%s
event.common.none.data.export=à¹à¸¡à¹à¸¡à¸µà¸à¹à¸­à¸¡à¸¹à¸¥à¹à¸«à¹à¸ªà¹à¸à¸­à¸­à¸
event.flow.fail.activityState=à¸à¸²à¸£à¸£à¸±à¸ XlmActivityStateà¸¥à¹à¸¡à¹à¸«à¸¥à¸§, processDefinitionId{},activityId{}
event.flow.fail.billState=à¸à¸²à¸£à¸£à¸±à¸ WorkBillActivity à¸¥à¹à¸¡à¹à¸«à¸¥à¸§, workBillId{},activityId{}
event.common.fail.update=à¸­à¸±à¸à¹à¸à¸à¸à¸²à¸à¸à¹à¸­à¸¡à¸¹à¸¥à¸¥à¹à¸¡à¹à¸«à¸¥à¸§ à¸à¸£à¸¸à¸à¸²à¸¥à¸­à¸à¸­à¸µà¸à¸à¸£à¸±à¹à¸à¹à¸à¸ à¸²à¸¢à¸«à¸¥à¸±à¸
event.flow.tip.starting=à¸à¸³à¸¥à¸±à¸à¹à¸£à¸´à¹à¸¡à¸ªà¸±à¹à¸à¸à¸²à¸
event.flow.fail.bizCategory=à¸à¸²à¸£à¸£à¸±à¸ XlmBizCategory à¸¥à¹à¸¡à¹à¸«à¸¥à¸§
event.flow.fail.start=à¸à¸²à¸£à¹à¸£à¸´à¹à¸¡à¸à¹à¸à¸à¸£à¸°à¸à¸§à¸à¸à¸²à¸£à¸¥à¹à¸¡à¹à¸«à¸¥à¸§, id{}, failReason{}
event.flow.fail.task.commit=à¸à¸²à¸£à¸ªà¹à¸à¸ à¸²à¸£à¸à¸´à¸à¸¥à¹à¸¡à¹à¸«à¸¥à¸§ï¼processInstanceId{}, message:{}
event.flow.fail.task.exp=à¸à¸²à¸£à¸ªà¹à¸à¸ à¸²à¸£à¸à¸´à¸à¸¥à¹à¸¡à¹à¸«à¸¥à¸§ à¸ à¸²à¸¢à¹à¸ flowableà¹à¸à¸´à¸à¸à¹à¸­à¸à¸´à¸à¸à¸¥à¸²à¸ï¼processInstanceId{}, message:{}
event.flow.none.submit.task=à¸à¸£à¸¸à¸à¸²à¸£à¸°à¸à¸¸ submitTaskDto
event.flow.none.taskId=à¸à¸£à¸¸à¸à¸²à¸£à¸°à¸à¸¸ taskId
event.flow.none.flowId=FlowEntityà¸à¸µà¹à¹à¸¡à¹à¸¡à¸µà¸­à¸¢à¸¹à¹, Id
event.flow.task.none.query=à¹à¸¡à¹à¸à¸à¸ à¸²à¸£à¸à¸´à¸à¸à¸±à¸à¸à¸¸à¸à¸±à¸=taskId{}
event.flow.task.none.commit.repeat=à¸ à¸²à¸£à¸à¸´à¸à¸à¸±à¸à¸à¸¸à¸à¸±à¸à¸ªà¸´à¹à¸à¸ªà¸¸à¸à¸¥à¸à¹à¸¥à¹à¸§ à¹à¸¡à¹à¸ªà¸²à¸¡à¸²à¸£à¸à¸ªà¹à¸à¸à¹à¸³à¹à¸à¹ =TaskId{}
event.common.none.user=à¸à¹à¸­à¸¡à¸¹à¸¥à¸à¸¹à¹à¹à¸à¹ %s
event.common.none.role=à¸à¹à¸­à¸¡à¸¹à¸¥à¸à¸à¸à¸²à¸à¸à¸­à¸à¸à¸¹à¹à¹à¸à¹ %s
event.common.none.flowVal=à¸à¸²à¸£à¸²à¸¡à¸´à¹à¸à¸­à¸£à¹ values%s
event.common.none.flow.variable=à¸à¸²à¸£à¸²à¸¡à¸´à¹à¸à¸­à¸£à¹ variables%s
event.flow.fail.update=à¸à¸²à¸£à¸­à¸±à¸à¹à¸à¸à¸à¹à¸²à¹à¸­à¸à¸à¸£à¸´à¸à¸´à¸§à¸à¹ FlowEntityà¸¥à¹à¸¡à¹à¸«à¸¥à¸§, flowId{}
event.bill.fail.update=à¸­à¸±à¸à¹à¸à¸à¸à¹à¸²à¹à¸­à¸à¸à¸£à¸´à¸à¸´à¸§à¸à¹WorkBillà¸¥à¹à¸¡à¹à¸«à¸¥à¸§, Id{}
event.bill.gis.format.error=à¸à¸´à¸à¸±à¸à¸¥à¸°à¸à¸´à¸à¸¹à¸à¹à¸¥à¸°à¸¥à¸­à¸à¸à¸´à¸à¸¹à¸à¹à¸¡à¹à¸ªà¸­à¸à¸à¸¥à¹à¸­à¸à¸à¸±à¸à¸«à¸¥à¸±à¸à¸à¸²à¸£à¸à¹à¸­à¸à¸à¹à¸­à¸¡à¸¹à¸¥=à¸à¸£à¸¸à¸à¸²à¸à¹à¸­à¸à¸à¸²à¸¡à¸£à¸¹à¸à¹à¸à¸à¸à¹à¸­à¹à¸à¸à¸µà¹ [31.426894,118.070765]
event.bill.grid.locate.error=à¹à¸¡à¹à¸à¸à¸à¸²à¸£à¸²à¸à¸à¸³à¸«à¸à¸à¸à¸³à¹à¸«à¸à¹à¸ à¹à¸¡à¹à¸ªà¸²à¸¡à¸²à¸£à¸à¸à¸³à¸«à¸à¸à¸à¸³à¹à¸«à¸à¹à¸à¹à¸à¹
event.bill.grid.none.change=à¸à¸²à¸£à¸²à¸à¹à¸à¸ªà¸±à¹à¸à¸à¸²à¸à¹à¸¡à¹à¸¡à¸µà¸à¸²à¸£à¹à¸à¸¥à¸µà¹à¸¢à¸à¹à¸à¸¥à¸
event.bill.none.exist=à¹à¸¡à¹à¸¡à¸µà¸à¸´à¸à¸à¸£à¸£à¸¡
event.annotation.point.exist=à¸¡à¸µ %sà¸­à¸¢à¸¹à¹à¹à¸¥à¹à¸§
event.annotation.point.none.exist=à¹à¸¡à¹à¸¡à¸µ %s
event.annotation.point.fail.exist=à¸à¸²à¸£à¸¥à¸ %s à¸¥à¹à¸¡à¹à¸«à¸¥à¸§
event.label.none.type=à¸à¸£à¸°à¹à¸ à¸ %s
event.grid.none.change.right=à¸à¸²à¸£à¹à¸«à¹à¸ªà¸´à¸à¸à¸´à¹à¹à¸à¹à¸à¸¹à¹à¹à¸à¹à¸à¸²à¸£à¸²à¸à¹à¸¡à¹à¸¡à¸µà¸à¸²à¸£à¹à¸à¸¥à¸µà¹à¸¢à¸à¹à¸à¸¥à¸ à¸à¸£à¸¸à¸à¸²à¸¢à¸·à¸à¸¢à¸±à¸à¹à¸«à¸¡à¹à¸­à¸µà¸à¸à¸£à¸±à¹à¸
event.rang.name.duplication=à¸à¸·à¹à¸­à¸à¹à¸³
event.range.empty.file=à¹à¸à¸¥à¹ %s
event.range.error.file.format=à¸£à¸¹à¸à¹à¸à¸à¹à¸à¸¥à¹à¹à¸¡à¹à¸à¸¹à¸à¸à¹à¸­à¸
event.bill.file.check=à¹à¸¡à¹à¹à¸à¹à¸à¸±à¹à¸à¸à¹à¸²properties, geometry
event.sync.task.repeat.exe=à¸ à¸²à¸£à¸à¸´à¸à¸à¸±à¸à¸à¸¸à¸à¸±à¸à¸à¸³à¸¥à¸±à¸à¸à¸³à¹à¸à¸´à¸à¸à¸²à¸£ à¹à¸à¸£à¸à¸¥à¸­à¸à¸­à¸µà¸à¸à¸£à¸±à¹à¸à¹à¸à¸ à¸²à¸¢à¸«à¸¥à¸±à¸
event.sync.none.id=à¸à¸µà¸¢à¹à¸«à¸¥à¸±à¸à¸à¹à¸­à¸à¹à¸¡à¹à¹à¸à¹à¸ %s
event.sync.none.state=syncState%s
event.sync.none.context=applicationContext%s
event.fail.generate.signature=à¸à¸²à¸£à¸ªà¸£à¹à¸²à¸à¸¥à¸²à¸¢à¹à¸à¹à¸à¸¥à¹à¸¡à¹à¸«à¸¥à¸§
event.fail.grid.delNoneSupport=à¹à¸¡à¹à¸ªà¸²à¸¡à¸²à¸£à¸à¸¥à¸à¸à¸²à¸£à¸²à¸à¸à¸µà¹à¹à¸à¹ à¹à¸à¸·à¹à¸­à¸à¸à¸²à¸à¸¡à¸µà¸à¸²à¸£à¸à¸£à¸°à¸¡à¸§à¸¥à¸à¸¥à¹à¸à¸ªà¸±à¹à¸à¸à¸²à¸à¹à¸à¸à¸²à¸£à¸²à¸
event.fail.grid.del.child=à¹à¸¡à¹à¸ªà¸²à¸¡à¸²à¸£à¸à¸¥à¸à¸à¸²à¸£à¸²à¸à¸à¸µà¹à¹à¸à¹ à¹à¸à¸·à¹à¸­à¸à¸à¸²à¸à¸¡à¸µà¸à¸²à¸£à¸²à¸à¸£à¸°à¸à¸±à¸à¸à¹à¸³à¸à¸§à¹à¸²à¸­à¸¢à¸¹à¹à¹à¸à¸à¸²à¸£à¸²à¸à¸à¸µà¹
event.grid.config.user=à¸à¸³à¸«à¸à¸à¸à¹à¸²à¸à¸à¸±à¸à¸à¸²à¸à¹à¸à¸à¸²à¸£à¸²à¸à¸ªà¸³à¹à¸£à¹à¸
event.grid.none.unit.name=à¸à¸£à¸¸à¸à¸²à¸à¸£à¸­à¸à¸à¸·à¹à¸­à¸à¸²à¸£à¸²à¸
event.grid.empty.original.logo=à¹à¸à¸£à¸·à¹à¸­à¸à¸«à¸¡à¸²à¸¢à¹à¸à¸µà¸¢à¸à¸«à¸à¸¶à¹à¸à¹à¸à¸µà¸¢à¸§à¸à¸­à¸à¸à¹à¸­à¸¡à¸¹à¸¥à¸à¹à¸à¸à¸à¸±à¸à¸à¹à¸­à¸à¹à¸¡à¹à¸ªà¸à¸£à¸´à¸à¸à¸µà¹à¸¡à¸µ %s à¸à¸±à¸§
event.common.empty=à¸§à¹à¸²à¸à¹à¸à¸¥à¹à¸²
event.common.required=à¸à¸³à¹à¸à¹à¸à¸à¹à¸­à¸à¸à¸£à¸­à¸
event.common.marker.point=à¸à¸³à¹à¸«à¸à¹à¸à¸à¸³à¸­à¸à¸´à¸à¸²à¸¢
event.flow.patrol=à¸à¸²à¸£à¸à¸£à¸§à¸à¸ªà¸­à¸à¸à¸µà¹à¸£à¸­
event.flow.handle=à¸à¸²à¸£à¸à¸³à¹à¸à¸´à¸à¸à¸²à¸£
event.flow.judging=à¸à¸²à¸£à¸à¸£à¸§à¸à¸ªà¸­à¸
event.flow.archived=à¹à¸à¹à¸à¸à¸²à¸§à¸£
event.flow.person=à¸à¸¸à¸à¸¥à¸²à¸à¸£
event.flow.handleType=à¸§à¸´à¸à¸µà¸à¸²à¸£à¸à¸³à¹à¸à¸´à¸à¸à¸²à¸£
event.flow.check=à¸à¸¥à¸à¸²à¸£à¸à¸£à¸§à¸à¸ªà¸­à¸
event.flow.handleqk=à¸ªà¸à¸²à¸à¸à¸²à¸£à¸à¹à¸à¸²à¸£à¸à¸³à¹à¸à¸´à¸à¸à¸²à¸£
event.flow.event_has=à¸¡à¸µà¹à¸«à¸à¸¸à¸à¸²à¸£à¸à¹
event.flow.yes=à¹à¸à¹
event.flow.no=à¹à¸¡à¹à¸¡à¸µ
event.flow.inspection_situation=à¸ªà¸à¸²à¸à¸à¸²à¸£à¸à¹à¸à¸²à¸£à¸à¸£à¸§à¸à¸ªà¸­à¸
event.flow.reason_rejection=à¹à¸«à¸à¸¸à¸à¸¥à¸à¸µà¹à¸à¸à¸´à¹à¸ªà¸
event.flow.extend_date=à¸§à¸±à¸à¸à¸µà¹à¸à¸¢à¸²à¸¢
event.flow.not_disposed=à¸¢à¸±à¸à¹à¸¡à¹à¹à¸à¹à¸à¸³à¹à¸à¸´à¸à¸à¸²à¸£
event.flow.no_need_disposal=à¹à¸¡à¹à¸à¸³à¹à¸à¹à¸à¸à¹à¸­à¸à¸à¸³à¹à¸à¸´à¸à¸à¸²à¸£
event.flow.has_disposal=à¸à¸³à¹à¸à¸´à¸à¸à¸²à¸£à¹à¸¥à¹à¸§
event.flow.later_disposal=à¸à¸²à¸£à¸à¸³à¹à¸à¸´à¸à¸à¸²à¸£à¹à¸à¸ à¸²à¸¢à¸«à¸¥à¸±à¸
event.flow.reject=reject
event.flow.pass=pass
event.common.distance.unit.meter=à¸¡.
event.common.distance.unit.kilometer=à¸à¸¡.
event.common.date.monday=à¸.
event.common.date.tuesday=à¸­.
event.common.date.wednesday=à¸.
event.common.date.thursday=à¸à¸¤.
event.common.date.friday=à¸¨.
event.common.date.saturday=à¸ª.
event.common.date.sunday=à¸­à¸².
event.common.date.january=à¸¡.à¸.
event.common.date.february=à¸.à¸.
event.common.date.march=à¸¡à¸µ.à¸.
event.common.date.april=à¹à¸¡.à¸¢.
event.common.date.may=à¸.à¸.
event.common.date.june=à¸¡à¸´.à¸¢.
event.common.date.july=à¸.à¸.
event.common.date.august=à¸ª.à¸.
event.common.date.september=à¸.à¸¢.
event.common.date.october=à¸.à¸.
event.common.date.november=à¸.à¸¢.
event.common.date.december=à¸.à¸.
event.flow.comment.client=à¸à¸¸à¸à¸à¸²à¸£à¸à¸³à¸à¸²à¸
event.flow.comment.remark=à¸à¹à¸­ chÃº Ã½
event.flow.comment.source=ê¸°ì à¸à¸µà¹à¹à¸à¸à¸§à¸±à¸ªà¸à¸¸
event.common.report.field.eventName=à¸à¸·à¹à¸­à¹à¸«à¸à¸¸à¸à¸²à¸£à¸à¹
event.common.report.field.createTime=à¸§à¸±à¸à¹à¸§à¸¥à¸²à¸ªà¸£à¹à¸²à¸
event.common.report.field.eventTag=à¸à¹à¸²à¸¢à¸à¸µà¹à¹à¸à¹à¸²à¹à¸«à¸à¸¸à¸à¸²à¸£à¸à¹
event.common.report.field.area=à¸à¸·à¹à¸à¸à¸µà¹
event.common.report.field.gridUnit=à¸à¸²à¸à¹à¸²à¸¢
event.common.report.field.eventOrigin=à¹à¸«à¸¥à¹à¸à¸à¸µà¹à¸¡à¸²à¸à¸­à¸à¹à¸«à¸à¸¸à¸à¸²à¸£à¸à¹
event.common.report.field.square=à¸à¸à¸²à¸à¸à¸·à¹à¸à¸à¸µà¹
event.common.report.field.eventStatus=à¸ªà¸à¸²à¸à¸°à¹à¸à¸à¸²à¸
event.common.report.field.eventLng=à¹à¸ªà¹à¸Ð´Ð¾Ð»É¡à¸´ 
event.common.report.field.eventLat=à¸¥à¸°à¸à¸´à¸à¸¹à¸ 
event.common.report.field.locationAddress=à¸ªà¸à¸²à¸à¸à¸µà¹à¹à¸à¸´à¸à¹à¸«à¸à¸¸
event.common.report.field.handleTyeName=à¸§à¸´à¸à¸µà¸à¸²à¸£à¸à¸³à¸à¸±à¸
event.common.report.filename=à¹à¸«à¸à¸¸à¸à¸²à¸£à¸à¹
event.common.return.success=à¸à¸§à¸²à¸¡à¸ªà¸³à¹à¸£à¹à¸
event.flow.pushing=à¹à¸à¸·à¹à¸­à¸à¸¥à¸±à¸à¸à¸±à¸
event.flow.handing=à¸à¸²à¸£à¸à¸³à¹à¸à¸´à¸à¸à¸²à¸£
event.common.unknownAreaName=à¹à¸¡à¹à¸à¸£à¸²à¸à¸à¸·à¹à¸à¸à¸µà¹
event.flow.deal_result=à¸à¸¥à¸à¸²à¸£à¸à¸³à¹à¸à¸´à¸à¸à¸²à¸£
event.flow.deal_complete=à¸à¸²à¸£à¸à¸³à¹à¸à¸´à¸à¸à¸²à¸£à¹à¸ªà¸£à¹à¸à¸ªà¸¡à¸à¸¹à¸£à¸à¹
event.flow.no_push=à¹à¸¡à¹à¸¡à¸µà¸à¸²à¸£à¸à¸¥à¸±à¸à¸à¸±à¸
event.common.report.field.hasEvent=à¸¡à¸µà¸­à¸¢à¸¹à¹à¸«à¸£à¸·à¸­à¹à¸¡à¹
event.common.report.field.patrolContent=à¹à¸à¸·à¹à¸­à¸«à¸²à¸à¸²à¸£à¸à¸£à¸§à¸à¸à¸£à¸²
event.common.report.field.eventUrl=à¸£à¸¹à¸à¸ à¸²à¸à¸à¸²à¸£à¸à¸£à¸§à¸
event.common.report.field.eventUrlDisplayName=à¸¥à¸´à¸à¸à¹ (à¸à¸¹à¸à¸±à¸§à¸­à¸¢à¹à¸²à¸à¸£à¸¹à¸à¸ à¸²à¸à¸à¸²à¸£à¸¥à¸²à¸à¸à¸£à¸°à¹à¸§à¸)
event.data.commit.repeat=à¸à¸²à¸£à¸«à¹à¸²à¸¡à¸ªà¹à¸à¹à¸«à¸à¸¸à¸à¸²à¸£à¸à¹à¸à¹à¸³
event.bill.pending.tip=à¸à¸²à¸£à¸à¸£à¸°à¸¡à¸§à¸¥à¸à¸¥à¸«à¸¥à¸±à¸
event.bill.pending.approval=à¸à¸²à¸£à¸à¸£à¸§à¸à¸ªà¸­à¸
event.bill.pending.approval.access=à¸­à¸à¸¸à¸¡à¸±à¸à¸´à¹à¸¥à¹à¸§
event.bill.pending.approval.viewing=à¸à¸³à¸¥à¸±à¸à¸à¸£à¸§à¸à¸ªà¸­à¸
event.bill.pending.approval.reject=à¸à¸¹à¸à¸à¸à¸´à¹à¸ªà¸
event.bill.pending.approval.apply=à¸à¸²à¸£à¸à¸­à¸­à¸à¸¸à¸à¸²à¸ à¸«à¸£à¸·à¸­ à¸à¸³à¸£à¹à¸­à¸à¸à¸­
event.bill.common.desc=à¸à¸³à¸­à¸à¸´à¸à¸²à¸¢
event.bill.pending.auto.del=à¸ªà¸¹à¸à¹à¸ªà¸µà¸¢à¸à¸¥à¹à¸à¸¢à¸­à¸±à¸à¹à¸à¸¡à¸±à¸à¸´

event.bill.generateBill=à¹à¸à¹à¸£à¸±à¸à¸à¸²à¸£à¸à¸£à¸§à¸à¸ªà¸­à¸à¹à¸¥à¹à¸§
event.bill.generateBillNothing=à¸¢à¸±à¸à¹à¸¡à¹à¹à¸à¹à¸£à¸±à¸à¸à¸²à¸£à¸à¸£à¸§à¸à¸ªà¸­à¸
event.bill.push.success=à¸ªà¹à¸à¸à¹à¸­à¸¡à¸¹à¸¥à¸ªà¸³à¹à¸£à¹à¸
event.bill.push.fail=à¸ªà¹à¸à¸à¹à¸­à¸¡à¸¹à¸¥à¸¥à¹à¸¡à¹à¸«à¸¥à¸§
event.bill.push.filename=à¸à¸±à¸à¸à¸¶à¸à¸à¸²à¸£à¸ªà¹à¸à¸à¹à¸­à¸¡à¸¹à¸¥
event.bill.push.log.excel.dataId=à¸«à¸¡à¸²à¸¢à¹à¸¥à¸à¹à¸«à¸à¸¸à¸à¸²à¸£à¸à¹
event.bill.push.log.excel.billNumber=à¸«à¸¡à¸²à¸¢à¹à¸¥à¸à¸à¸³à¸à¸­à¸à¸³à¸à¸²à¸
event.bill.push.log.excel.generateBillStatus=à¸ªà¸à¸²à¸à¸°à¸à¸²à¸£à¸à¸£à¸§à¸à¸ªà¸­à¸
event.bill.push.log.excel.pushState=à¸ªà¸à¸²à¸à¸°à¸à¸²à¸£à¸ªà¹à¸à¸à¹à¸­à¸¡à¸¹à¸¥
event.bill.push.log.excel.pushMsg=à¸ªà¸²à¹à¸«à¸à¸¸à¸à¸­à¸à¸à¸²à¸£à¸¥à¹à¸¡à¹à¸«à¸¥à¸§
event.bill.push.log.excel.pushTime=à¹à¸§à¸¥à¸²à¸à¸²à¸£à¸ªà¹à¸à¸à¹à¸­à¸¡à¸¹à¸¥