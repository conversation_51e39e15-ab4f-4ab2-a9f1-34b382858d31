server:
  port: 8080
spring:
  flyway:
    # 是否启用
    enabled: true
    # SQL脚本路径
    locations: classpath:db/migration/mysql
    # 当迁移时发现目标schema非空，而且带有没有元数据的表时，是否自动执行基准迁移
    baseline-on-migrate: true
    # 是否允许无序的迁移 开发环境最好开启, 生产环境关闭
    out-of-order: true
    # 关闭占位符替换，因为插入的sql里边可能包含${}关键字
    placeholder-replacement: false
    baseline-version: 1.0.0

  application:
    name: lup-event-bill-server
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候回抛出异常,不启动会使用默认数据源.
      datasource:
        master:
          type: com.alibaba.druid.pool.DruidDataSource
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ${liangma.datasource.bill.url}
          username: ${liangma.datasource.bill.username}
          password: ${liangma.datasource.bill.password}

mybatis-plus:
  mapper-locations: classpath*:/mapper/*Mapper.xml
  typeAliasesPackage: com.mascj.lup.event.bill.entity
liangma:
  swagger: true
  tenant:
    ignore-tables:
      - "yongan_category"
