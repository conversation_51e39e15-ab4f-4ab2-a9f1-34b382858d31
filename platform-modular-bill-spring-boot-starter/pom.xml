<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>lup-event-workbill</artifactId>
        <groupId>com.mascj</groupId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>
    
    <groupId>com.mascj</groupId>
    <artifactId>lup-event-bill-server</artifactId>

    <version>2.0.0-SNAPSHOT</version>


    <dependencies>
        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>liangma-kernel-flyway</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>liangma-kernel-jetcache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>liangma-kernel-log</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.baomidou/dynamic-datasource-spring-boot-starter -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.4.1</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.belerweb/pinyin4j -->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>liangma-kernel-tools</artifactId>
            <version>${liangma.kernel.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mascj</groupId>
            <artifactId>platform-modular-bill-rest</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>