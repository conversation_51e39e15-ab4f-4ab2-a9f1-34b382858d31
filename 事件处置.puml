@startuml 事件推送表关系类图

' 核心实体类
class LupData<事件数据> {
  + id: bigint
  + tenant_id: bigint
  + project_id: bigint
  + data_origin: int
  + original_data_logo: string
  + happened_time: string
  + data_type: int
  + compare_type: int
  + locate_area: decimal
  + shape: string
  + location_lng: decimal
  + location_lat: decimal
  + current_batch_id: bigint
  + compare_batch_id: bigint
  + fly_task_id: bigint
  + device_name: string
  + push_state: int
  + address: string
  + event_desc: string
  + generate_bill: int
  + createBill()
  + addLabel()
  + addDataSource()
  + pushData()
}

class LupBill<工单> {
  + id: bigint
  + tenant_id: bigint
  + project_id: bigint
  + data_id: bigint
  + grid_unit_id: bigint
  + grid_code: string
  + grid_name: string
  + area_code: string
  + area_name: string
  + state: int
  + flow_entity_id: bigint
  + bill_number: string
  + extend_data: string
  + pending_state: int
  + addUsableRecord()
  + addPendingRecord()
  + addNotification()
}

class LupGridUnit<网格> {
  + id: bigint
  + name: string
  + code: string
  + pid: bigint
  + hierarchy: string
  + polygon: string
  + geometry_str: string
  + grid_area: decimal
  + description: string
  + status: boolean
  + sort: int
  + tenant_id: bigint
  + project_id: bigint
}

class LupFlowEntity<流程> {
  + id: bigint
  + process_definition_id: string
  + process_instance_id: string
  + process_task_id: string
  + biz_code: string
  + flow_state: int
  + four_success: int
  + archived_time: datetime
  + processing_duration: bigint
  + tenant_id: bigint
  + ownership: string
  + handle_type: int
  + addTimeOutRecord()
}

' 关联类
class LupDataLabel<事件标签> {
  + id: bigint
  + data_id: bigint
  + label_id: bigint
  + tenant_id: bigint
}

class LupDataSource<数据源> {
  + id: bigint
  + data_id: bigint
  + source_id: bigint
  + tenant_id: bigint
}

class LupBillUsable<可用性> {
  + id: bigint
  + bill_id: bigint
  + usable: int
  + reason: string
  + tenant_id: bigint
}

class LupBillPending<挂起工单> {
  + id: bigint
  + bill_id: bigint
  + process_task_id: string
  + apply_reason: string
  + approval_user_id: bigint
  + approval_state: int
  + approval_time: datetime
  + reject_reason: string
  + tenant_id: bigint
  + addFile()
}

class LupBillPendingFile<挂起工单文件> {
  + id: bigint
  + pending_id: bigint
  + file_id: bigint
  + tenant_id: bigint
}

class LupFlowEntityTimeOut<超时流程> {
  + id: bigint
  + flow_entity_id: bigint
  + process_task_id: string
  + process_start_time: datetime
  + dead_line: datetime
  + tenant_id: bigint
}

class LupDataPushLog<三方事件推送日志> {
  + id: bigint
  + data_id: bigint
  + push_state: int
  + push_msg: string
  + push_stage: string
  + push_mode: int
  + push_auto: int
  + push_event_flow: string
  + data_origin: int
  + bill_number: string
  + bill_id: bigint
  + generate_bill: int
  + push_time: datetime
  + pushable: int
  + tenant_id: bigint
}

class LupBillNotification<工单通知> {
  + id: bigint
  + bill_id: bigint
  + user_id: bigint
  + notification_type: string
  + content: string
  + read_status: int
  + send_time: datetime
  + tenant_id: bigint
}

class LupLabel<标签> {
  + id: bigint
  + tenant_id: bigint
  + project_id: bigint
  + name: string
  + event_type_id: bigint
  + event_type_code: string
}

class LupBatch<批次> {
  + id: bigint
  + tenant_id: bigint
  + batch_name: string
  + batch_number: string
  + batch_type: int
  + description: string
}

class LupSource<数据资源> {
  + id: bigint
  + source_name: string
  + source_type: int
  + fly_achievement_id: bigint
  + original_tile_source_id: bigint
  + org_id: bigint
  + tenant_id: bigint
}

' 关联关系
LupData "1" -- "0..*" LupBill : "生成工单"
LupData "1" -- "0..*" LupDataLabel : "事件标签"
LupData "1" -- "0..*" LupDataSource : "数据源关联"
LupData "1" -- "0..*" LupDataPushLog : "推送记录"
LupData "1" -- "1" LupBatch : "当前批次"
LupData "1" -- "0..1" LupBatch : "对比批次"
LupBill "0..*" -- "1" LupGridUnit : "所属网格"
LupBill "1" -- "0..*" LupBillUsable : "可用性记录"
LupBill "1" -- "0..*" LupBillPending : "挂起记录"
LupBill "1" -- "0..*" LupBillNotification : "通知记录"
LupBill "0..1" -- "1" LupFlowEntity : "关联流程"
LupDataLabel "1" -- "1" LupLabel : "标签"
LupDataSource "1" -- "1" LupSource : "数据源"
LupGridUnit "0..1" -- "0..*" LupGridUnit : "父子网格"
LupFlowEntity "1" -- "0..*" LupFlowEntityTimeOut : "超时记录"
LupBillPending "1" -- "0..*" LupBillPendingFile : "附件文件"

@enduml