#FROM registry.mascj.com/xiaoliangma/openjdk11:centos8.4.210
FROM registry.mascj.com/adoptopenjdk/openjdk11:2025-03-26-1


#RUN yum install -y kde-l10n-Chinese && \
#    localedef -c -f UTF-8 -i zh_CN zh_CN.utf8 && \
#    echo 'LANG="zh_CN.UTF-8"' > /etc/locale.conf && \
#    yum clean all


ENV project="lup-event-bill-server"

ENV spring.application.name=$project

ENV JAVA_OPTS="-Xms512m -Xmx512m -server --add-exports java.desktop/sun.font=ALL-UNNAMED  -Djava.security.egd=file:/dev/./urandom"

COPY Fangsong.ttf /usr/share/fonts

# Set the environment variables to UTF-8
#RUN locale-gen zh_CN.UTF-8
#ENV LANG="zh_CN.UTF-8" \
#    LC_ALL="zh_CN.UTF-8"

ENV TZ=Asia/Shanghai

RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

EXPOSE 8080


ADD ./platform-modular-bill-spring-boot-starter/target/$project.jar /opt/app

ENTRYPOINT java -jar /opt/app/$project.jar   $JAVA_OPTS
