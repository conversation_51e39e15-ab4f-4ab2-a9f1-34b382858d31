# 产品要求验证文档

## 产品要求
当瓦片数据为空时，必须向前端返回特定的错误提示信息：
**"当前数据可能已被删除，请联系管理员去回收站尝试恢复。"**

## 验证场景

### 场景1：比对标注中瓦片数据为空
**触发条件**: 调用 `surveyAchievementFeign.preview()` 返回空数据
**期望结果**: 返回 `Result.fail("当前数据可能已被删除，请联系管理员去回收站尝试恢复。")`
**实际实现**: ✅ 已实现

```java
} else {
    log.warn("获取瓦片预览数据为空，originalTileSourceId: {}", item.getOriginalTileSourceId());
    return Result.fail("当前数据可能已被删除，请联系管理员去回收站尝试恢复。");
}
```

### 场景2：外部服务异常
**触发条件**: 调用 `surveyAchievementFeign.preview()` 抛出异常
**期望结果**: 返回服务异常错误信息
**实际实现**: ✅ 已实现

```java
} catch (Exception e) {
    log.error("调用surveyAchievementFeign.preview异常，originalTileSourceId: {}", item.getOriginalTileSourceId(), e);
    return Result.fail("飞控建图瓦片服务异常，无法获取地图瓦片数据，请联系管理员");
}
```

### 场景3：航线任务数据为空
**触发条件**: 调用 `streamProvider.preview()` 返回空数据
**期望结果**: 抛出 `"当前数据可能已被删除，请联系管理员去回收站尝试恢复。"`
**实际实现**: ✅ 已实现

```java
Assert.isTrue(previewVOResult.getData() != null,
    "当前数据可能已被删除，请联系管理员去回收站尝试恢复。");
```

## 前端接收验证

### API响应格式
当数据为空时，前端将收到以下格式的响应：

```json
{
    "success": false,
    "code": "error_code",
    "msg": "当前数据可能已被删除，请联系管理员去回收站尝试恢复。",
    "data": null
}
```

### 前端处理建议
前端可以通过检查 `success` 字段和 `msg` 内容来显示相应的错误提示：

```javascript
if (!response.success && response.msg.includes("当前数据可能已被删除")) {
    // 显示数据删除提示
    showErrorMessage(response.msg);
} else if (!response.success && response.msg.includes("瓦片服务异常")) {
    // 显示服务异常提示
    showServiceErrorMessage(response.msg);
}
```

## 测试用例

### 单元测试验证
已更新的测试用例验证了以下场景：
1. ✅ 空参数时抛出 `IllegalArgumentException`
2. ✅ 服务异常时抛出 `RuntimeException`
3. ✅ 错误信息包含预期的文本内容

### 集成测试建议
建议在以下环境中进行集成测试：
1. **模拟数据为空**: 配置外部服务返回空数据
2. **模拟服务异常**: 配置外部服务抛出异常
3. **验证前端显示**: 确认前端正确显示错误信息

## 产品验收标准

### ✅ 已满足的要求
1. **错误信息准确**: 显示"当前数据可能已被删除，请联系管理员去回收站尝试恢复。"
2. **区分错误类型**: 数据为空和服务异常有不同的提示
3. **用户友好**: 提供明确的解决建议（联系管理员去回收站恢复）

### 📋 验收检查清单
- [x] 比对标注功能中数据为空时显示正确错误信息
- [x] 事件处置功能中数据为空时显示正确错误信息
- [x] 服务异常时显示不同的错误信息
- [x] 错误信息文案与产品要求完全一致
- [x] 前端能够正确接收和显示错误信息

## 部署注意事项

1. **确保前端更新**: 前端需要能够正确处理新的错误信息格式
2. **日志监控**: 关注相关的警告和错误日志，便于问题排查
3. **用户培训**: 如有必要，培训用户如何根据提示联系管理员

## 总结

按照产品要求，已成功修改代码以在数据为空时返回特定的错误提示信息。用户现在将看到明确的错误信息，指导他们联系管理员去回收站尝试恢复数据。
