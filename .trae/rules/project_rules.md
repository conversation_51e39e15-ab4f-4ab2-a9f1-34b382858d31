实体类位置是platform-modular-bill-api/src/main/java/com/mascj/lup/event/bill/entity
实体类参考LupData
使用的是mybatis-plus

mapper的位置是platform-modular-bill-logic/src/main/java/com/mascj/lup/event/bill/mapper

xlm的位置是platform-modular-bill-logic/src/main/resources/mapper

service的位置是platform-modular-bill-logic/src/main/java/com/mascj/lup/event/bill/service
serviceImpl的位置是platform-modular-bill-logic/src/main/java/com/mascj/lup/event/bill/service/impl 

ntroller的位置是platform-modular-bill-rest/src/main/java/com/mascj/lup/event/bill/controller

## 技术栈
- Java Spring Boot
- MySQL数据库
- MyBatis ORM框架
- Flyway数据库迁移工具
- Lombok代码简化
- Swagger API文档

## 核心模块
1. platform-modular-bill-api: 定义DTO、VO和常量
2. platform-modular-bill-logic: 核心业务逻辑实现
3. platform-modular-bill-rest: RESTful接口层
4. platform-modular-bill-spring-boot-starter: 启动模块