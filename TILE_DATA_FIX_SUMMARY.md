# 瓦片数据获取错误提示 - 按产品要求修复总结

## 问题描述

产品要求：当瓦片数据为空时，必须向前端返回特定的错误提示信息：
**"当前数据可能已被删除，请联系管理员去回收站尝试恢复。"**

## 产品要求

根据产品反馈，必须在数据为空时显示特定的错误提示信息，而不是跳过或优雅降级。

### 1. 修改 `LupSourceServiceImpl.java`

#### 主要变更：
- **恢复错误提示**: 当瓦片数据为空时，返回 `Result.fail("当前数据可能已被删除，请联系管理员去回收站尝试恢复。")`
- **服务异常提示**: 当外部服务异常时，返回 `Result.fail("飞控建图瓦片服务异常，无法获取地图瓦片数据，请联系管理员")`
- **Assert异常处理**: 在 `fetchTileOptions` 方法中使用 `Assert.isTrue` 确保数据完整性

#### 核心逻辑：
```java
try {
    CommonResult commonResult = surveyAchievementFeign.preview(item.getOriginalTileSourceId());
    log.info("commonResult:"+JSONUtil.toJsonStr(commonResult));
    if(commonResult != null && commonResult.getData() != null && !"".equals(commonResult.getData())) {
        AchievementClientPreviewVO achievementPreviewVO = BeanUtil.toBean(commonResult.getData(), AchievementClientPreviewVO.class);
        geoTile.setTileUrl(achievementPreviewVO.getPreviewUrl());
        geoTile.setFlipFlag(achievementPreviewVO.getFlipFlag());
    } else {
        log.warn("获取瓦片预览数据为空，originalTileSourceId: {}", item.getOriginalTileSourceId());
        return Result.fail("当前数据可能已被删除，请联系管理员去回收站尝试恢复。");
    }
} catch (Exception e) {
    log.error("调用surveyAchievementFeign.preview异常，originalTileSourceId: {}", item.getOriginalTileSourceId(), e);
    return Result.fail("飞控建图瓦片服务异常，无法获取地图瓦片数据，请联系管理员");
}
```

#### `fetchTileOptions` 方法修改：
- **恢复Assert验证**: 使用 `Assert.isTrue` 进行参数和数据验证
- **明确错误信息**: 当数据为空时抛出 `"当前数据可能已被删除，请联系管理员去回收站尝试恢复。"`
- **服务异常处理**: 抛出包含详细信息的 `RuntimeException`

```java
@Override
public AchievementClientPreviewVO fetchTileOptions(Long airlineTaskId){
    Assert.isTrue(airlineTaskId != null, "航线任务ID不能为空");

    try {
        Result<AchievementClientPreviewVO> previewVOResult = streamProvider.preview(airlineTaskId);

        Assert.isTrue(previewVOResult != null,
            "航线任务瓦片数据查询失败，请检查任务是否存在");

        Assert.isTrue(previewVOResult.getData() != null,
            "当前数据可能已被删除，请联系管理员去回收站尝试恢复。");

        return previewVOResult.getData();

    } catch (Exception e) {
        log.error("航线任务瓦片数据查询异常，航线任务ID：{}", airlineTaskId, e);
        throw new RuntimeException(
            "航线任务瓦片数据服务异常，无法获取地图数据，请联系管理员检查服务状态", e);
    }
}
```

### 2. 添加单元测试

创建了 `LupSourceServiceImplTest.java` 来验证：
- 空参数处理
- 异常情况的优雅降级
- 外部服务调用的正确性

## 修改效果

### ✅ 满足产品要求：
1. **明确错误提示**: 当瓦片数据为空时，向前端返回明确的错误信息
2. **用户友好提示**: 显示"当前数据可能已被删除，请联系管理员去回收站尝试恢复。"
3. **区分错误类型**: 数据为空和服务异常有不同的错误提示

### 📊 执行流程：
1. **遍历所有瓦片数据源**
2. **对每个数据源**:
   - 尝试获取瓦片预览数据
   - 如果成功 → 添加到结果列表
   - 如果数据为空 → 返回 `Result.fail("当前数据可能已被删除，请联系管理员去回收站尝试恢复。")`
   - 如果服务异常 → 返回 `Result.fail("飞控建图瓦片服务异常，无法获取地图瓦片数据，请联系管理员")`

### 🔍 错误处理策略：
- **数据为空**: 返回特定错误信息，中断流程
- **服务异常**: 返回服务异常错误信息，中断流程
- **参数验证**: 使用 Assert 进行严格的参数验证

## 建议的后续优化

1. **添加重试机制**: 对于网络异常可以考虑重试
2. **缓存机制**: 对成功获取的瓦片数据进行缓存
3. **监控告警**: 对频繁失败的数据源进行监控和告警
4. **用户提示**: 在前端适当位置提示用户某些图层数据暂时不可用

## 文件清单

### 修改的文件：
- `platform-modular-bill-logic/src/main/java/com/mascj/lup/event/bill/service/impl/LupSourceServiceImpl.java`

### 新增的文件：
- `platform-modular-bill-logic/src/test/java/com/mascj/lup/event/bill/service/impl/LupSourceServiceImplTest.java`

## 测试验证

建议在以下场景下进行测试：
1. 正常瓦片数据加载
2. 部分瓦片数据缺失的情况
3. 外部服务异常的情况
4. 网络连接问题的情况

通过这些修改，系统现在能够优雅地处理瓦片数据获取失败的情况，提供更好的用户体验和系统稳定性。
